// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// OncallAlertsColumns holds the columns for the "oncall_alerts" table.
	OncallAlertsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID, Comment: "UUID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Create Time | 创建日期"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Update Time | 修改日期"},
		{Name: "raw_alert_ref_id", Type: field.TypeString, Nullable: true, Comment: "原始告警ID"},
		{Name: "title", Type: field.TypeString, Comment: "告警标题"},
		{Name: "description", Type: field.TypeString, Nullable: true, Comment: "描述"},
		{Name: "alert_severity", Type: field.TypeEnum, Comment: "告警严重程度", Enums: []string{"Info", "Warning", "Critical"}},
		{Name: "alert_status", Type: field.TypeEnum, Comment: "告警状态", Enums: []string{"Info", "Warning", "Critical", "Ok"}},
		{Name: "progress", Type: field.TypeEnum, Comment: "处理进度", Enums: []string{"Triggered", "Processing", "Closed"}},
		{Name: "alert_key", Type: field.TypeString, Comment: "拥有相同alert_key的告警事件，将合并为同一个告警即为一条故障消息通知（原始告警有聚合）"},
		{Name: "start_time", Type: field.TypeTime, Comment: "开始时间"},
		{Name: "last_time", Type: field.TypeTime, Comment: "最后更新时间"},
		{Name: "end_time", Type: field.TypeTime, Nullable: true, Comment: "结束时间"},
		{Name: "ack_time", Type: field.TypeTime, Nullable: true, Comment: "确认时间"},
		{Name: "close_time", Type: field.TypeTime, Nullable: true, Comment: "关闭时间"},
		{Name: "labels", Type: field.TypeJSON, Comment: "标签"},
		{Name: "ever_muted", Type: field.TypeBool, Comment: "是否被静默过", Default: false},
		{Name: "event_cnt", Type: field.TypeInt, Comment: "事件计数", Default: 1},
		{Name: "raw_data", Type: field.TypeJSON, Comment: "原始完整数据"},
		{Name: "integrations_id", Type: field.TypeUUID, Comment: "集成ID"},
		{Name: "space_id", Type: field.TypeString, Comment: "空间ID"},
		{Name: "incident_id", Type: field.TypeString, Nullable: true, Comment: "关联故障ID"},
	}
	// OncallAlertsTable holds the schema information for the "oncall_alerts" table.
	OncallAlertsTable = &schema.Table{
		Name:       "oncall_alerts",
		Columns:    OncallAlertsColumns,
		PrimaryKey: []*schema.Column{OncallAlertsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "alert_id",
				Unique:  false,
				Columns: []*schema.Column{OncallAlertsColumns[0]},
			},
			{
				Name:    "alert_integrations_id",
				Unique:  false,
				Columns: []*schema.Column{OncallAlertsColumns[19]},
			},
			{
				Name:    "alert_start_time",
				Unique:  false,
				Columns: []*schema.Column{OncallAlertsColumns[10]},
			},
			{
				Name:    "alert_space_id",
				Unique:  false,
				Columns: []*schema.Column{OncallAlertsColumns[20]},
			},
			{
				Name:    "alert_incident_id",
				Unique:  false,
				Columns: []*schema.Column{OncallAlertsColumns[21]},
			},
			{
				Name:    "alert_alert_key",
				Unique:  false,
				Columns: []*schema.Column{OncallAlertsColumns[9]},
			},
			{
				Name:    "alert_raw_alert_ref_id",
				Unique:  false,
				Columns: []*schema.Column{OncallAlertsColumns[3]},
			},
		},
	}
	// OncallIncidentsColumns holds the columns for the "oncall_incidents" table.
	OncallIncidentsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID, Comment: "UUID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Create Time | 创建日期"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Update Time | 修改日期"},
		{Name: "dedup_key", Type: field.TypeString, Comment: "去重键(故障聚合的第一条告警的alert_key)"},
		{Name: "title", Type: field.TypeString, Comment: "故障标题"},
		{Name: "description", Type: field.TypeString, Nullable: true, Comment: "描述"},
		{Name: "incident_severity", Type: field.TypeEnum, Comment: "故障严重程度", Enums: []string{"Info", "Warning", "Critical"}},
		{Name: "incident_status", Type: field.TypeEnum, Comment: "故障状态", Enums: []string{"Info", "Warning", "Critical", "Ok"}},
		{Name: "progress", Type: field.TypeEnum, Comment: "处理进度", Enums: []string{"Triggered", "Processing", "Closed"}},
		{Name: "start_time", Type: field.TypeTime, Comment: "开始时间"},
		{Name: "last_time", Type: field.TypeTime, Comment: "最后更新时间"},
		{Name: "end_time", Type: field.TypeTime, Nullable: true, Comment: "结束时间"},
		{Name: "ack_time", Type: field.TypeTime, Nullable: true, Comment: "首次认领时间"},
		{Name: "close_time", Type: field.TypeTime, Nullable: true, Comment: "关闭时间(故障自动恢复时，故障也将自动关闭)"},
		{Name: "creator_by", Type: field.TypeString, Comment: "发起人ID", Default: "system"},
		{Name: "close_by", Type: field.TypeString, Nullable: true, Comment: "关闭人"},
		{Name: "labels", Type: field.TypeJSON, Comment: "标签"},
		{Name: "root_cause", Type: field.TypeString, Nullable: true, Comment: "故障原因"},
		{Name: "resolution", Type: field.TypeString, Nullable: true, Comment: "解决方案"},
		{Name: "frequency", Type: field.TypeString, Comment: "频率(rare/frequent),rare30天内仅发生一次,frequent30天内发生多次", Default: "rare"},
		{Name: "ever_muted", Type: field.TypeBool, Comment: "是否被静音过", Default: false},
		{Name: "snoozed_before", Type: field.TypeInt64, Comment: "静音截止时间戳(取消关闭或认领时，该值重置为0)", Default: 0},
		{Name: "group_method", Type: field.TypeEnum, Comment: "分组方法", Enums: []string{"none", "pattern"}, Default: "none"},
		{Name: "alert_cnt", Type: field.TypeInt, Comment: "关联告警数", Default: 1},
		{Name: "integrations_id", Type: field.TypeUUID, Comment: "集成ID"},
		{Name: "space_id", Type: field.TypeString, Comment: "空间ID"},
	}
	// OncallIncidentsTable holds the schema information for the "oncall_incidents" table.
	OncallIncidentsTable = &schema.Table{
		Name:       "oncall_incidents",
		Columns:    OncallIncidentsColumns,
		PrimaryKey: []*schema.Column{OncallIncidentsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "incident_dedup_key",
				Unique:  false,
				Columns: []*schema.Column{OncallIncidentsColumns[3]},
			},
			{
				Name:    "incident_start_time",
				Unique:  false,
				Columns: []*schema.Column{OncallIncidentsColumns[9]},
			},
			{
				Name:    "incident_space_id",
				Unique:  false,
				Columns: []*schema.Column{OncallIncidentsColumns[25]},
			},
			{
				Name:    "incident_integrations_id",
				Unique:  false,
				Columns: []*schema.Column{OncallIncidentsColumns[24]},
			},
			{
				Name:    "incident_id",
				Unique:  false,
				Columns: []*schema.Column{OncallIncidentsColumns[0]},
			},
		},
	}
	// OncallRawalertsColumns holds the columns for the "oncall_rawalerts" table.
	OncallRawalertsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID, Comment: "UUID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Create Time | 创建日期"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Update Time | 修改日期"},
		{Name: "raw_data", Type: field.TypeString, Size: 2147483647, Comment: "原始数据"},
		{Name: "integrations_id", Type: field.TypeString, Comment: "集成ID"},
		{Name: "status", Type: field.TypeString, Comment: "处理状态", Default: "pending"},
		{Name: "message", Type: field.TypeString, Nullable: true, Comment: "处理消息"},
		{Name: "alert_raw_alert", Type: field.TypeUUID, Unique: true, Nullable: true},
	}
	// OncallRawalertsTable holds the schema information for the "oncall_rawalerts" table.
	OncallRawalertsTable = &schema.Table{
		Name:       "oncall_rawalerts",
		Columns:    OncallRawalertsColumns,
		PrimaryKey: []*schema.Column{OncallRawalertsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "oncall_rawalerts_oncall_alerts_raw_alert",
				Columns:    []*schema.Column{OncallRawalertsColumns[7]},
				RefColumns: []*schema.Column{OncallAlertsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "rawalert_created_at",
				Unique:  false,
				Columns: []*schema.Column{OncallRawalertsColumns[1]},
			},
		},
	}
	// IncidentAlertsColumns holds the columns for the "incident_alerts" table.
	IncidentAlertsColumns = []*schema.Column{
		{Name: "incident_id", Type: field.TypeUUID},
		{Name: "alert_id", Type: field.TypeUUID},
	}
	// IncidentAlertsTable holds the schema information for the "incident_alerts" table.
	IncidentAlertsTable = &schema.Table{
		Name:       "incident_alerts",
		Columns:    IncidentAlertsColumns,
		PrimaryKey: []*schema.Column{IncidentAlertsColumns[0], IncidentAlertsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "incident_alerts_incident_id",
				Columns:    []*schema.Column{IncidentAlertsColumns[0]},
				RefColumns: []*schema.Column{OncallIncidentsColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "incident_alerts_alert_id",
				Columns:    []*schema.Column{IncidentAlertsColumns[1]},
				RefColumns: []*schema.Column{OncallAlertsColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		OncallAlertsTable,
		OncallIncidentsTable,
		OncallRawalertsTable,
		IncidentAlertsTable,
	}
)

func init() {
	OncallAlertsTable.Annotation = &entsql.Annotation{
		Table: "oncall_alerts",
	}
	OncallIncidentsTable.Annotation = &entsql.Annotation{
		Table: "oncall_incidents",
	}
	OncallRawalertsTable.ForeignKeys[0].RefTable = OncallAlertsTable
	OncallRawalertsTable.Annotation = &entsql.Annotation{
		Table: "oncall_rawalerts",
	}
	IncidentAlertsTable.ForeignKeys[0].RefTable = OncallIncidentsTable
	IncidentAlertsTable.ForeignKeys[1].RefTable = OncallAlertsTable
}
