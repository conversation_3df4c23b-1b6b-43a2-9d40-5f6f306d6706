// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	uuid "github.com/gofrs/uuid/v5"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/alert"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/incident"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/predicate"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/rawalert"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeAlert    = "Alert"
	TypeIncident = "Incident"
	TypeRawAlert = "RawAlert"
)

// AlertMutation represents an operation that mutates the Alert nodes in the graph.
type AlertMutation struct {
	config
	op               Op
	typ              string
	id               *uuid.UUID
	created_at       *time.Time
	updated_at       *time.Time
	raw_alert_ref_id *string
	title            *string
	description      *string
	alert_severity   *alert.AlertSeverity
	alert_status     *alert.AlertStatus
	progress         *alert.Progress
	alert_key        *string
	start_time       *time.Time
	last_time        *time.Time
	end_time         *time.Time
	ack_time         *time.Time
	close_time       *time.Time
	labels           *map[string]string
	ever_muted       *bool
	event_cnt        *int
	addevent_cnt     *int
	raw_data         *map[string]interface{}
	integrations_id  *uuid.UUID
	space_id         *string
	incident_id      *string
	clearedFields    map[string]struct{}
	raw_alert        *uuid.UUID
	clearedraw_alert bool
	incident         map[uuid.UUID]struct{}
	removedincident  map[uuid.UUID]struct{}
	clearedincident  bool
	done             bool
	oldValue         func(context.Context) (*Alert, error)
	predicates       []predicate.Alert
}

var _ ent.Mutation = (*AlertMutation)(nil)

// alertOption allows management of the mutation configuration using functional options.
type alertOption func(*AlertMutation)

// newAlertMutation creates new mutation for the Alert entity.
func newAlertMutation(c config, op Op, opts ...alertOption) *AlertMutation {
	m := &AlertMutation{
		config:        c,
		op:            op,
		typ:           TypeAlert,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withAlertID sets the ID field of the mutation.
func withAlertID(id uuid.UUID) alertOption {
	return func(m *AlertMutation) {
		var (
			err   error
			once  sync.Once
			value *Alert
		)
		m.oldValue = func(ctx context.Context) (*Alert, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Alert.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withAlert sets the old Alert of the mutation.
func withAlert(node *Alert) alertOption {
	return func(m *AlertMutation) {
		m.oldValue = func(context.Context) (*Alert, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m AlertMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m AlertMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Alert entities.
func (m *AlertMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *AlertMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *AlertMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Alert.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *AlertMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *AlertMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *AlertMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *AlertMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *AlertMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *AlertMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetRawAlertRefID sets the "raw_alert_ref_id" field.
func (m *AlertMutation) SetRawAlertRefID(s string) {
	m.raw_alert_ref_id = &s
}

// RawAlertRefID returns the value of the "raw_alert_ref_id" field in the mutation.
func (m *AlertMutation) RawAlertRefID() (r string, exists bool) {
	v := m.raw_alert_ref_id
	if v == nil {
		return
	}
	return *v, true
}

// OldRawAlertRefID returns the old "raw_alert_ref_id" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldRawAlertRefID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRawAlertRefID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRawAlertRefID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRawAlertRefID: %w", err)
	}
	return oldValue.RawAlertRefID, nil
}

// ClearRawAlertRefID clears the value of the "raw_alert_ref_id" field.
func (m *AlertMutation) ClearRawAlertRefID() {
	m.raw_alert_ref_id = nil
	m.clearedFields[alert.FieldRawAlertRefID] = struct{}{}
}

// RawAlertRefIDCleared returns if the "raw_alert_ref_id" field was cleared in this mutation.
func (m *AlertMutation) RawAlertRefIDCleared() bool {
	_, ok := m.clearedFields[alert.FieldRawAlertRefID]
	return ok
}

// ResetRawAlertRefID resets all changes to the "raw_alert_ref_id" field.
func (m *AlertMutation) ResetRawAlertRefID() {
	m.raw_alert_ref_id = nil
	delete(m.clearedFields, alert.FieldRawAlertRefID)
}

// SetTitle sets the "title" field.
func (m *AlertMutation) SetTitle(s string) {
	m.title = &s
}

// Title returns the value of the "title" field in the mutation.
func (m *AlertMutation) Title() (r string, exists bool) {
	v := m.title
	if v == nil {
		return
	}
	return *v, true
}

// OldTitle returns the old "title" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldTitle(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTitle is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTitle requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTitle: %w", err)
	}
	return oldValue.Title, nil
}

// ResetTitle resets all changes to the "title" field.
func (m *AlertMutation) ResetTitle() {
	m.title = nil
}

// SetDescription sets the "description" field.
func (m *AlertMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *AlertMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ClearDescription clears the value of the "description" field.
func (m *AlertMutation) ClearDescription() {
	m.description = nil
	m.clearedFields[alert.FieldDescription] = struct{}{}
}

// DescriptionCleared returns if the "description" field was cleared in this mutation.
func (m *AlertMutation) DescriptionCleared() bool {
	_, ok := m.clearedFields[alert.FieldDescription]
	return ok
}

// ResetDescription resets all changes to the "description" field.
func (m *AlertMutation) ResetDescription() {
	m.description = nil
	delete(m.clearedFields, alert.FieldDescription)
}

// SetAlertSeverity sets the "alert_severity" field.
func (m *AlertMutation) SetAlertSeverity(as alert.AlertSeverity) {
	m.alert_severity = &as
}

// AlertSeverity returns the value of the "alert_severity" field in the mutation.
func (m *AlertMutation) AlertSeverity() (r alert.AlertSeverity, exists bool) {
	v := m.alert_severity
	if v == nil {
		return
	}
	return *v, true
}

// OldAlertSeverity returns the old "alert_severity" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldAlertSeverity(ctx context.Context) (v alert.AlertSeverity, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAlertSeverity is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAlertSeverity requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAlertSeverity: %w", err)
	}
	return oldValue.AlertSeverity, nil
}

// ResetAlertSeverity resets all changes to the "alert_severity" field.
func (m *AlertMutation) ResetAlertSeverity() {
	m.alert_severity = nil
}

// SetAlertStatus sets the "alert_status" field.
func (m *AlertMutation) SetAlertStatus(as alert.AlertStatus) {
	m.alert_status = &as
}

// AlertStatus returns the value of the "alert_status" field in the mutation.
func (m *AlertMutation) AlertStatus() (r alert.AlertStatus, exists bool) {
	v := m.alert_status
	if v == nil {
		return
	}
	return *v, true
}

// OldAlertStatus returns the old "alert_status" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldAlertStatus(ctx context.Context) (v alert.AlertStatus, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAlertStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAlertStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAlertStatus: %w", err)
	}
	return oldValue.AlertStatus, nil
}

// ResetAlertStatus resets all changes to the "alert_status" field.
func (m *AlertMutation) ResetAlertStatus() {
	m.alert_status = nil
}

// SetProgress sets the "progress" field.
func (m *AlertMutation) SetProgress(a alert.Progress) {
	m.progress = &a
}

// Progress returns the value of the "progress" field in the mutation.
func (m *AlertMutation) Progress() (r alert.Progress, exists bool) {
	v := m.progress
	if v == nil {
		return
	}
	return *v, true
}

// OldProgress returns the old "progress" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldProgress(ctx context.Context) (v alert.Progress, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldProgress is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldProgress requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldProgress: %w", err)
	}
	return oldValue.Progress, nil
}

// ResetProgress resets all changes to the "progress" field.
func (m *AlertMutation) ResetProgress() {
	m.progress = nil
}

// SetAlertKey sets the "alert_key" field.
func (m *AlertMutation) SetAlertKey(s string) {
	m.alert_key = &s
}

// AlertKey returns the value of the "alert_key" field in the mutation.
func (m *AlertMutation) AlertKey() (r string, exists bool) {
	v := m.alert_key
	if v == nil {
		return
	}
	return *v, true
}

// OldAlertKey returns the old "alert_key" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldAlertKey(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAlertKey is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAlertKey requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAlertKey: %w", err)
	}
	return oldValue.AlertKey, nil
}

// ResetAlertKey resets all changes to the "alert_key" field.
func (m *AlertMutation) ResetAlertKey() {
	m.alert_key = nil
}

// SetStartTime sets the "start_time" field.
func (m *AlertMutation) SetStartTime(t time.Time) {
	m.start_time = &t
}

// StartTime returns the value of the "start_time" field in the mutation.
func (m *AlertMutation) StartTime() (r time.Time, exists bool) {
	v := m.start_time
	if v == nil {
		return
	}
	return *v, true
}

// OldStartTime returns the old "start_time" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldStartTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStartTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStartTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStartTime: %w", err)
	}
	return oldValue.StartTime, nil
}

// ResetStartTime resets all changes to the "start_time" field.
func (m *AlertMutation) ResetStartTime() {
	m.start_time = nil
}

// SetLastTime sets the "last_time" field.
func (m *AlertMutation) SetLastTime(t time.Time) {
	m.last_time = &t
}

// LastTime returns the value of the "last_time" field in the mutation.
func (m *AlertMutation) LastTime() (r time.Time, exists bool) {
	v := m.last_time
	if v == nil {
		return
	}
	return *v, true
}

// OldLastTime returns the old "last_time" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldLastTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLastTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLastTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLastTime: %w", err)
	}
	return oldValue.LastTime, nil
}

// ResetLastTime resets all changes to the "last_time" field.
func (m *AlertMutation) ResetLastTime() {
	m.last_time = nil
}

// SetEndTime sets the "end_time" field.
func (m *AlertMutation) SetEndTime(t time.Time) {
	m.end_time = &t
}

// EndTime returns the value of the "end_time" field in the mutation.
func (m *AlertMutation) EndTime() (r time.Time, exists bool) {
	v := m.end_time
	if v == nil {
		return
	}
	return *v, true
}

// OldEndTime returns the old "end_time" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldEndTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEndTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEndTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEndTime: %w", err)
	}
	return oldValue.EndTime, nil
}

// ClearEndTime clears the value of the "end_time" field.
func (m *AlertMutation) ClearEndTime() {
	m.end_time = nil
	m.clearedFields[alert.FieldEndTime] = struct{}{}
}

// EndTimeCleared returns if the "end_time" field was cleared in this mutation.
func (m *AlertMutation) EndTimeCleared() bool {
	_, ok := m.clearedFields[alert.FieldEndTime]
	return ok
}

// ResetEndTime resets all changes to the "end_time" field.
func (m *AlertMutation) ResetEndTime() {
	m.end_time = nil
	delete(m.clearedFields, alert.FieldEndTime)
}

// SetAckTime sets the "ack_time" field.
func (m *AlertMutation) SetAckTime(t time.Time) {
	m.ack_time = &t
}

// AckTime returns the value of the "ack_time" field in the mutation.
func (m *AlertMutation) AckTime() (r time.Time, exists bool) {
	v := m.ack_time
	if v == nil {
		return
	}
	return *v, true
}

// OldAckTime returns the old "ack_time" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldAckTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAckTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAckTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAckTime: %w", err)
	}
	return oldValue.AckTime, nil
}

// ClearAckTime clears the value of the "ack_time" field.
func (m *AlertMutation) ClearAckTime() {
	m.ack_time = nil
	m.clearedFields[alert.FieldAckTime] = struct{}{}
}

// AckTimeCleared returns if the "ack_time" field was cleared in this mutation.
func (m *AlertMutation) AckTimeCleared() bool {
	_, ok := m.clearedFields[alert.FieldAckTime]
	return ok
}

// ResetAckTime resets all changes to the "ack_time" field.
func (m *AlertMutation) ResetAckTime() {
	m.ack_time = nil
	delete(m.clearedFields, alert.FieldAckTime)
}

// SetCloseTime sets the "close_time" field.
func (m *AlertMutation) SetCloseTime(t time.Time) {
	m.close_time = &t
}

// CloseTime returns the value of the "close_time" field in the mutation.
func (m *AlertMutation) CloseTime() (r time.Time, exists bool) {
	v := m.close_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCloseTime returns the old "close_time" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldCloseTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCloseTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCloseTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCloseTime: %w", err)
	}
	return oldValue.CloseTime, nil
}

// ClearCloseTime clears the value of the "close_time" field.
func (m *AlertMutation) ClearCloseTime() {
	m.close_time = nil
	m.clearedFields[alert.FieldCloseTime] = struct{}{}
}

// CloseTimeCleared returns if the "close_time" field was cleared in this mutation.
func (m *AlertMutation) CloseTimeCleared() bool {
	_, ok := m.clearedFields[alert.FieldCloseTime]
	return ok
}

// ResetCloseTime resets all changes to the "close_time" field.
func (m *AlertMutation) ResetCloseTime() {
	m.close_time = nil
	delete(m.clearedFields, alert.FieldCloseTime)
}

// SetLabels sets the "labels" field.
func (m *AlertMutation) SetLabels(value map[string]string) {
	m.labels = &value
}

// Labels returns the value of the "labels" field in the mutation.
func (m *AlertMutation) Labels() (r map[string]string, exists bool) {
	v := m.labels
	if v == nil {
		return
	}
	return *v, true
}

// OldLabels returns the old "labels" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldLabels(ctx context.Context) (v map[string]string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLabels is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLabels requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLabels: %w", err)
	}
	return oldValue.Labels, nil
}

// ResetLabels resets all changes to the "labels" field.
func (m *AlertMutation) ResetLabels() {
	m.labels = nil
}

// SetEverMuted sets the "ever_muted" field.
func (m *AlertMutation) SetEverMuted(b bool) {
	m.ever_muted = &b
}

// EverMuted returns the value of the "ever_muted" field in the mutation.
func (m *AlertMutation) EverMuted() (r bool, exists bool) {
	v := m.ever_muted
	if v == nil {
		return
	}
	return *v, true
}

// OldEverMuted returns the old "ever_muted" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldEverMuted(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEverMuted is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEverMuted requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEverMuted: %w", err)
	}
	return oldValue.EverMuted, nil
}

// ResetEverMuted resets all changes to the "ever_muted" field.
func (m *AlertMutation) ResetEverMuted() {
	m.ever_muted = nil
}

// SetEventCnt sets the "event_cnt" field.
func (m *AlertMutation) SetEventCnt(i int) {
	m.event_cnt = &i
	m.addevent_cnt = nil
}

// EventCnt returns the value of the "event_cnt" field in the mutation.
func (m *AlertMutation) EventCnt() (r int, exists bool) {
	v := m.event_cnt
	if v == nil {
		return
	}
	return *v, true
}

// OldEventCnt returns the old "event_cnt" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldEventCnt(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEventCnt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEventCnt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEventCnt: %w", err)
	}
	return oldValue.EventCnt, nil
}

// AddEventCnt adds i to the "event_cnt" field.
func (m *AlertMutation) AddEventCnt(i int) {
	if m.addevent_cnt != nil {
		*m.addevent_cnt += i
	} else {
		m.addevent_cnt = &i
	}
}

// AddedEventCnt returns the value that was added to the "event_cnt" field in this mutation.
func (m *AlertMutation) AddedEventCnt() (r int, exists bool) {
	v := m.addevent_cnt
	if v == nil {
		return
	}
	return *v, true
}

// ResetEventCnt resets all changes to the "event_cnt" field.
func (m *AlertMutation) ResetEventCnt() {
	m.event_cnt = nil
	m.addevent_cnt = nil
}

// SetRawData sets the "raw_data" field.
func (m *AlertMutation) SetRawData(value map[string]interface{}) {
	m.raw_data = &value
}

// RawData returns the value of the "raw_data" field in the mutation.
func (m *AlertMutation) RawData() (r map[string]interface{}, exists bool) {
	v := m.raw_data
	if v == nil {
		return
	}
	return *v, true
}

// OldRawData returns the old "raw_data" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldRawData(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRawData is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRawData requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRawData: %w", err)
	}
	return oldValue.RawData, nil
}

// ResetRawData resets all changes to the "raw_data" field.
func (m *AlertMutation) ResetRawData() {
	m.raw_data = nil
}

// SetIntegrationsID sets the "integrations_id" field.
func (m *AlertMutation) SetIntegrationsID(u uuid.UUID) {
	m.integrations_id = &u
}

// IntegrationsID returns the value of the "integrations_id" field in the mutation.
func (m *AlertMutation) IntegrationsID() (r uuid.UUID, exists bool) {
	v := m.integrations_id
	if v == nil {
		return
	}
	return *v, true
}

// OldIntegrationsID returns the old "integrations_id" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldIntegrationsID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIntegrationsID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIntegrationsID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIntegrationsID: %w", err)
	}
	return oldValue.IntegrationsID, nil
}

// ResetIntegrationsID resets all changes to the "integrations_id" field.
func (m *AlertMutation) ResetIntegrationsID() {
	m.integrations_id = nil
}

// SetSpaceID sets the "space_id" field.
func (m *AlertMutation) SetSpaceID(s string) {
	m.space_id = &s
}

// SpaceID returns the value of the "space_id" field in the mutation.
func (m *AlertMutation) SpaceID() (r string, exists bool) {
	v := m.space_id
	if v == nil {
		return
	}
	return *v, true
}

// OldSpaceID returns the old "space_id" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldSpaceID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSpaceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSpaceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSpaceID: %w", err)
	}
	return oldValue.SpaceID, nil
}

// ResetSpaceID resets all changes to the "space_id" field.
func (m *AlertMutation) ResetSpaceID() {
	m.space_id = nil
}

// SetIncidentID sets the "incident_id" field.
func (m *AlertMutation) SetIncidentID(s string) {
	m.incident_id = &s
}

// IncidentID returns the value of the "incident_id" field in the mutation.
func (m *AlertMutation) IncidentID() (r string, exists bool) {
	v := m.incident_id
	if v == nil {
		return
	}
	return *v, true
}

// OldIncidentID returns the old "incident_id" field's value of the Alert entity.
// If the Alert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertMutation) OldIncidentID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIncidentID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIncidentID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIncidentID: %w", err)
	}
	return oldValue.IncidentID, nil
}

// ClearIncidentID clears the value of the "incident_id" field.
func (m *AlertMutation) ClearIncidentID() {
	m.incident_id = nil
	m.clearedFields[alert.FieldIncidentID] = struct{}{}
}

// IncidentIDCleared returns if the "incident_id" field was cleared in this mutation.
func (m *AlertMutation) IncidentIDCleared() bool {
	_, ok := m.clearedFields[alert.FieldIncidentID]
	return ok
}

// ResetIncidentID resets all changes to the "incident_id" field.
func (m *AlertMutation) ResetIncidentID() {
	m.incident_id = nil
	delete(m.clearedFields, alert.FieldIncidentID)
}

// SetRawAlertID sets the "raw_alert" edge to the RawAlert entity by id.
func (m *AlertMutation) SetRawAlertID(id uuid.UUID) {
	m.raw_alert = &id
}

// ClearRawAlert clears the "raw_alert" edge to the RawAlert entity.
func (m *AlertMutation) ClearRawAlert() {
	m.clearedraw_alert = true
}

// RawAlertCleared reports if the "raw_alert" edge to the RawAlert entity was cleared.
func (m *AlertMutation) RawAlertCleared() bool {
	return m.clearedraw_alert
}

// RawAlertID returns the "raw_alert" edge ID in the mutation.
func (m *AlertMutation) RawAlertID() (id uuid.UUID, exists bool) {
	if m.raw_alert != nil {
		return *m.raw_alert, true
	}
	return
}

// RawAlertIDs returns the "raw_alert" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// RawAlertID instead. It exists only for internal usage by the builders.
func (m *AlertMutation) RawAlertIDs() (ids []uuid.UUID) {
	if id := m.raw_alert; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetRawAlert resets all changes to the "raw_alert" edge.
func (m *AlertMutation) ResetRawAlert() {
	m.raw_alert = nil
	m.clearedraw_alert = false
}

// AddIncidentIDs adds the "incident" edge to the Incident entity by ids.
func (m *AlertMutation) AddIncidentIDs(ids ...uuid.UUID) {
	if m.incident == nil {
		m.incident = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.incident[ids[i]] = struct{}{}
	}
}

// ClearIncident clears the "incident" edge to the Incident entity.
func (m *AlertMutation) ClearIncident() {
	m.clearedincident = true
}

// IncidentCleared reports if the "incident" edge to the Incident entity was cleared.
func (m *AlertMutation) IncidentCleared() bool {
	return m.clearedincident
}

// RemoveIncidentIDs removes the "incident" edge to the Incident entity by IDs.
func (m *AlertMutation) RemoveIncidentIDs(ids ...uuid.UUID) {
	if m.removedincident == nil {
		m.removedincident = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.incident, ids[i])
		m.removedincident[ids[i]] = struct{}{}
	}
}

// RemovedIncident returns the removed IDs of the "incident" edge to the Incident entity.
func (m *AlertMutation) RemovedIncidentIDs() (ids []uuid.UUID) {
	for id := range m.removedincident {
		ids = append(ids, id)
	}
	return
}

// IncidentIDs returns the "incident" edge IDs in the mutation.
func (m *AlertMutation) IncidentIDs() (ids []uuid.UUID) {
	for id := range m.incident {
		ids = append(ids, id)
	}
	return
}

// ResetIncident resets all changes to the "incident" edge.
func (m *AlertMutation) ResetIncident() {
	m.incident = nil
	m.clearedincident = false
	m.removedincident = nil
}

// Where appends a list predicates to the AlertMutation builder.
func (m *AlertMutation) Where(ps ...predicate.Alert) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the AlertMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *AlertMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Alert, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *AlertMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *AlertMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Alert).
func (m *AlertMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *AlertMutation) Fields() []string {
	fields := make([]string, 0, 21)
	if m.created_at != nil {
		fields = append(fields, alert.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, alert.FieldUpdatedAt)
	}
	if m.raw_alert_ref_id != nil {
		fields = append(fields, alert.FieldRawAlertRefID)
	}
	if m.title != nil {
		fields = append(fields, alert.FieldTitle)
	}
	if m.description != nil {
		fields = append(fields, alert.FieldDescription)
	}
	if m.alert_severity != nil {
		fields = append(fields, alert.FieldAlertSeverity)
	}
	if m.alert_status != nil {
		fields = append(fields, alert.FieldAlertStatus)
	}
	if m.progress != nil {
		fields = append(fields, alert.FieldProgress)
	}
	if m.alert_key != nil {
		fields = append(fields, alert.FieldAlertKey)
	}
	if m.start_time != nil {
		fields = append(fields, alert.FieldStartTime)
	}
	if m.last_time != nil {
		fields = append(fields, alert.FieldLastTime)
	}
	if m.end_time != nil {
		fields = append(fields, alert.FieldEndTime)
	}
	if m.ack_time != nil {
		fields = append(fields, alert.FieldAckTime)
	}
	if m.close_time != nil {
		fields = append(fields, alert.FieldCloseTime)
	}
	if m.labels != nil {
		fields = append(fields, alert.FieldLabels)
	}
	if m.ever_muted != nil {
		fields = append(fields, alert.FieldEverMuted)
	}
	if m.event_cnt != nil {
		fields = append(fields, alert.FieldEventCnt)
	}
	if m.raw_data != nil {
		fields = append(fields, alert.FieldRawData)
	}
	if m.integrations_id != nil {
		fields = append(fields, alert.FieldIntegrationsID)
	}
	if m.space_id != nil {
		fields = append(fields, alert.FieldSpaceID)
	}
	if m.incident_id != nil {
		fields = append(fields, alert.FieldIncidentID)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *AlertMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case alert.FieldCreatedAt:
		return m.CreatedAt()
	case alert.FieldUpdatedAt:
		return m.UpdatedAt()
	case alert.FieldRawAlertRefID:
		return m.RawAlertRefID()
	case alert.FieldTitle:
		return m.Title()
	case alert.FieldDescription:
		return m.Description()
	case alert.FieldAlertSeverity:
		return m.AlertSeverity()
	case alert.FieldAlertStatus:
		return m.AlertStatus()
	case alert.FieldProgress:
		return m.Progress()
	case alert.FieldAlertKey:
		return m.AlertKey()
	case alert.FieldStartTime:
		return m.StartTime()
	case alert.FieldLastTime:
		return m.LastTime()
	case alert.FieldEndTime:
		return m.EndTime()
	case alert.FieldAckTime:
		return m.AckTime()
	case alert.FieldCloseTime:
		return m.CloseTime()
	case alert.FieldLabels:
		return m.Labels()
	case alert.FieldEverMuted:
		return m.EverMuted()
	case alert.FieldEventCnt:
		return m.EventCnt()
	case alert.FieldRawData:
		return m.RawData()
	case alert.FieldIntegrationsID:
		return m.IntegrationsID()
	case alert.FieldSpaceID:
		return m.SpaceID()
	case alert.FieldIncidentID:
		return m.IncidentID()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *AlertMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case alert.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case alert.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case alert.FieldRawAlertRefID:
		return m.OldRawAlertRefID(ctx)
	case alert.FieldTitle:
		return m.OldTitle(ctx)
	case alert.FieldDescription:
		return m.OldDescription(ctx)
	case alert.FieldAlertSeverity:
		return m.OldAlertSeverity(ctx)
	case alert.FieldAlertStatus:
		return m.OldAlertStatus(ctx)
	case alert.FieldProgress:
		return m.OldProgress(ctx)
	case alert.FieldAlertKey:
		return m.OldAlertKey(ctx)
	case alert.FieldStartTime:
		return m.OldStartTime(ctx)
	case alert.FieldLastTime:
		return m.OldLastTime(ctx)
	case alert.FieldEndTime:
		return m.OldEndTime(ctx)
	case alert.FieldAckTime:
		return m.OldAckTime(ctx)
	case alert.FieldCloseTime:
		return m.OldCloseTime(ctx)
	case alert.FieldLabels:
		return m.OldLabels(ctx)
	case alert.FieldEverMuted:
		return m.OldEverMuted(ctx)
	case alert.FieldEventCnt:
		return m.OldEventCnt(ctx)
	case alert.FieldRawData:
		return m.OldRawData(ctx)
	case alert.FieldIntegrationsID:
		return m.OldIntegrationsID(ctx)
	case alert.FieldSpaceID:
		return m.OldSpaceID(ctx)
	case alert.FieldIncidentID:
		return m.OldIncidentID(ctx)
	}
	return nil, fmt.Errorf("unknown Alert field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AlertMutation) SetField(name string, value ent.Value) error {
	switch name {
	case alert.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case alert.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case alert.FieldRawAlertRefID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRawAlertRefID(v)
		return nil
	case alert.FieldTitle:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTitle(v)
		return nil
	case alert.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case alert.FieldAlertSeverity:
		v, ok := value.(alert.AlertSeverity)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAlertSeverity(v)
		return nil
	case alert.FieldAlertStatus:
		v, ok := value.(alert.AlertStatus)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAlertStatus(v)
		return nil
	case alert.FieldProgress:
		v, ok := value.(alert.Progress)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetProgress(v)
		return nil
	case alert.FieldAlertKey:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAlertKey(v)
		return nil
	case alert.FieldStartTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStartTime(v)
		return nil
	case alert.FieldLastTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLastTime(v)
		return nil
	case alert.FieldEndTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEndTime(v)
		return nil
	case alert.FieldAckTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAckTime(v)
		return nil
	case alert.FieldCloseTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCloseTime(v)
		return nil
	case alert.FieldLabels:
		v, ok := value.(map[string]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLabels(v)
		return nil
	case alert.FieldEverMuted:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEverMuted(v)
		return nil
	case alert.FieldEventCnt:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEventCnt(v)
		return nil
	case alert.FieldRawData:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRawData(v)
		return nil
	case alert.FieldIntegrationsID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIntegrationsID(v)
		return nil
	case alert.FieldSpaceID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSpaceID(v)
		return nil
	case alert.FieldIncidentID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIncidentID(v)
		return nil
	}
	return fmt.Errorf("unknown Alert field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *AlertMutation) AddedFields() []string {
	var fields []string
	if m.addevent_cnt != nil {
		fields = append(fields, alert.FieldEventCnt)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *AlertMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case alert.FieldEventCnt:
		return m.AddedEventCnt()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AlertMutation) AddField(name string, value ent.Value) error {
	switch name {
	case alert.FieldEventCnt:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddEventCnt(v)
		return nil
	}
	return fmt.Errorf("unknown Alert numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *AlertMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(alert.FieldRawAlertRefID) {
		fields = append(fields, alert.FieldRawAlertRefID)
	}
	if m.FieldCleared(alert.FieldDescription) {
		fields = append(fields, alert.FieldDescription)
	}
	if m.FieldCleared(alert.FieldEndTime) {
		fields = append(fields, alert.FieldEndTime)
	}
	if m.FieldCleared(alert.FieldAckTime) {
		fields = append(fields, alert.FieldAckTime)
	}
	if m.FieldCleared(alert.FieldCloseTime) {
		fields = append(fields, alert.FieldCloseTime)
	}
	if m.FieldCleared(alert.FieldIncidentID) {
		fields = append(fields, alert.FieldIncidentID)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *AlertMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *AlertMutation) ClearField(name string) error {
	switch name {
	case alert.FieldRawAlertRefID:
		m.ClearRawAlertRefID()
		return nil
	case alert.FieldDescription:
		m.ClearDescription()
		return nil
	case alert.FieldEndTime:
		m.ClearEndTime()
		return nil
	case alert.FieldAckTime:
		m.ClearAckTime()
		return nil
	case alert.FieldCloseTime:
		m.ClearCloseTime()
		return nil
	case alert.FieldIncidentID:
		m.ClearIncidentID()
		return nil
	}
	return fmt.Errorf("unknown Alert nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *AlertMutation) ResetField(name string) error {
	switch name {
	case alert.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case alert.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case alert.FieldRawAlertRefID:
		m.ResetRawAlertRefID()
		return nil
	case alert.FieldTitle:
		m.ResetTitle()
		return nil
	case alert.FieldDescription:
		m.ResetDescription()
		return nil
	case alert.FieldAlertSeverity:
		m.ResetAlertSeverity()
		return nil
	case alert.FieldAlertStatus:
		m.ResetAlertStatus()
		return nil
	case alert.FieldProgress:
		m.ResetProgress()
		return nil
	case alert.FieldAlertKey:
		m.ResetAlertKey()
		return nil
	case alert.FieldStartTime:
		m.ResetStartTime()
		return nil
	case alert.FieldLastTime:
		m.ResetLastTime()
		return nil
	case alert.FieldEndTime:
		m.ResetEndTime()
		return nil
	case alert.FieldAckTime:
		m.ResetAckTime()
		return nil
	case alert.FieldCloseTime:
		m.ResetCloseTime()
		return nil
	case alert.FieldLabels:
		m.ResetLabels()
		return nil
	case alert.FieldEverMuted:
		m.ResetEverMuted()
		return nil
	case alert.FieldEventCnt:
		m.ResetEventCnt()
		return nil
	case alert.FieldRawData:
		m.ResetRawData()
		return nil
	case alert.FieldIntegrationsID:
		m.ResetIntegrationsID()
		return nil
	case alert.FieldSpaceID:
		m.ResetSpaceID()
		return nil
	case alert.FieldIncidentID:
		m.ResetIncidentID()
		return nil
	}
	return fmt.Errorf("unknown Alert field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *AlertMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.raw_alert != nil {
		edges = append(edges, alert.EdgeRawAlert)
	}
	if m.incident != nil {
		edges = append(edges, alert.EdgeIncident)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *AlertMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case alert.EdgeRawAlert:
		if id := m.raw_alert; id != nil {
			return []ent.Value{*id}
		}
	case alert.EdgeIncident:
		ids := make([]ent.Value, 0, len(m.incident))
		for id := range m.incident {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *AlertMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	if m.removedincident != nil {
		edges = append(edges, alert.EdgeIncident)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *AlertMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case alert.EdgeIncident:
		ids := make([]ent.Value, 0, len(m.removedincident))
		for id := range m.removedincident {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *AlertMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedraw_alert {
		edges = append(edges, alert.EdgeRawAlert)
	}
	if m.clearedincident {
		edges = append(edges, alert.EdgeIncident)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *AlertMutation) EdgeCleared(name string) bool {
	switch name {
	case alert.EdgeRawAlert:
		return m.clearedraw_alert
	case alert.EdgeIncident:
		return m.clearedincident
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *AlertMutation) ClearEdge(name string) error {
	switch name {
	case alert.EdgeRawAlert:
		m.ClearRawAlert()
		return nil
	}
	return fmt.Errorf("unknown Alert unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *AlertMutation) ResetEdge(name string) error {
	switch name {
	case alert.EdgeRawAlert:
		m.ResetRawAlert()
		return nil
	case alert.EdgeIncident:
		m.ResetIncident()
		return nil
	}
	return fmt.Errorf("unknown Alert edge %s", name)
}

// IncidentMutation represents an operation that mutates the Incident nodes in the graph.
type IncidentMutation struct {
	config
	op                Op
	typ               string
	id                *uuid.UUID
	created_at        *time.Time
	updated_at        *time.Time
	dedup_key         *string
	title             *string
	description       *string
	incident_severity *incident.IncidentSeverity
	incident_status   *incident.IncidentStatus
	progress          *incident.Progress
	start_time        *time.Time
	last_time         *time.Time
	end_time          *time.Time
	ack_time          *time.Time
	close_time        *time.Time
	creator_by        *string
	close_by          *string
	labels            *map[string]string
	root_cause        *string
	resolution        *string
	frequency         *string
	ever_muted        *bool
	snoozed_before    *int64
	addsnoozed_before *int64
	group_method      *incident.GroupMethod
	alert_cnt         *int
	addalert_cnt      *int
	integrations_id   *uuid.UUID
	space_id          *string
	clearedFields     map[string]struct{}
	alerts            map[uuid.UUID]struct{}
	removedalerts     map[uuid.UUID]struct{}
	clearedalerts     bool
	done              bool
	oldValue          func(context.Context) (*Incident, error)
	predicates        []predicate.Incident
}

var _ ent.Mutation = (*IncidentMutation)(nil)

// incidentOption allows management of the mutation configuration using functional options.
type incidentOption func(*IncidentMutation)

// newIncidentMutation creates new mutation for the Incident entity.
func newIncidentMutation(c config, op Op, opts ...incidentOption) *IncidentMutation {
	m := &IncidentMutation{
		config:        c,
		op:            op,
		typ:           TypeIncident,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withIncidentID sets the ID field of the mutation.
func withIncidentID(id uuid.UUID) incidentOption {
	return func(m *IncidentMutation) {
		var (
			err   error
			once  sync.Once
			value *Incident
		)
		m.oldValue = func(ctx context.Context) (*Incident, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Incident.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withIncident sets the old Incident of the mutation.
func withIncident(node *Incident) incidentOption {
	return func(m *IncidentMutation) {
		m.oldValue = func(context.Context) (*Incident, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m IncidentMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m IncidentMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Incident entities.
func (m *IncidentMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *IncidentMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *IncidentMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Incident.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *IncidentMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *IncidentMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *IncidentMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *IncidentMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *IncidentMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *IncidentMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDedupKey sets the "dedup_key" field.
func (m *IncidentMutation) SetDedupKey(s string) {
	m.dedup_key = &s
}

// DedupKey returns the value of the "dedup_key" field in the mutation.
func (m *IncidentMutation) DedupKey() (r string, exists bool) {
	v := m.dedup_key
	if v == nil {
		return
	}
	return *v, true
}

// OldDedupKey returns the old "dedup_key" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldDedupKey(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDedupKey is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDedupKey requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDedupKey: %w", err)
	}
	return oldValue.DedupKey, nil
}

// ResetDedupKey resets all changes to the "dedup_key" field.
func (m *IncidentMutation) ResetDedupKey() {
	m.dedup_key = nil
}

// SetTitle sets the "title" field.
func (m *IncidentMutation) SetTitle(s string) {
	m.title = &s
}

// Title returns the value of the "title" field in the mutation.
func (m *IncidentMutation) Title() (r string, exists bool) {
	v := m.title
	if v == nil {
		return
	}
	return *v, true
}

// OldTitle returns the old "title" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldTitle(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTitle is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTitle requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTitle: %w", err)
	}
	return oldValue.Title, nil
}

// ResetTitle resets all changes to the "title" field.
func (m *IncidentMutation) ResetTitle() {
	m.title = nil
}

// SetDescription sets the "description" field.
func (m *IncidentMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *IncidentMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ClearDescription clears the value of the "description" field.
func (m *IncidentMutation) ClearDescription() {
	m.description = nil
	m.clearedFields[incident.FieldDescription] = struct{}{}
}

// DescriptionCleared returns if the "description" field was cleared in this mutation.
func (m *IncidentMutation) DescriptionCleared() bool {
	_, ok := m.clearedFields[incident.FieldDescription]
	return ok
}

// ResetDescription resets all changes to the "description" field.
func (m *IncidentMutation) ResetDescription() {
	m.description = nil
	delete(m.clearedFields, incident.FieldDescription)
}

// SetIncidentSeverity sets the "incident_severity" field.
func (m *IncidentMutation) SetIncidentSeverity(is incident.IncidentSeverity) {
	m.incident_severity = &is
}

// IncidentSeverity returns the value of the "incident_severity" field in the mutation.
func (m *IncidentMutation) IncidentSeverity() (r incident.IncidentSeverity, exists bool) {
	v := m.incident_severity
	if v == nil {
		return
	}
	return *v, true
}

// OldIncidentSeverity returns the old "incident_severity" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldIncidentSeverity(ctx context.Context) (v incident.IncidentSeverity, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIncidentSeverity is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIncidentSeverity requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIncidentSeverity: %w", err)
	}
	return oldValue.IncidentSeverity, nil
}

// ResetIncidentSeverity resets all changes to the "incident_severity" field.
func (m *IncidentMutation) ResetIncidentSeverity() {
	m.incident_severity = nil
}

// SetIncidentStatus sets the "incident_status" field.
func (m *IncidentMutation) SetIncidentStatus(is incident.IncidentStatus) {
	m.incident_status = &is
}

// IncidentStatus returns the value of the "incident_status" field in the mutation.
func (m *IncidentMutation) IncidentStatus() (r incident.IncidentStatus, exists bool) {
	v := m.incident_status
	if v == nil {
		return
	}
	return *v, true
}

// OldIncidentStatus returns the old "incident_status" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldIncidentStatus(ctx context.Context) (v incident.IncidentStatus, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIncidentStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIncidentStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIncidentStatus: %w", err)
	}
	return oldValue.IncidentStatus, nil
}

// ResetIncidentStatus resets all changes to the "incident_status" field.
func (m *IncidentMutation) ResetIncidentStatus() {
	m.incident_status = nil
}

// SetProgress sets the "progress" field.
func (m *IncidentMutation) SetProgress(i incident.Progress) {
	m.progress = &i
}

// Progress returns the value of the "progress" field in the mutation.
func (m *IncidentMutation) Progress() (r incident.Progress, exists bool) {
	v := m.progress
	if v == nil {
		return
	}
	return *v, true
}

// OldProgress returns the old "progress" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldProgress(ctx context.Context) (v incident.Progress, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldProgress is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldProgress requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldProgress: %w", err)
	}
	return oldValue.Progress, nil
}

// ResetProgress resets all changes to the "progress" field.
func (m *IncidentMutation) ResetProgress() {
	m.progress = nil
}

// SetStartTime sets the "start_time" field.
func (m *IncidentMutation) SetStartTime(t time.Time) {
	m.start_time = &t
}

// StartTime returns the value of the "start_time" field in the mutation.
func (m *IncidentMutation) StartTime() (r time.Time, exists bool) {
	v := m.start_time
	if v == nil {
		return
	}
	return *v, true
}

// OldStartTime returns the old "start_time" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldStartTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStartTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStartTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStartTime: %w", err)
	}
	return oldValue.StartTime, nil
}

// ResetStartTime resets all changes to the "start_time" field.
func (m *IncidentMutation) ResetStartTime() {
	m.start_time = nil
}

// SetLastTime sets the "last_time" field.
func (m *IncidentMutation) SetLastTime(t time.Time) {
	m.last_time = &t
}

// LastTime returns the value of the "last_time" field in the mutation.
func (m *IncidentMutation) LastTime() (r time.Time, exists bool) {
	v := m.last_time
	if v == nil {
		return
	}
	return *v, true
}

// OldLastTime returns the old "last_time" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldLastTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLastTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLastTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLastTime: %w", err)
	}
	return oldValue.LastTime, nil
}

// ResetLastTime resets all changes to the "last_time" field.
func (m *IncidentMutation) ResetLastTime() {
	m.last_time = nil
}

// SetEndTime sets the "end_time" field.
func (m *IncidentMutation) SetEndTime(t time.Time) {
	m.end_time = &t
}

// EndTime returns the value of the "end_time" field in the mutation.
func (m *IncidentMutation) EndTime() (r time.Time, exists bool) {
	v := m.end_time
	if v == nil {
		return
	}
	return *v, true
}

// OldEndTime returns the old "end_time" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldEndTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEndTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEndTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEndTime: %w", err)
	}
	return oldValue.EndTime, nil
}

// ClearEndTime clears the value of the "end_time" field.
func (m *IncidentMutation) ClearEndTime() {
	m.end_time = nil
	m.clearedFields[incident.FieldEndTime] = struct{}{}
}

// EndTimeCleared returns if the "end_time" field was cleared in this mutation.
func (m *IncidentMutation) EndTimeCleared() bool {
	_, ok := m.clearedFields[incident.FieldEndTime]
	return ok
}

// ResetEndTime resets all changes to the "end_time" field.
func (m *IncidentMutation) ResetEndTime() {
	m.end_time = nil
	delete(m.clearedFields, incident.FieldEndTime)
}

// SetAckTime sets the "ack_time" field.
func (m *IncidentMutation) SetAckTime(t time.Time) {
	m.ack_time = &t
}

// AckTime returns the value of the "ack_time" field in the mutation.
func (m *IncidentMutation) AckTime() (r time.Time, exists bool) {
	v := m.ack_time
	if v == nil {
		return
	}
	return *v, true
}

// OldAckTime returns the old "ack_time" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldAckTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAckTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAckTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAckTime: %w", err)
	}
	return oldValue.AckTime, nil
}

// ClearAckTime clears the value of the "ack_time" field.
func (m *IncidentMutation) ClearAckTime() {
	m.ack_time = nil
	m.clearedFields[incident.FieldAckTime] = struct{}{}
}

// AckTimeCleared returns if the "ack_time" field was cleared in this mutation.
func (m *IncidentMutation) AckTimeCleared() bool {
	_, ok := m.clearedFields[incident.FieldAckTime]
	return ok
}

// ResetAckTime resets all changes to the "ack_time" field.
func (m *IncidentMutation) ResetAckTime() {
	m.ack_time = nil
	delete(m.clearedFields, incident.FieldAckTime)
}

// SetCloseTime sets the "close_time" field.
func (m *IncidentMutation) SetCloseTime(t time.Time) {
	m.close_time = &t
}

// CloseTime returns the value of the "close_time" field in the mutation.
func (m *IncidentMutation) CloseTime() (r time.Time, exists bool) {
	v := m.close_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCloseTime returns the old "close_time" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldCloseTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCloseTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCloseTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCloseTime: %w", err)
	}
	return oldValue.CloseTime, nil
}

// ClearCloseTime clears the value of the "close_time" field.
func (m *IncidentMutation) ClearCloseTime() {
	m.close_time = nil
	m.clearedFields[incident.FieldCloseTime] = struct{}{}
}

// CloseTimeCleared returns if the "close_time" field was cleared in this mutation.
func (m *IncidentMutation) CloseTimeCleared() bool {
	_, ok := m.clearedFields[incident.FieldCloseTime]
	return ok
}

// ResetCloseTime resets all changes to the "close_time" field.
func (m *IncidentMutation) ResetCloseTime() {
	m.close_time = nil
	delete(m.clearedFields, incident.FieldCloseTime)
}

// SetCreatorBy sets the "creator_by" field.
func (m *IncidentMutation) SetCreatorBy(s string) {
	m.creator_by = &s
}

// CreatorBy returns the value of the "creator_by" field in the mutation.
func (m *IncidentMutation) CreatorBy() (r string, exists bool) {
	v := m.creator_by
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatorBy returns the old "creator_by" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldCreatorBy(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatorBy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatorBy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatorBy: %w", err)
	}
	return oldValue.CreatorBy, nil
}

// ResetCreatorBy resets all changes to the "creator_by" field.
func (m *IncidentMutation) ResetCreatorBy() {
	m.creator_by = nil
}

// SetCloseBy sets the "close_by" field.
func (m *IncidentMutation) SetCloseBy(s string) {
	m.close_by = &s
}

// CloseBy returns the value of the "close_by" field in the mutation.
func (m *IncidentMutation) CloseBy() (r string, exists bool) {
	v := m.close_by
	if v == nil {
		return
	}
	return *v, true
}

// OldCloseBy returns the old "close_by" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldCloseBy(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCloseBy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCloseBy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCloseBy: %w", err)
	}
	return oldValue.CloseBy, nil
}

// ClearCloseBy clears the value of the "close_by" field.
func (m *IncidentMutation) ClearCloseBy() {
	m.close_by = nil
	m.clearedFields[incident.FieldCloseBy] = struct{}{}
}

// CloseByCleared returns if the "close_by" field was cleared in this mutation.
func (m *IncidentMutation) CloseByCleared() bool {
	_, ok := m.clearedFields[incident.FieldCloseBy]
	return ok
}

// ResetCloseBy resets all changes to the "close_by" field.
func (m *IncidentMutation) ResetCloseBy() {
	m.close_by = nil
	delete(m.clearedFields, incident.FieldCloseBy)
}

// SetLabels sets the "labels" field.
func (m *IncidentMutation) SetLabels(value map[string]string) {
	m.labels = &value
}

// Labels returns the value of the "labels" field in the mutation.
func (m *IncidentMutation) Labels() (r map[string]string, exists bool) {
	v := m.labels
	if v == nil {
		return
	}
	return *v, true
}

// OldLabels returns the old "labels" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldLabels(ctx context.Context) (v map[string]string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLabels is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLabels requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLabels: %w", err)
	}
	return oldValue.Labels, nil
}

// ResetLabels resets all changes to the "labels" field.
func (m *IncidentMutation) ResetLabels() {
	m.labels = nil
}

// SetRootCause sets the "root_cause" field.
func (m *IncidentMutation) SetRootCause(s string) {
	m.root_cause = &s
}

// RootCause returns the value of the "root_cause" field in the mutation.
func (m *IncidentMutation) RootCause() (r string, exists bool) {
	v := m.root_cause
	if v == nil {
		return
	}
	return *v, true
}

// OldRootCause returns the old "root_cause" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldRootCause(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRootCause is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRootCause requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRootCause: %w", err)
	}
	return oldValue.RootCause, nil
}

// ClearRootCause clears the value of the "root_cause" field.
func (m *IncidentMutation) ClearRootCause() {
	m.root_cause = nil
	m.clearedFields[incident.FieldRootCause] = struct{}{}
}

// RootCauseCleared returns if the "root_cause" field was cleared in this mutation.
func (m *IncidentMutation) RootCauseCleared() bool {
	_, ok := m.clearedFields[incident.FieldRootCause]
	return ok
}

// ResetRootCause resets all changes to the "root_cause" field.
func (m *IncidentMutation) ResetRootCause() {
	m.root_cause = nil
	delete(m.clearedFields, incident.FieldRootCause)
}

// SetResolution sets the "resolution" field.
func (m *IncidentMutation) SetResolution(s string) {
	m.resolution = &s
}

// Resolution returns the value of the "resolution" field in the mutation.
func (m *IncidentMutation) Resolution() (r string, exists bool) {
	v := m.resolution
	if v == nil {
		return
	}
	return *v, true
}

// OldResolution returns the old "resolution" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldResolution(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldResolution is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldResolution requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldResolution: %w", err)
	}
	return oldValue.Resolution, nil
}

// ClearResolution clears the value of the "resolution" field.
func (m *IncidentMutation) ClearResolution() {
	m.resolution = nil
	m.clearedFields[incident.FieldResolution] = struct{}{}
}

// ResolutionCleared returns if the "resolution" field was cleared in this mutation.
func (m *IncidentMutation) ResolutionCleared() bool {
	_, ok := m.clearedFields[incident.FieldResolution]
	return ok
}

// ResetResolution resets all changes to the "resolution" field.
func (m *IncidentMutation) ResetResolution() {
	m.resolution = nil
	delete(m.clearedFields, incident.FieldResolution)
}

// SetFrequency sets the "frequency" field.
func (m *IncidentMutation) SetFrequency(s string) {
	m.frequency = &s
}

// Frequency returns the value of the "frequency" field in the mutation.
func (m *IncidentMutation) Frequency() (r string, exists bool) {
	v := m.frequency
	if v == nil {
		return
	}
	return *v, true
}

// OldFrequency returns the old "frequency" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldFrequency(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFrequency is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFrequency requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFrequency: %w", err)
	}
	return oldValue.Frequency, nil
}

// ResetFrequency resets all changes to the "frequency" field.
func (m *IncidentMutation) ResetFrequency() {
	m.frequency = nil
}

// SetEverMuted sets the "ever_muted" field.
func (m *IncidentMutation) SetEverMuted(b bool) {
	m.ever_muted = &b
}

// EverMuted returns the value of the "ever_muted" field in the mutation.
func (m *IncidentMutation) EverMuted() (r bool, exists bool) {
	v := m.ever_muted
	if v == nil {
		return
	}
	return *v, true
}

// OldEverMuted returns the old "ever_muted" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldEverMuted(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEverMuted is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEverMuted requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEverMuted: %w", err)
	}
	return oldValue.EverMuted, nil
}

// ResetEverMuted resets all changes to the "ever_muted" field.
func (m *IncidentMutation) ResetEverMuted() {
	m.ever_muted = nil
}

// SetSnoozedBefore sets the "snoozed_before" field.
func (m *IncidentMutation) SetSnoozedBefore(i int64) {
	m.snoozed_before = &i
	m.addsnoozed_before = nil
}

// SnoozedBefore returns the value of the "snoozed_before" field in the mutation.
func (m *IncidentMutation) SnoozedBefore() (r int64, exists bool) {
	v := m.snoozed_before
	if v == nil {
		return
	}
	return *v, true
}

// OldSnoozedBefore returns the old "snoozed_before" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldSnoozedBefore(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSnoozedBefore is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSnoozedBefore requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSnoozedBefore: %w", err)
	}
	return oldValue.SnoozedBefore, nil
}

// AddSnoozedBefore adds i to the "snoozed_before" field.
func (m *IncidentMutation) AddSnoozedBefore(i int64) {
	if m.addsnoozed_before != nil {
		*m.addsnoozed_before += i
	} else {
		m.addsnoozed_before = &i
	}
}

// AddedSnoozedBefore returns the value that was added to the "snoozed_before" field in this mutation.
func (m *IncidentMutation) AddedSnoozedBefore() (r int64, exists bool) {
	v := m.addsnoozed_before
	if v == nil {
		return
	}
	return *v, true
}

// ResetSnoozedBefore resets all changes to the "snoozed_before" field.
func (m *IncidentMutation) ResetSnoozedBefore() {
	m.snoozed_before = nil
	m.addsnoozed_before = nil
}

// SetGroupMethod sets the "group_method" field.
func (m *IncidentMutation) SetGroupMethod(im incident.GroupMethod) {
	m.group_method = &im
}

// GroupMethod returns the value of the "group_method" field in the mutation.
func (m *IncidentMutation) GroupMethod() (r incident.GroupMethod, exists bool) {
	v := m.group_method
	if v == nil {
		return
	}
	return *v, true
}

// OldGroupMethod returns the old "group_method" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldGroupMethod(ctx context.Context) (v incident.GroupMethod, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldGroupMethod is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldGroupMethod requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldGroupMethod: %w", err)
	}
	return oldValue.GroupMethod, nil
}

// ResetGroupMethod resets all changes to the "group_method" field.
func (m *IncidentMutation) ResetGroupMethod() {
	m.group_method = nil
}

// SetAlertCnt sets the "alert_cnt" field.
func (m *IncidentMutation) SetAlertCnt(i int) {
	m.alert_cnt = &i
	m.addalert_cnt = nil
}

// AlertCnt returns the value of the "alert_cnt" field in the mutation.
func (m *IncidentMutation) AlertCnt() (r int, exists bool) {
	v := m.alert_cnt
	if v == nil {
		return
	}
	return *v, true
}

// OldAlertCnt returns the old "alert_cnt" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldAlertCnt(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAlertCnt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAlertCnt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAlertCnt: %w", err)
	}
	return oldValue.AlertCnt, nil
}

// AddAlertCnt adds i to the "alert_cnt" field.
func (m *IncidentMutation) AddAlertCnt(i int) {
	if m.addalert_cnt != nil {
		*m.addalert_cnt += i
	} else {
		m.addalert_cnt = &i
	}
}

// AddedAlertCnt returns the value that was added to the "alert_cnt" field in this mutation.
func (m *IncidentMutation) AddedAlertCnt() (r int, exists bool) {
	v := m.addalert_cnt
	if v == nil {
		return
	}
	return *v, true
}

// ResetAlertCnt resets all changes to the "alert_cnt" field.
func (m *IncidentMutation) ResetAlertCnt() {
	m.alert_cnt = nil
	m.addalert_cnt = nil
}

// SetIntegrationsID sets the "integrations_id" field.
func (m *IncidentMutation) SetIntegrationsID(u uuid.UUID) {
	m.integrations_id = &u
}

// IntegrationsID returns the value of the "integrations_id" field in the mutation.
func (m *IncidentMutation) IntegrationsID() (r uuid.UUID, exists bool) {
	v := m.integrations_id
	if v == nil {
		return
	}
	return *v, true
}

// OldIntegrationsID returns the old "integrations_id" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldIntegrationsID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIntegrationsID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIntegrationsID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIntegrationsID: %w", err)
	}
	return oldValue.IntegrationsID, nil
}

// ResetIntegrationsID resets all changes to the "integrations_id" field.
func (m *IncidentMutation) ResetIntegrationsID() {
	m.integrations_id = nil
}

// SetSpaceID sets the "space_id" field.
func (m *IncidentMutation) SetSpaceID(s string) {
	m.space_id = &s
}

// SpaceID returns the value of the "space_id" field in the mutation.
func (m *IncidentMutation) SpaceID() (r string, exists bool) {
	v := m.space_id
	if v == nil {
		return
	}
	return *v, true
}

// OldSpaceID returns the old "space_id" field's value of the Incident entity.
// If the Incident object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IncidentMutation) OldSpaceID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSpaceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSpaceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSpaceID: %w", err)
	}
	return oldValue.SpaceID, nil
}

// ResetSpaceID resets all changes to the "space_id" field.
func (m *IncidentMutation) ResetSpaceID() {
	m.space_id = nil
}

// AddAlertIDs adds the "alerts" edge to the Alert entity by ids.
func (m *IncidentMutation) AddAlertIDs(ids ...uuid.UUID) {
	if m.alerts == nil {
		m.alerts = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.alerts[ids[i]] = struct{}{}
	}
}

// ClearAlerts clears the "alerts" edge to the Alert entity.
func (m *IncidentMutation) ClearAlerts() {
	m.clearedalerts = true
}

// AlertsCleared reports if the "alerts" edge to the Alert entity was cleared.
func (m *IncidentMutation) AlertsCleared() bool {
	return m.clearedalerts
}

// RemoveAlertIDs removes the "alerts" edge to the Alert entity by IDs.
func (m *IncidentMutation) RemoveAlertIDs(ids ...uuid.UUID) {
	if m.removedalerts == nil {
		m.removedalerts = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.alerts, ids[i])
		m.removedalerts[ids[i]] = struct{}{}
	}
}

// RemovedAlerts returns the removed IDs of the "alerts" edge to the Alert entity.
func (m *IncidentMutation) RemovedAlertsIDs() (ids []uuid.UUID) {
	for id := range m.removedalerts {
		ids = append(ids, id)
	}
	return
}

// AlertsIDs returns the "alerts" edge IDs in the mutation.
func (m *IncidentMutation) AlertsIDs() (ids []uuid.UUID) {
	for id := range m.alerts {
		ids = append(ids, id)
	}
	return
}

// ResetAlerts resets all changes to the "alerts" edge.
func (m *IncidentMutation) ResetAlerts() {
	m.alerts = nil
	m.clearedalerts = false
	m.removedalerts = nil
}

// Where appends a list predicates to the IncidentMutation builder.
func (m *IncidentMutation) Where(ps ...predicate.Incident) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the IncidentMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *IncidentMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Incident, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *IncidentMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *IncidentMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Incident).
func (m *IncidentMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *IncidentMutation) Fields() []string {
	fields := make([]string, 0, 25)
	if m.created_at != nil {
		fields = append(fields, incident.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, incident.FieldUpdatedAt)
	}
	if m.dedup_key != nil {
		fields = append(fields, incident.FieldDedupKey)
	}
	if m.title != nil {
		fields = append(fields, incident.FieldTitle)
	}
	if m.description != nil {
		fields = append(fields, incident.FieldDescription)
	}
	if m.incident_severity != nil {
		fields = append(fields, incident.FieldIncidentSeverity)
	}
	if m.incident_status != nil {
		fields = append(fields, incident.FieldIncidentStatus)
	}
	if m.progress != nil {
		fields = append(fields, incident.FieldProgress)
	}
	if m.start_time != nil {
		fields = append(fields, incident.FieldStartTime)
	}
	if m.last_time != nil {
		fields = append(fields, incident.FieldLastTime)
	}
	if m.end_time != nil {
		fields = append(fields, incident.FieldEndTime)
	}
	if m.ack_time != nil {
		fields = append(fields, incident.FieldAckTime)
	}
	if m.close_time != nil {
		fields = append(fields, incident.FieldCloseTime)
	}
	if m.creator_by != nil {
		fields = append(fields, incident.FieldCreatorBy)
	}
	if m.close_by != nil {
		fields = append(fields, incident.FieldCloseBy)
	}
	if m.labels != nil {
		fields = append(fields, incident.FieldLabels)
	}
	if m.root_cause != nil {
		fields = append(fields, incident.FieldRootCause)
	}
	if m.resolution != nil {
		fields = append(fields, incident.FieldResolution)
	}
	if m.frequency != nil {
		fields = append(fields, incident.FieldFrequency)
	}
	if m.ever_muted != nil {
		fields = append(fields, incident.FieldEverMuted)
	}
	if m.snoozed_before != nil {
		fields = append(fields, incident.FieldSnoozedBefore)
	}
	if m.group_method != nil {
		fields = append(fields, incident.FieldGroupMethod)
	}
	if m.alert_cnt != nil {
		fields = append(fields, incident.FieldAlertCnt)
	}
	if m.integrations_id != nil {
		fields = append(fields, incident.FieldIntegrationsID)
	}
	if m.space_id != nil {
		fields = append(fields, incident.FieldSpaceID)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *IncidentMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case incident.FieldCreatedAt:
		return m.CreatedAt()
	case incident.FieldUpdatedAt:
		return m.UpdatedAt()
	case incident.FieldDedupKey:
		return m.DedupKey()
	case incident.FieldTitle:
		return m.Title()
	case incident.FieldDescription:
		return m.Description()
	case incident.FieldIncidentSeverity:
		return m.IncidentSeverity()
	case incident.FieldIncidentStatus:
		return m.IncidentStatus()
	case incident.FieldProgress:
		return m.Progress()
	case incident.FieldStartTime:
		return m.StartTime()
	case incident.FieldLastTime:
		return m.LastTime()
	case incident.FieldEndTime:
		return m.EndTime()
	case incident.FieldAckTime:
		return m.AckTime()
	case incident.FieldCloseTime:
		return m.CloseTime()
	case incident.FieldCreatorBy:
		return m.CreatorBy()
	case incident.FieldCloseBy:
		return m.CloseBy()
	case incident.FieldLabels:
		return m.Labels()
	case incident.FieldRootCause:
		return m.RootCause()
	case incident.FieldResolution:
		return m.Resolution()
	case incident.FieldFrequency:
		return m.Frequency()
	case incident.FieldEverMuted:
		return m.EverMuted()
	case incident.FieldSnoozedBefore:
		return m.SnoozedBefore()
	case incident.FieldGroupMethod:
		return m.GroupMethod()
	case incident.FieldAlertCnt:
		return m.AlertCnt()
	case incident.FieldIntegrationsID:
		return m.IntegrationsID()
	case incident.FieldSpaceID:
		return m.SpaceID()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *IncidentMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case incident.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case incident.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case incident.FieldDedupKey:
		return m.OldDedupKey(ctx)
	case incident.FieldTitle:
		return m.OldTitle(ctx)
	case incident.FieldDescription:
		return m.OldDescription(ctx)
	case incident.FieldIncidentSeverity:
		return m.OldIncidentSeverity(ctx)
	case incident.FieldIncidentStatus:
		return m.OldIncidentStatus(ctx)
	case incident.FieldProgress:
		return m.OldProgress(ctx)
	case incident.FieldStartTime:
		return m.OldStartTime(ctx)
	case incident.FieldLastTime:
		return m.OldLastTime(ctx)
	case incident.FieldEndTime:
		return m.OldEndTime(ctx)
	case incident.FieldAckTime:
		return m.OldAckTime(ctx)
	case incident.FieldCloseTime:
		return m.OldCloseTime(ctx)
	case incident.FieldCreatorBy:
		return m.OldCreatorBy(ctx)
	case incident.FieldCloseBy:
		return m.OldCloseBy(ctx)
	case incident.FieldLabels:
		return m.OldLabels(ctx)
	case incident.FieldRootCause:
		return m.OldRootCause(ctx)
	case incident.FieldResolution:
		return m.OldResolution(ctx)
	case incident.FieldFrequency:
		return m.OldFrequency(ctx)
	case incident.FieldEverMuted:
		return m.OldEverMuted(ctx)
	case incident.FieldSnoozedBefore:
		return m.OldSnoozedBefore(ctx)
	case incident.FieldGroupMethod:
		return m.OldGroupMethod(ctx)
	case incident.FieldAlertCnt:
		return m.OldAlertCnt(ctx)
	case incident.FieldIntegrationsID:
		return m.OldIntegrationsID(ctx)
	case incident.FieldSpaceID:
		return m.OldSpaceID(ctx)
	}
	return nil, fmt.Errorf("unknown Incident field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *IncidentMutation) SetField(name string, value ent.Value) error {
	switch name {
	case incident.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case incident.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case incident.FieldDedupKey:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDedupKey(v)
		return nil
	case incident.FieldTitle:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTitle(v)
		return nil
	case incident.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case incident.FieldIncidentSeverity:
		v, ok := value.(incident.IncidentSeverity)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIncidentSeverity(v)
		return nil
	case incident.FieldIncidentStatus:
		v, ok := value.(incident.IncidentStatus)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIncidentStatus(v)
		return nil
	case incident.FieldProgress:
		v, ok := value.(incident.Progress)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetProgress(v)
		return nil
	case incident.FieldStartTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStartTime(v)
		return nil
	case incident.FieldLastTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLastTime(v)
		return nil
	case incident.FieldEndTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEndTime(v)
		return nil
	case incident.FieldAckTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAckTime(v)
		return nil
	case incident.FieldCloseTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCloseTime(v)
		return nil
	case incident.FieldCreatorBy:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatorBy(v)
		return nil
	case incident.FieldCloseBy:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCloseBy(v)
		return nil
	case incident.FieldLabels:
		v, ok := value.(map[string]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLabels(v)
		return nil
	case incident.FieldRootCause:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRootCause(v)
		return nil
	case incident.FieldResolution:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetResolution(v)
		return nil
	case incident.FieldFrequency:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFrequency(v)
		return nil
	case incident.FieldEverMuted:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEverMuted(v)
		return nil
	case incident.FieldSnoozedBefore:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSnoozedBefore(v)
		return nil
	case incident.FieldGroupMethod:
		v, ok := value.(incident.GroupMethod)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetGroupMethod(v)
		return nil
	case incident.FieldAlertCnt:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAlertCnt(v)
		return nil
	case incident.FieldIntegrationsID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIntegrationsID(v)
		return nil
	case incident.FieldSpaceID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSpaceID(v)
		return nil
	}
	return fmt.Errorf("unknown Incident field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *IncidentMutation) AddedFields() []string {
	var fields []string
	if m.addsnoozed_before != nil {
		fields = append(fields, incident.FieldSnoozedBefore)
	}
	if m.addalert_cnt != nil {
		fields = append(fields, incident.FieldAlertCnt)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *IncidentMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case incident.FieldSnoozedBefore:
		return m.AddedSnoozedBefore()
	case incident.FieldAlertCnt:
		return m.AddedAlertCnt()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *IncidentMutation) AddField(name string, value ent.Value) error {
	switch name {
	case incident.FieldSnoozedBefore:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddSnoozedBefore(v)
		return nil
	case incident.FieldAlertCnt:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddAlertCnt(v)
		return nil
	}
	return fmt.Errorf("unknown Incident numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *IncidentMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(incident.FieldDescription) {
		fields = append(fields, incident.FieldDescription)
	}
	if m.FieldCleared(incident.FieldEndTime) {
		fields = append(fields, incident.FieldEndTime)
	}
	if m.FieldCleared(incident.FieldAckTime) {
		fields = append(fields, incident.FieldAckTime)
	}
	if m.FieldCleared(incident.FieldCloseTime) {
		fields = append(fields, incident.FieldCloseTime)
	}
	if m.FieldCleared(incident.FieldCloseBy) {
		fields = append(fields, incident.FieldCloseBy)
	}
	if m.FieldCleared(incident.FieldRootCause) {
		fields = append(fields, incident.FieldRootCause)
	}
	if m.FieldCleared(incident.FieldResolution) {
		fields = append(fields, incident.FieldResolution)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *IncidentMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *IncidentMutation) ClearField(name string) error {
	switch name {
	case incident.FieldDescription:
		m.ClearDescription()
		return nil
	case incident.FieldEndTime:
		m.ClearEndTime()
		return nil
	case incident.FieldAckTime:
		m.ClearAckTime()
		return nil
	case incident.FieldCloseTime:
		m.ClearCloseTime()
		return nil
	case incident.FieldCloseBy:
		m.ClearCloseBy()
		return nil
	case incident.FieldRootCause:
		m.ClearRootCause()
		return nil
	case incident.FieldResolution:
		m.ClearResolution()
		return nil
	}
	return fmt.Errorf("unknown Incident nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *IncidentMutation) ResetField(name string) error {
	switch name {
	case incident.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case incident.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case incident.FieldDedupKey:
		m.ResetDedupKey()
		return nil
	case incident.FieldTitle:
		m.ResetTitle()
		return nil
	case incident.FieldDescription:
		m.ResetDescription()
		return nil
	case incident.FieldIncidentSeverity:
		m.ResetIncidentSeverity()
		return nil
	case incident.FieldIncidentStatus:
		m.ResetIncidentStatus()
		return nil
	case incident.FieldProgress:
		m.ResetProgress()
		return nil
	case incident.FieldStartTime:
		m.ResetStartTime()
		return nil
	case incident.FieldLastTime:
		m.ResetLastTime()
		return nil
	case incident.FieldEndTime:
		m.ResetEndTime()
		return nil
	case incident.FieldAckTime:
		m.ResetAckTime()
		return nil
	case incident.FieldCloseTime:
		m.ResetCloseTime()
		return nil
	case incident.FieldCreatorBy:
		m.ResetCreatorBy()
		return nil
	case incident.FieldCloseBy:
		m.ResetCloseBy()
		return nil
	case incident.FieldLabels:
		m.ResetLabels()
		return nil
	case incident.FieldRootCause:
		m.ResetRootCause()
		return nil
	case incident.FieldResolution:
		m.ResetResolution()
		return nil
	case incident.FieldFrequency:
		m.ResetFrequency()
		return nil
	case incident.FieldEverMuted:
		m.ResetEverMuted()
		return nil
	case incident.FieldSnoozedBefore:
		m.ResetSnoozedBefore()
		return nil
	case incident.FieldGroupMethod:
		m.ResetGroupMethod()
		return nil
	case incident.FieldAlertCnt:
		m.ResetAlertCnt()
		return nil
	case incident.FieldIntegrationsID:
		m.ResetIntegrationsID()
		return nil
	case incident.FieldSpaceID:
		m.ResetSpaceID()
		return nil
	}
	return fmt.Errorf("unknown Incident field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *IncidentMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.alerts != nil {
		edges = append(edges, incident.EdgeAlerts)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *IncidentMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case incident.EdgeAlerts:
		ids := make([]ent.Value, 0, len(m.alerts))
		for id := range m.alerts {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *IncidentMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	if m.removedalerts != nil {
		edges = append(edges, incident.EdgeAlerts)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *IncidentMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case incident.EdgeAlerts:
		ids := make([]ent.Value, 0, len(m.removedalerts))
		for id := range m.removedalerts {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *IncidentMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedalerts {
		edges = append(edges, incident.EdgeAlerts)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *IncidentMutation) EdgeCleared(name string) bool {
	switch name {
	case incident.EdgeAlerts:
		return m.clearedalerts
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *IncidentMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown Incident unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *IncidentMutation) ResetEdge(name string) error {
	switch name {
	case incident.EdgeAlerts:
		m.ResetAlerts()
		return nil
	}
	return fmt.Errorf("unknown Incident edge %s", name)
}

// RawAlertMutation represents an operation that mutates the RawAlert nodes in the graph.
type RawAlertMutation struct {
	config
	op              Op
	typ             string
	id              *uuid.UUID
	created_at      *time.Time
	updated_at      *time.Time
	raw_data        *string
	integrations_id *string
	status          *string
	message         *string
	clearedFields   map[string]struct{}
	alerts          *uuid.UUID
	clearedalerts   bool
	done            bool
	oldValue        func(context.Context) (*RawAlert, error)
	predicates      []predicate.RawAlert
}

var _ ent.Mutation = (*RawAlertMutation)(nil)

// rawalertOption allows management of the mutation configuration using functional options.
type rawalertOption func(*RawAlertMutation)

// newRawAlertMutation creates new mutation for the RawAlert entity.
func newRawAlertMutation(c config, op Op, opts ...rawalertOption) *RawAlertMutation {
	m := &RawAlertMutation{
		config:        c,
		op:            op,
		typ:           TypeRawAlert,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withRawAlertID sets the ID field of the mutation.
func withRawAlertID(id uuid.UUID) rawalertOption {
	return func(m *RawAlertMutation) {
		var (
			err   error
			once  sync.Once
			value *RawAlert
		)
		m.oldValue = func(ctx context.Context) (*RawAlert, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().RawAlert.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withRawAlert sets the old RawAlert of the mutation.
func withRawAlert(node *RawAlert) rawalertOption {
	return func(m *RawAlertMutation) {
		m.oldValue = func(context.Context) (*RawAlert, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m RawAlertMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m RawAlertMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of RawAlert entities.
func (m *RawAlertMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *RawAlertMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *RawAlertMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().RawAlert.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *RawAlertMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *RawAlertMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the RawAlert entity.
// If the RawAlert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RawAlertMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *RawAlertMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *RawAlertMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *RawAlertMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the RawAlert entity.
// If the RawAlert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RawAlertMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *RawAlertMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetRawData sets the "raw_data" field.
func (m *RawAlertMutation) SetRawData(s string) {
	m.raw_data = &s
}

// RawData returns the value of the "raw_data" field in the mutation.
func (m *RawAlertMutation) RawData() (r string, exists bool) {
	v := m.raw_data
	if v == nil {
		return
	}
	return *v, true
}

// OldRawData returns the old "raw_data" field's value of the RawAlert entity.
// If the RawAlert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RawAlertMutation) OldRawData(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRawData is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRawData requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRawData: %w", err)
	}
	return oldValue.RawData, nil
}

// ResetRawData resets all changes to the "raw_data" field.
func (m *RawAlertMutation) ResetRawData() {
	m.raw_data = nil
}

// SetIntegrationsID sets the "integrations_id" field.
func (m *RawAlertMutation) SetIntegrationsID(s string) {
	m.integrations_id = &s
}

// IntegrationsID returns the value of the "integrations_id" field in the mutation.
func (m *RawAlertMutation) IntegrationsID() (r string, exists bool) {
	v := m.integrations_id
	if v == nil {
		return
	}
	return *v, true
}

// OldIntegrationsID returns the old "integrations_id" field's value of the RawAlert entity.
// If the RawAlert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RawAlertMutation) OldIntegrationsID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIntegrationsID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIntegrationsID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIntegrationsID: %w", err)
	}
	return oldValue.IntegrationsID, nil
}

// ResetIntegrationsID resets all changes to the "integrations_id" field.
func (m *RawAlertMutation) ResetIntegrationsID() {
	m.integrations_id = nil
}

// SetStatus sets the "status" field.
func (m *RawAlertMutation) SetStatus(s string) {
	m.status = &s
}

// Status returns the value of the "status" field in the mutation.
func (m *RawAlertMutation) Status() (r string, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the RawAlert entity.
// If the RawAlert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RawAlertMutation) OldStatus(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *RawAlertMutation) ResetStatus() {
	m.status = nil
}

// SetMessage sets the "message" field.
func (m *RawAlertMutation) SetMessage(s string) {
	m.message = &s
}

// Message returns the value of the "message" field in the mutation.
func (m *RawAlertMutation) Message() (r string, exists bool) {
	v := m.message
	if v == nil {
		return
	}
	return *v, true
}

// OldMessage returns the old "message" field's value of the RawAlert entity.
// If the RawAlert object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RawAlertMutation) OldMessage(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMessage is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMessage requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMessage: %w", err)
	}
	return oldValue.Message, nil
}

// ClearMessage clears the value of the "message" field.
func (m *RawAlertMutation) ClearMessage() {
	m.message = nil
	m.clearedFields[rawalert.FieldMessage] = struct{}{}
}

// MessageCleared returns if the "message" field was cleared in this mutation.
func (m *RawAlertMutation) MessageCleared() bool {
	_, ok := m.clearedFields[rawalert.FieldMessage]
	return ok
}

// ResetMessage resets all changes to the "message" field.
func (m *RawAlertMutation) ResetMessage() {
	m.message = nil
	delete(m.clearedFields, rawalert.FieldMessage)
}

// SetAlertsID sets the "alerts" edge to the Alert entity by id.
func (m *RawAlertMutation) SetAlertsID(id uuid.UUID) {
	m.alerts = &id
}

// ClearAlerts clears the "alerts" edge to the Alert entity.
func (m *RawAlertMutation) ClearAlerts() {
	m.clearedalerts = true
}

// AlertsCleared reports if the "alerts" edge to the Alert entity was cleared.
func (m *RawAlertMutation) AlertsCleared() bool {
	return m.clearedalerts
}

// AlertsID returns the "alerts" edge ID in the mutation.
func (m *RawAlertMutation) AlertsID() (id uuid.UUID, exists bool) {
	if m.alerts != nil {
		return *m.alerts, true
	}
	return
}

// AlertsIDs returns the "alerts" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// AlertsID instead. It exists only for internal usage by the builders.
func (m *RawAlertMutation) AlertsIDs() (ids []uuid.UUID) {
	if id := m.alerts; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetAlerts resets all changes to the "alerts" edge.
func (m *RawAlertMutation) ResetAlerts() {
	m.alerts = nil
	m.clearedalerts = false
}

// Where appends a list predicates to the RawAlertMutation builder.
func (m *RawAlertMutation) Where(ps ...predicate.RawAlert) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the RawAlertMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *RawAlertMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.RawAlert, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *RawAlertMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *RawAlertMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (RawAlert).
func (m *RawAlertMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *RawAlertMutation) Fields() []string {
	fields := make([]string, 0, 6)
	if m.created_at != nil {
		fields = append(fields, rawalert.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, rawalert.FieldUpdatedAt)
	}
	if m.raw_data != nil {
		fields = append(fields, rawalert.FieldRawData)
	}
	if m.integrations_id != nil {
		fields = append(fields, rawalert.FieldIntegrationsID)
	}
	if m.status != nil {
		fields = append(fields, rawalert.FieldStatus)
	}
	if m.message != nil {
		fields = append(fields, rawalert.FieldMessage)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *RawAlertMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case rawalert.FieldCreatedAt:
		return m.CreatedAt()
	case rawalert.FieldUpdatedAt:
		return m.UpdatedAt()
	case rawalert.FieldRawData:
		return m.RawData()
	case rawalert.FieldIntegrationsID:
		return m.IntegrationsID()
	case rawalert.FieldStatus:
		return m.Status()
	case rawalert.FieldMessage:
		return m.Message()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *RawAlertMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case rawalert.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case rawalert.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case rawalert.FieldRawData:
		return m.OldRawData(ctx)
	case rawalert.FieldIntegrationsID:
		return m.OldIntegrationsID(ctx)
	case rawalert.FieldStatus:
		return m.OldStatus(ctx)
	case rawalert.FieldMessage:
		return m.OldMessage(ctx)
	}
	return nil, fmt.Errorf("unknown RawAlert field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *RawAlertMutation) SetField(name string, value ent.Value) error {
	switch name {
	case rawalert.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case rawalert.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case rawalert.FieldRawData:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRawData(v)
		return nil
	case rawalert.FieldIntegrationsID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIntegrationsID(v)
		return nil
	case rawalert.FieldStatus:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case rawalert.FieldMessage:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMessage(v)
		return nil
	}
	return fmt.Errorf("unknown RawAlert field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *RawAlertMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *RawAlertMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *RawAlertMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown RawAlert numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *RawAlertMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(rawalert.FieldMessage) {
		fields = append(fields, rawalert.FieldMessage)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *RawAlertMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *RawAlertMutation) ClearField(name string) error {
	switch name {
	case rawalert.FieldMessage:
		m.ClearMessage()
		return nil
	}
	return fmt.Errorf("unknown RawAlert nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *RawAlertMutation) ResetField(name string) error {
	switch name {
	case rawalert.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case rawalert.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case rawalert.FieldRawData:
		m.ResetRawData()
		return nil
	case rawalert.FieldIntegrationsID:
		m.ResetIntegrationsID()
		return nil
	case rawalert.FieldStatus:
		m.ResetStatus()
		return nil
	case rawalert.FieldMessage:
		m.ResetMessage()
		return nil
	}
	return fmt.Errorf("unknown RawAlert field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *RawAlertMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.alerts != nil {
		edges = append(edges, rawalert.EdgeAlerts)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *RawAlertMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case rawalert.EdgeAlerts:
		if id := m.alerts; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *RawAlertMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *RawAlertMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *RawAlertMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedalerts {
		edges = append(edges, rawalert.EdgeAlerts)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *RawAlertMutation) EdgeCleared(name string) bool {
	switch name {
	case rawalert.EdgeAlerts:
		return m.clearedalerts
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *RawAlertMutation) ClearEdge(name string) error {
	switch name {
	case rawalert.EdgeAlerts:
		m.ClearAlerts()
		return nil
	}
	return fmt.Errorf("unknown RawAlert unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *RawAlertMutation) ResetEdge(name string) error {
	switch name {
	case rawalert.EdgeAlerts:
		m.ResetAlerts()
		return nil
	}
	return fmt.Errorf("unknown RawAlert edge %s", name)
}
