// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	uuid "github.com/gofrs/uuid/v5"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/alert"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/incident"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/rawalert"
)

// AlertCreate is the builder for creating a Alert entity.
type AlertCreate struct {
	config
	mutation *AlertMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (ac *AlertCreate) SetCreatedAt(t time.Time) *AlertCreate {
	ac.mutation.SetCreatedAt(t)
	return ac
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ac *AlertCreate) SetNillableCreatedAt(t *time.Time) *AlertCreate {
	if t != nil {
		ac.SetCreatedAt(*t)
	}
	return ac
}

// SetUpdatedAt sets the "updated_at" field.
func (ac *AlertCreate) SetUpdatedAt(t time.Time) *AlertCreate {
	ac.mutation.SetUpdatedAt(t)
	return ac
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ac *AlertCreate) SetNillableUpdatedAt(t *time.Time) *AlertCreate {
	if t != nil {
		ac.SetUpdatedAt(*t)
	}
	return ac
}

// SetRawAlertRefID sets the "raw_alert_ref_id" field.
func (ac *AlertCreate) SetRawAlertRefID(s string) *AlertCreate {
	ac.mutation.SetRawAlertRefID(s)
	return ac
}

// SetNillableRawAlertRefID sets the "raw_alert_ref_id" field if the given value is not nil.
func (ac *AlertCreate) SetNillableRawAlertRefID(s *string) *AlertCreate {
	if s != nil {
		ac.SetRawAlertRefID(*s)
	}
	return ac
}

// SetTitle sets the "title" field.
func (ac *AlertCreate) SetTitle(s string) *AlertCreate {
	ac.mutation.SetTitle(s)
	return ac
}

// SetDescription sets the "description" field.
func (ac *AlertCreate) SetDescription(s string) *AlertCreate {
	ac.mutation.SetDescription(s)
	return ac
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (ac *AlertCreate) SetNillableDescription(s *string) *AlertCreate {
	if s != nil {
		ac.SetDescription(*s)
	}
	return ac
}

// SetAlertSeverity sets the "alert_severity" field.
func (ac *AlertCreate) SetAlertSeverity(as alert.AlertSeverity) *AlertCreate {
	ac.mutation.SetAlertSeverity(as)
	return ac
}

// SetAlertStatus sets the "alert_status" field.
func (ac *AlertCreate) SetAlertStatus(as alert.AlertStatus) *AlertCreate {
	ac.mutation.SetAlertStatus(as)
	return ac
}

// SetProgress sets the "progress" field.
func (ac *AlertCreate) SetProgress(a alert.Progress) *AlertCreate {
	ac.mutation.SetProgress(a)
	return ac
}

// SetAlertKey sets the "alert_key" field.
func (ac *AlertCreate) SetAlertKey(s string) *AlertCreate {
	ac.mutation.SetAlertKey(s)
	return ac
}

// SetStartTime sets the "start_time" field.
func (ac *AlertCreate) SetStartTime(t time.Time) *AlertCreate {
	ac.mutation.SetStartTime(t)
	return ac
}

// SetLastTime sets the "last_time" field.
func (ac *AlertCreate) SetLastTime(t time.Time) *AlertCreate {
	ac.mutation.SetLastTime(t)
	return ac
}

// SetEndTime sets the "end_time" field.
func (ac *AlertCreate) SetEndTime(t time.Time) *AlertCreate {
	ac.mutation.SetEndTime(t)
	return ac
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (ac *AlertCreate) SetNillableEndTime(t *time.Time) *AlertCreate {
	if t != nil {
		ac.SetEndTime(*t)
	}
	return ac
}

// SetAckTime sets the "ack_time" field.
func (ac *AlertCreate) SetAckTime(t time.Time) *AlertCreate {
	ac.mutation.SetAckTime(t)
	return ac
}

// SetNillableAckTime sets the "ack_time" field if the given value is not nil.
func (ac *AlertCreate) SetNillableAckTime(t *time.Time) *AlertCreate {
	if t != nil {
		ac.SetAckTime(*t)
	}
	return ac
}

// SetCloseTime sets the "close_time" field.
func (ac *AlertCreate) SetCloseTime(t time.Time) *AlertCreate {
	ac.mutation.SetCloseTime(t)
	return ac
}

// SetNillableCloseTime sets the "close_time" field if the given value is not nil.
func (ac *AlertCreate) SetNillableCloseTime(t *time.Time) *AlertCreate {
	if t != nil {
		ac.SetCloseTime(*t)
	}
	return ac
}

// SetLabels sets the "labels" field.
func (ac *AlertCreate) SetLabels(m map[string]string) *AlertCreate {
	ac.mutation.SetLabels(m)
	return ac
}

// SetEverMuted sets the "ever_muted" field.
func (ac *AlertCreate) SetEverMuted(b bool) *AlertCreate {
	ac.mutation.SetEverMuted(b)
	return ac
}

// SetNillableEverMuted sets the "ever_muted" field if the given value is not nil.
func (ac *AlertCreate) SetNillableEverMuted(b *bool) *AlertCreate {
	if b != nil {
		ac.SetEverMuted(*b)
	}
	return ac
}

// SetEventCnt sets the "event_cnt" field.
func (ac *AlertCreate) SetEventCnt(i int) *AlertCreate {
	ac.mutation.SetEventCnt(i)
	return ac
}

// SetNillableEventCnt sets the "event_cnt" field if the given value is not nil.
func (ac *AlertCreate) SetNillableEventCnt(i *int) *AlertCreate {
	if i != nil {
		ac.SetEventCnt(*i)
	}
	return ac
}

// SetRawData sets the "raw_data" field.
func (ac *AlertCreate) SetRawData(m map[string]interface{}) *AlertCreate {
	ac.mutation.SetRawData(m)
	return ac
}

// SetIntegrationsID sets the "integrations_id" field.
func (ac *AlertCreate) SetIntegrationsID(u uuid.UUID) *AlertCreate {
	ac.mutation.SetIntegrationsID(u)
	return ac
}

// SetSpaceID sets the "space_id" field.
func (ac *AlertCreate) SetSpaceID(s string) *AlertCreate {
	ac.mutation.SetSpaceID(s)
	return ac
}

// SetIncidentID sets the "incident_id" field.
func (ac *AlertCreate) SetIncidentID(s string) *AlertCreate {
	ac.mutation.SetIncidentID(s)
	return ac
}

// SetNillableIncidentID sets the "incident_id" field if the given value is not nil.
func (ac *AlertCreate) SetNillableIncidentID(s *string) *AlertCreate {
	if s != nil {
		ac.SetIncidentID(*s)
	}
	return ac
}

// SetID sets the "id" field.
func (ac *AlertCreate) SetID(u uuid.UUID) *AlertCreate {
	ac.mutation.SetID(u)
	return ac
}

// SetNillableID sets the "id" field if the given value is not nil.
func (ac *AlertCreate) SetNillableID(u *uuid.UUID) *AlertCreate {
	if u != nil {
		ac.SetID(*u)
	}
	return ac
}

// SetRawAlertID sets the "raw_alert" edge to the RawAlert entity by ID.
func (ac *AlertCreate) SetRawAlertID(id uuid.UUID) *AlertCreate {
	ac.mutation.SetRawAlertID(id)
	return ac
}

// SetNillableRawAlertID sets the "raw_alert" edge to the RawAlert entity by ID if the given value is not nil.
func (ac *AlertCreate) SetNillableRawAlertID(id *uuid.UUID) *AlertCreate {
	if id != nil {
		ac = ac.SetRawAlertID(*id)
	}
	return ac
}

// SetRawAlert sets the "raw_alert" edge to the RawAlert entity.
func (ac *AlertCreate) SetRawAlert(r *RawAlert) *AlertCreate {
	return ac.SetRawAlertID(r.ID)
}

// AddIncidentIDs adds the "incident" edge to the Incident entity by IDs.
func (ac *AlertCreate) AddIncidentIDs(ids ...uuid.UUID) *AlertCreate {
	ac.mutation.AddIncidentIDs(ids...)
	return ac
}

// AddIncident adds the "incident" edges to the Incident entity.
func (ac *AlertCreate) AddIncident(i ...*Incident) *AlertCreate {
	ids := make([]uuid.UUID, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return ac.AddIncidentIDs(ids...)
}

// Mutation returns the AlertMutation object of the builder.
func (ac *AlertCreate) Mutation() *AlertMutation {
	return ac.mutation
}

// Save creates the Alert in the database.
func (ac *AlertCreate) Save(ctx context.Context) (*Alert, error) {
	ac.defaults()
	return withHooks(ctx, ac.sqlSave, ac.mutation, ac.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ac *AlertCreate) SaveX(ctx context.Context) *Alert {
	v, err := ac.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ac *AlertCreate) Exec(ctx context.Context) error {
	_, err := ac.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ac *AlertCreate) ExecX(ctx context.Context) {
	if err := ac.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ac *AlertCreate) defaults() {
	if _, ok := ac.mutation.CreatedAt(); !ok {
		v := alert.DefaultCreatedAt()
		ac.mutation.SetCreatedAt(v)
	}
	if _, ok := ac.mutation.UpdatedAt(); !ok {
		v := alert.DefaultUpdatedAt()
		ac.mutation.SetUpdatedAt(v)
	}
	if _, ok := ac.mutation.EverMuted(); !ok {
		v := alert.DefaultEverMuted
		ac.mutation.SetEverMuted(v)
	}
	if _, ok := ac.mutation.EventCnt(); !ok {
		v := alert.DefaultEventCnt
		ac.mutation.SetEventCnt(v)
	}
	if _, ok := ac.mutation.ID(); !ok {
		v := alert.DefaultID()
		ac.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ac *AlertCreate) check() error {
	if _, ok := ac.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Alert.created_at"`)}
	}
	if _, ok := ac.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Alert.updated_at"`)}
	}
	if _, ok := ac.mutation.Title(); !ok {
		return &ValidationError{Name: "title", err: errors.New(`ent: missing required field "Alert.title"`)}
	}
	if _, ok := ac.mutation.AlertSeverity(); !ok {
		return &ValidationError{Name: "alert_severity", err: errors.New(`ent: missing required field "Alert.alert_severity"`)}
	}
	if v, ok := ac.mutation.AlertSeverity(); ok {
		if err := alert.AlertSeverityValidator(v); err != nil {
			return &ValidationError{Name: "alert_severity", err: fmt.Errorf(`ent: validator failed for field "Alert.alert_severity": %w`, err)}
		}
	}
	if _, ok := ac.mutation.AlertStatus(); !ok {
		return &ValidationError{Name: "alert_status", err: errors.New(`ent: missing required field "Alert.alert_status"`)}
	}
	if v, ok := ac.mutation.AlertStatus(); ok {
		if err := alert.AlertStatusValidator(v); err != nil {
			return &ValidationError{Name: "alert_status", err: fmt.Errorf(`ent: validator failed for field "Alert.alert_status": %w`, err)}
		}
	}
	if _, ok := ac.mutation.Progress(); !ok {
		return &ValidationError{Name: "progress", err: errors.New(`ent: missing required field "Alert.progress"`)}
	}
	if v, ok := ac.mutation.Progress(); ok {
		if err := alert.ProgressValidator(v); err != nil {
			return &ValidationError{Name: "progress", err: fmt.Errorf(`ent: validator failed for field "Alert.progress": %w`, err)}
		}
	}
	if _, ok := ac.mutation.AlertKey(); !ok {
		return &ValidationError{Name: "alert_key", err: errors.New(`ent: missing required field "Alert.alert_key"`)}
	}
	if _, ok := ac.mutation.StartTime(); !ok {
		return &ValidationError{Name: "start_time", err: errors.New(`ent: missing required field "Alert.start_time"`)}
	}
	if _, ok := ac.mutation.LastTime(); !ok {
		return &ValidationError{Name: "last_time", err: errors.New(`ent: missing required field "Alert.last_time"`)}
	}
	if _, ok := ac.mutation.Labels(); !ok {
		return &ValidationError{Name: "labels", err: errors.New(`ent: missing required field "Alert.labels"`)}
	}
	if _, ok := ac.mutation.EverMuted(); !ok {
		return &ValidationError{Name: "ever_muted", err: errors.New(`ent: missing required field "Alert.ever_muted"`)}
	}
	if _, ok := ac.mutation.EventCnt(); !ok {
		return &ValidationError{Name: "event_cnt", err: errors.New(`ent: missing required field "Alert.event_cnt"`)}
	}
	if _, ok := ac.mutation.RawData(); !ok {
		return &ValidationError{Name: "raw_data", err: errors.New(`ent: missing required field "Alert.raw_data"`)}
	}
	if _, ok := ac.mutation.IntegrationsID(); !ok {
		return &ValidationError{Name: "integrations_id", err: errors.New(`ent: missing required field "Alert.integrations_id"`)}
	}
	if _, ok := ac.mutation.SpaceID(); !ok {
		return &ValidationError{Name: "space_id", err: errors.New(`ent: missing required field "Alert.space_id"`)}
	}
	return nil
}

func (ac *AlertCreate) sqlSave(ctx context.Context) (*Alert, error) {
	if err := ac.check(); err != nil {
		return nil, err
	}
	_node, _spec := ac.createSpec()
	if err := sqlgraph.CreateNode(ctx, ac.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	ac.mutation.id = &_node.ID
	ac.mutation.done = true
	return _node, nil
}

func (ac *AlertCreate) createSpec() (*Alert, *sqlgraph.CreateSpec) {
	var (
		_node = &Alert{config: ac.config}
		_spec = sqlgraph.NewCreateSpec(alert.Table, sqlgraph.NewFieldSpec(alert.FieldID, field.TypeUUID))
	)
	if id, ok := ac.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := ac.mutation.CreatedAt(); ok {
		_spec.SetField(alert.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ac.mutation.UpdatedAt(); ok {
		_spec.SetField(alert.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := ac.mutation.RawAlertRefID(); ok {
		_spec.SetField(alert.FieldRawAlertRefID, field.TypeString, value)
		_node.RawAlertRefID = value
	}
	if value, ok := ac.mutation.Title(); ok {
		_spec.SetField(alert.FieldTitle, field.TypeString, value)
		_node.Title = value
	}
	if value, ok := ac.mutation.Description(); ok {
		_spec.SetField(alert.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := ac.mutation.AlertSeverity(); ok {
		_spec.SetField(alert.FieldAlertSeverity, field.TypeEnum, value)
		_node.AlertSeverity = value
	}
	if value, ok := ac.mutation.AlertStatus(); ok {
		_spec.SetField(alert.FieldAlertStatus, field.TypeEnum, value)
		_node.AlertStatus = value
	}
	if value, ok := ac.mutation.Progress(); ok {
		_spec.SetField(alert.FieldProgress, field.TypeEnum, value)
		_node.Progress = value
	}
	if value, ok := ac.mutation.AlertKey(); ok {
		_spec.SetField(alert.FieldAlertKey, field.TypeString, value)
		_node.AlertKey = value
	}
	if value, ok := ac.mutation.StartTime(); ok {
		_spec.SetField(alert.FieldStartTime, field.TypeTime, value)
		_node.StartTime = value
	}
	if value, ok := ac.mutation.LastTime(); ok {
		_spec.SetField(alert.FieldLastTime, field.TypeTime, value)
		_node.LastTime = value
	}
	if value, ok := ac.mutation.EndTime(); ok {
		_spec.SetField(alert.FieldEndTime, field.TypeTime, value)
		_node.EndTime = value
	}
	if value, ok := ac.mutation.AckTime(); ok {
		_spec.SetField(alert.FieldAckTime, field.TypeTime, value)
		_node.AckTime = value
	}
	if value, ok := ac.mutation.CloseTime(); ok {
		_spec.SetField(alert.FieldCloseTime, field.TypeTime, value)
		_node.CloseTime = value
	}
	if value, ok := ac.mutation.Labels(); ok {
		_spec.SetField(alert.FieldLabels, field.TypeJSON, value)
		_node.Labels = value
	}
	if value, ok := ac.mutation.EverMuted(); ok {
		_spec.SetField(alert.FieldEverMuted, field.TypeBool, value)
		_node.EverMuted = value
	}
	if value, ok := ac.mutation.EventCnt(); ok {
		_spec.SetField(alert.FieldEventCnt, field.TypeInt, value)
		_node.EventCnt = value
	}
	if value, ok := ac.mutation.RawData(); ok {
		_spec.SetField(alert.FieldRawData, field.TypeJSON, value)
		_node.RawData = value
	}
	if value, ok := ac.mutation.IntegrationsID(); ok {
		_spec.SetField(alert.FieldIntegrationsID, field.TypeUUID, value)
		_node.IntegrationsID = value
	}
	if value, ok := ac.mutation.SpaceID(); ok {
		_spec.SetField(alert.FieldSpaceID, field.TypeString, value)
		_node.SpaceID = value
	}
	if value, ok := ac.mutation.IncidentID(); ok {
		_spec.SetField(alert.FieldIncidentID, field.TypeString, value)
		_node.IncidentID = value
	}
	if nodes := ac.mutation.RawAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   alert.RawAlertTable,
			Columns: []string{alert.RawAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(rawalert.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := ac.mutation.IncidentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   alert.IncidentTable,
			Columns: alert.IncidentPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(incident.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// AlertCreateBulk is the builder for creating many Alert entities in bulk.
type AlertCreateBulk struct {
	config
	err      error
	builders []*AlertCreate
}

// Save creates the Alert entities in the database.
func (acb *AlertCreateBulk) Save(ctx context.Context) ([]*Alert, error) {
	if acb.err != nil {
		return nil, acb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(acb.builders))
	nodes := make([]*Alert, len(acb.builders))
	mutators := make([]Mutator, len(acb.builders))
	for i := range acb.builders {
		func(i int, root context.Context) {
			builder := acb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AlertMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, acb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, acb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, acb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (acb *AlertCreateBulk) SaveX(ctx context.Context) []*Alert {
	v, err := acb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (acb *AlertCreateBulk) Exec(ctx context.Context) error {
	_, err := acb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acb *AlertCreateBulk) ExecX(ctx context.Context) {
	if err := acb.Exec(ctx); err != nil {
		panic(err)
	}
}
