// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	uuid "github.com/gofrs/uuid/v5"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/alert"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/incident"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/predicate"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/rawalert"
)

// AlertUpdate is the builder for updating Alert entities.
type AlertUpdate struct {
	config
	hooks     []Hook
	mutation  *AlertMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AlertUpdate builder.
func (au *AlertUpdate) Where(ps ...predicate.Alert) *AlertUpdate {
	au.mutation.Where(ps...)
	return au
}

// SetUpdatedAt sets the "updated_at" field.
func (au *AlertUpdate) SetUpdatedAt(t time.Time) *AlertUpdate {
	au.mutation.SetUpdatedAt(t)
	return au
}

// SetRawAlertRefID sets the "raw_alert_ref_id" field.
func (au *AlertUpdate) SetRawAlertRefID(s string) *AlertUpdate {
	au.mutation.SetRawAlertRefID(s)
	return au
}

// SetNillableRawAlertRefID sets the "raw_alert_ref_id" field if the given value is not nil.
func (au *AlertUpdate) SetNillableRawAlertRefID(s *string) *AlertUpdate {
	if s != nil {
		au.SetRawAlertRefID(*s)
	}
	return au
}

// ClearRawAlertRefID clears the value of the "raw_alert_ref_id" field.
func (au *AlertUpdate) ClearRawAlertRefID() *AlertUpdate {
	au.mutation.ClearRawAlertRefID()
	return au
}

// SetTitle sets the "title" field.
func (au *AlertUpdate) SetTitle(s string) *AlertUpdate {
	au.mutation.SetTitle(s)
	return au
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (au *AlertUpdate) SetNillableTitle(s *string) *AlertUpdate {
	if s != nil {
		au.SetTitle(*s)
	}
	return au
}

// SetDescription sets the "description" field.
func (au *AlertUpdate) SetDescription(s string) *AlertUpdate {
	au.mutation.SetDescription(s)
	return au
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (au *AlertUpdate) SetNillableDescription(s *string) *AlertUpdate {
	if s != nil {
		au.SetDescription(*s)
	}
	return au
}

// ClearDescription clears the value of the "description" field.
func (au *AlertUpdate) ClearDescription() *AlertUpdate {
	au.mutation.ClearDescription()
	return au
}

// SetAlertSeverity sets the "alert_severity" field.
func (au *AlertUpdate) SetAlertSeverity(as alert.AlertSeverity) *AlertUpdate {
	au.mutation.SetAlertSeverity(as)
	return au
}

// SetNillableAlertSeverity sets the "alert_severity" field if the given value is not nil.
func (au *AlertUpdate) SetNillableAlertSeverity(as *alert.AlertSeverity) *AlertUpdate {
	if as != nil {
		au.SetAlertSeverity(*as)
	}
	return au
}

// SetAlertStatus sets the "alert_status" field.
func (au *AlertUpdate) SetAlertStatus(as alert.AlertStatus) *AlertUpdate {
	au.mutation.SetAlertStatus(as)
	return au
}

// SetNillableAlertStatus sets the "alert_status" field if the given value is not nil.
func (au *AlertUpdate) SetNillableAlertStatus(as *alert.AlertStatus) *AlertUpdate {
	if as != nil {
		au.SetAlertStatus(*as)
	}
	return au
}

// SetProgress sets the "progress" field.
func (au *AlertUpdate) SetProgress(a alert.Progress) *AlertUpdate {
	au.mutation.SetProgress(a)
	return au
}

// SetNillableProgress sets the "progress" field if the given value is not nil.
func (au *AlertUpdate) SetNillableProgress(a *alert.Progress) *AlertUpdate {
	if a != nil {
		au.SetProgress(*a)
	}
	return au
}

// SetAlertKey sets the "alert_key" field.
func (au *AlertUpdate) SetAlertKey(s string) *AlertUpdate {
	au.mutation.SetAlertKey(s)
	return au
}

// SetNillableAlertKey sets the "alert_key" field if the given value is not nil.
func (au *AlertUpdate) SetNillableAlertKey(s *string) *AlertUpdate {
	if s != nil {
		au.SetAlertKey(*s)
	}
	return au
}

// SetStartTime sets the "start_time" field.
func (au *AlertUpdate) SetStartTime(t time.Time) *AlertUpdate {
	au.mutation.SetStartTime(t)
	return au
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (au *AlertUpdate) SetNillableStartTime(t *time.Time) *AlertUpdate {
	if t != nil {
		au.SetStartTime(*t)
	}
	return au
}

// SetLastTime sets the "last_time" field.
func (au *AlertUpdate) SetLastTime(t time.Time) *AlertUpdate {
	au.mutation.SetLastTime(t)
	return au
}

// SetNillableLastTime sets the "last_time" field if the given value is not nil.
func (au *AlertUpdate) SetNillableLastTime(t *time.Time) *AlertUpdate {
	if t != nil {
		au.SetLastTime(*t)
	}
	return au
}

// SetEndTime sets the "end_time" field.
func (au *AlertUpdate) SetEndTime(t time.Time) *AlertUpdate {
	au.mutation.SetEndTime(t)
	return au
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (au *AlertUpdate) SetNillableEndTime(t *time.Time) *AlertUpdate {
	if t != nil {
		au.SetEndTime(*t)
	}
	return au
}

// ClearEndTime clears the value of the "end_time" field.
func (au *AlertUpdate) ClearEndTime() *AlertUpdate {
	au.mutation.ClearEndTime()
	return au
}

// SetAckTime sets the "ack_time" field.
func (au *AlertUpdate) SetAckTime(t time.Time) *AlertUpdate {
	au.mutation.SetAckTime(t)
	return au
}

// SetNillableAckTime sets the "ack_time" field if the given value is not nil.
func (au *AlertUpdate) SetNillableAckTime(t *time.Time) *AlertUpdate {
	if t != nil {
		au.SetAckTime(*t)
	}
	return au
}

// ClearAckTime clears the value of the "ack_time" field.
func (au *AlertUpdate) ClearAckTime() *AlertUpdate {
	au.mutation.ClearAckTime()
	return au
}

// SetCloseTime sets the "close_time" field.
func (au *AlertUpdate) SetCloseTime(t time.Time) *AlertUpdate {
	au.mutation.SetCloseTime(t)
	return au
}

// SetNillableCloseTime sets the "close_time" field if the given value is not nil.
func (au *AlertUpdate) SetNillableCloseTime(t *time.Time) *AlertUpdate {
	if t != nil {
		au.SetCloseTime(*t)
	}
	return au
}

// ClearCloseTime clears the value of the "close_time" field.
func (au *AlertUpdate) ClearCloseTime() *AlertUpdate {
	au.mutation.ClearCloseTime()
	return au
}

// SetLabels sets the "labels" field.
func (au *AlertUpdate) SetLabels(m map[string]string) *AlertUpdate {
	au.mutation.SetLabels(m)
	return au
}

// SetEverMuted sets the "ever_muted" field.
func (au *AlertUpdate) SetEverMuted(b bool) *AlertUpdate {
	au.mutation.SetEverMuted(b)
	return au
}

// SetNillableEverMuted sets the "ever_muted" field if the given value is not nil.
func (au *AlertUpdate) SetNillableEverMuted(b *bool) *AlertUpdate {
	if b != nil {
		au.SetEverMuted(*b)
	}
	return au
}

// SetEventCnt sets the "event_cnt" field.
func (au *AlertUpdate) SetEventCnt(i int) *AlertUpdate {
	au.mutation.ResetEventCnt()
	au.mutation.SetEventCnt(i)
	return au
}

// SetNillableEventCnt sets the "event_cnt" field if the given value is not nil.
func (au *AlertUpdate) SetNillableEventCnt(i *int) *AlertUpdate {
	if i != nil {
		au.SetEventCnt(*i)
	}
	return au
}

// AddEventCnt adds i to the "event_cnt" field.
func (au *AlertUpdate) AddEventCnt(i int) *AlertUpdate {
	au.mutation.AddEventCnt(i)
	return au
}

// SetRawData sets the "raw_data" field.
func (au *AlertUpdate) SetRawData(m map[string]interface{}) *AlertUpdate {
	au.mutation.SetRawData(m)
	return au
}

// SetIntegrationsID sets the "integrations_id" field.
func (au *AlertUpdate) SetIntegrationsID(u uuid.UUID) *AlertUpdate {
	au.mutation.SetIntegrationsID(u)
	return au
}

// SetNillableIntegrationsID sets the "integrations_id" field if the given value is not nil.
func (au *AlertUpdate) SetNillableIntegrationsID(u *uuid.UUID) *AlertUpdate {
	if u != nil {
		au.SetIntegrationsID(*u)
	}
	return au
}

// SetSpaceID sets the "space_id" field.
func (au *AlertUpdate) SetSpaceID(s string) *AlertUpdate {
	au.mutation.SetSpaceID(s)
	return au
}

// SetNillableSpaceID sets the "space_id" field if the given value is not nil.
func (au *AlertUpdate) SetNillableSpaceID(s *string) *AlertUpdate {
	if s != nil {
		au.SetSpaceID(*s)
	}
	return au
}

// SetIncidentID sets the "incident_id" field.
func (au *AlertUpdate) SetIncidentID(s string) *AlertUpdate {
	au.mutation.SetIncidentID(s)
	return au
}

// SetNillableIncidentID sets the "incident_id" field if the given value is not nil.
func (au *AlertUpdate) SetNillableIncidentID(s *string) *AlertUpdate {
	if s != nil {
		au.SetIncidentID(*s)
	}
	return au
}

// ClearIncidentID clears the value of the "incident_id" field.
func (au *AlertUpdate) ClearIncidentID() *AlertUpdate {
	au.mutation.ClearIncidentID()
	return au
}

// SetRawAlertID sets the "raw_alert" edge to the RawAlert entity by ID.
func (au *AlertUpdate) SetRawAlertID(id uuid.UUID) *AlertUpdate {
	au.mutation.SetRawAlertID(id)
	return au
}

// SetNillableRawAlertID sets the "raw_alert" edge to the RawAlert entity by ID if the given value is not nil.
func (au *AlertUpdate) SetNillableRawAlertID(id *uuid.UUID) *AlertUpdate {
	if id != nil {
		au = au.SetRawAlertID(*id)
	}
	return au
}

// SetRawAlert sets the "raw_alert" edge to the RawAlert entity.
func (au *AlertUpdate) SetRawAlert(r *RawAlert) *AlertUpdate {
	return au.SetRawAlertID(r.ID)
}

// AddIncidentIDs adds the "incident" edge to the Incident entity by IDs.
func (au *AlertUpdate) AddIncidentIDs(ids ...uuid.UUID) *AlertUpdate {
	au.mutation.AddIncidentIDs(ids...)
	return au
}

// AddIncident adds the "incident" edges to the Incident entity.
func (au *AlertUpdate) AddIncident(i ...*Incident) *AlertUpdate {
	ids := make([]uuid.UUID, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return au.AddIncidentIDs(ids...)
}

// Mutation returns the AlertMutation object of the builder.
func (au *AlertUpdate) Mutation() *AlertMutation {
	return au.mutation
}

// ClearRawAlert clears the "raw_alert" edge to the RawAlert entity.
func (au *AlertUpdate) ClearRawAlert() *AlertUpdate {
	au.mutation.ClearRawAlert()
	return au
}

// ClearIncident clears all "incident" edges to the Incident entity.
func (au *AlertUpdate) ClearIncident() *AlertUpdate {
	au.mutation.ClearIncident()
	return au
}

// RemoveIncidentIDs removes the "incident" edge to Incident entities by IDs.
func (au *AlertUpdate) RemoveIncidentIDs(ids ...uuid.UUID) *AlertUpdate {
	au.mutation.RemoveIncidentIDs(ids...)
	return au
}

// RemoveIncident removes "incident" edges to Incident entities.
func (au *AlertUpdate) RemoveIncident(i ...*Incident) *AlertUpdate {
	ids := make([]uuid.UUID, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return au.RemoveIncidentIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (au *AlertUpdate) Save(ctx context.Context) (int, error) {
	au.defaults()
	return withHooks(ctx, au.sqlSave, au.mutation, au.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (au *AlertUpdate) SaveX(ctx context.Context) int {
	affected, err := au.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (au *AlertUpdate) Exec(ctx context.Context) error {
	_, err := au.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (au *AlertUpdate) ExecX(ctx context.Context) {
	if err := au.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (au *AlertUpdate) defaults() {
	if _, ok := au.mutation.UpdatedAt(); !ok {
		v := alert.UpdateDefaultUpdatedAt()
		au.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (au *AlertUpdate) check() error {
	if v, ok := au.mutation.AlertSeverity(); ok {
		if err := alert.AlertSeverityValidator(v); err != nil {
			return &ValidationError{Name: "alert_severity", err: fmt.Errorf(`ent: validator failed for field "Alert.alert_severity": %w`, err)}
		}
	}
	if v, ok := au.mutation.AlertStatus(); ok {
		if err := alert.AlertStatusValidator(v); err != nil {
			return &ValidationError{Name: "alert_status", err: fmt.Errorf(`ent: validator failed for field "Alert.alert_status": %w`, err)}
		}
	}
	if v, ok := au.mutation.Progress(); ok {
		if err := alert.ProgressValidator(v); err != nil {
			return &ValidationError{Name: "progress", err: fmt.Errorf(`ent: validator failed for field "Alert.progress": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (au *AlertUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AlertUpdate {
	au.modifiers = append(au.modifiers, modifiers...)
	return au
}

func (au *AlertUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := au.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(alert.Table, alert.Columns, sqlgraph.NewFieldSpec(alert.FieldID, field.TypeUUID))
	if ps := au.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := au.mutation.UpdatedAt(); ok {
		_spec.SetField(alert.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := au.mutation.RawAlertRefID(); ok {
		_spec.SetField(alert.FieldRawAlertRefID, field.TypeString, value)
	}
	if au.mutation.RawAlertRefIDCleared() {
		_spec.ClearField(alert.FieldRawAlertRefID, field.TypeString)
	}
	if value, ok := au.mutation.Title(); ok {
		_spec.SetField(alert.FieldTitle, field.TypeString, value)
	}
	if value, ok := au.mutation.Description(); ok {
		_spec.SetField(alert.FieldDescription, field.TypeString, value)
	}
	if au.mutation.DescriptionCleared() {
		_spec.ClearField(alert.FieldDescription, field.TypeString)
	}
	if value, ok := au.mutation.AlertSeverity(); ok {
		_spec.SetField(alert.FieldAlertSeverity, field.TypeEnum, value)
	}
	if value, ok := au.mutation.AlertStatus(); ok {
		_spec.SetField(alert.FieldAlertStatus, field.TypeEnum, value)
	}
	if value, ok := au.mutation.Progress(); ok {
		_spec.SetField(alert.FieldProgress, field.TypeEnum, value)
	}
	if value, ok := au.mutation.AlertKey(); ok {
		_spec.SetField(alert.FieldAlertKey, field.TypeString, value)
	}
	if value, ok := au.mutation.StartTime(); ok {
		_spec.SetField(alert.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := au.mutation.LastTime(); ok {
		_spec.SetField(alert.FieldLastTime, field.TypeTime, value)
	}
	if value, ok := au.mutation.EndTime(); ok {
		_spec.SetField(alert.FieldEndTime, field.TypeTime, value)
	}
	if au.mutation.EndTimeCleared() {
		_spec.ClearField(alert.FieldEndTime, field.TypeTime)
	}
	if value, ok := au.mutation.AckTime(); ok {
		_spec.SetField(alert.FieldAckTime, field.TypeTime, value)
	}
	if au.mutation.AckTimeCleared() {
		_spec.ClearField(alert.FieldAckTime, field.TypeTime)
	}
	if value, ok := au.mutation.CloseTime(); ok {
		_spec.SetField(alert.FieldCloseTime, field.TypeTime, value)
	}
	if au.mutation.CloseTimeCleared() {
		_spec.ClearField(alert.FieldCloseTime, field.TypeTime)
	}
	if value, ok := au.mutation.Labels(); ok {
		_spec.SetField(alert.FieldLabels, field.TypeJSON, value)
	}
	if value, ok := au.mutation.EverMuted(); ok {
		_spec.SetField(alert.FieldEverMuted, field.TypeBool, value)
	}
	if value, ok := au.mutation.EventCnt(); ok {
		_spec.SetField(alert.FieldEventCnt, field.TypeInt, value)
	}
	if value, ok := au.mutation.AddedEventCnt(); ok {
		_spec.AddField(alert.FieldEventCnt, field.TypeInt, value)
	}
	if value, ok := au.mutation.RawData(); ok {
		_spec.SetField(alert.FieldRawData, field.TypeJSON, value)
	}
	if value, ok := au.mutation.IntegrationsID(); ok {
		_spec.SetField(alert.FieldIntegrationsID, field.TypeUUID, value)
	}
	if value, ok := au.mutation.SpaceID(); ok {
		_spec.SetField(alert.FieldSpaceID, field.TypeString, value)
	}
	if value, ok := au.mutation.IncidentID(); ok {
		_spec.SetField(alert.FieldIncidentID, field.TypeString, value)
	}
	if au.mutation.IncidentIDCleared() {
		_spec.ClearField(alert.FieldIncidentID, field.TypeString)
	}
	if au.mutation.RawAlertCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   alert.RawAlertTable,
			Columns: []string{alert.RawAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(rawalert.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := au.mutation.RawAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   alert.RawAlertTable,
			Columns: []string{alert.RawAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(rawalert.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if au.mutation.IncidentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   alert.IncidentTable,
			Columns: alert.IncidentPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(incident.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := au.mutation.RemovedIncidentIDs(); len(nodes) > 0 && !au.mutation.IncidentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   alert.IncidentTable,
			Columns: alert.IncidentPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(incident.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := au.mutation.IncidentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   alert.IncidentTable,
			Columns: alert.IncidentPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(incident.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(au.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, au.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{alert.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	au.mutation.done = true
	return n, nil
}

// AlertUpdateOne is the builder for updating a single Alert entity.
type AlertUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AlertMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (auo *AlertUpdateOne) SetUpdatedAt(t time.Time) *AlertUpdateOne {
	auo.mutation.SetUpdatedAt(t)
	return auo
}

// SetRawAlertRefID sets the "raw_alert_ref_id" field.
func (auo *AlertUpdateOne) SetRawAlertRefID(s string) *AlertUpdateOne {
	auo.mutation.SetRawAlertRefID(s)
	return auo
}

// SetNillableRawAlertRefID sets the "raw_alert_ref_id" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableRawAlertRefID(s *string) *AlertUpdateOne {
	if s != nil {
		auo.SetRawAlertRefID(*s)
	}
	return auo
}

// ClearRawAlertRefID clears the value of the "raw_alert_ref_id" field.
func (auo *AlertUpdateOne) ClearRawAlertRefID() *AlertUpdateOne {
	auo.mutation.ClearRawAlertRefID()
	return auo
}

// SetTitle sets the "title" field.
func (auo *AlertUpdateOne) SetTitle(s string) *AlertUpdateOne {
	auo.mutation.SetTitle(s)
	return auo
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableTitle(s *string) *AlertUpdateOne {
	if s != nil {
		auo.SetTitle(*s)
	}
	return auo
}

// SetDescription sets the "description" field.
func (auo *AlertUpdateOne) SetDescription(s string) *AlertUpdateOne {
	auo.mutation.SetDescription(s)
	return auo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableDescription(s *string) *AlertUpdateOne {
	if s != nil {
		auo.SetDescription(*s)
	}
	return auo
}

// ClearDescription clears the value of the "description" field.
func (auo *AlertUpdateOne) ClearDescription() *AlertUpdateOne {
	auo.mutation.ClearDescription()
	return auo
}

// SetAlertSeverity sets the "alert_severity" field.
func (auo *AlertUpdateOne) SetAlertSeverity(as alert.AlertSeverity) *AlertUpdateOne {
	auo.mutation.SetAlertSeverity(as)
	return auo
}

// SetNillableAlertSeverity sets the "alert_severity" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableAlertSeverity(as *alert.AlertSeverity) *AlertUpdateOne {
	if as != nil {
		auo.SetAlertSeverity(*as)
	}
	return auo
}

// SetAlertStatus sets the "alert_status" field.
func (auo *AlertUpdateOne) SetAlertStatus(as alert.AlertStatus) *AlertUpdateOne {
	auo.mutation.SetAlertStatus(as)
	return auo
}

// SetNillableAlertStatus sets the "alert_status" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableAlertStatus(as *alert.AlertStatus) *AlertUpdateOne {
	if as != nil {
		auo.SetAlertStatus(*as)
	}
	return auo
}

// SetProgress sets the "progress" field.
func (auo *AlertUpdateOne) SetProgress(a alert.Progress) *AlertUpdateOne {
	auo.mutation.SetProgress(a)
	return auo
}

// SetNillableProgress sets the "progress" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableProgress(a *alert.Progress) *AlertUpdateOne {
	if a != nil {
		auo.SetProgress(*a)
	}
	return auo
}

// SetAlertKey sets the "alert_key" field.
func (auo *AlertUpdateOne) SetAlertKey(s string) *AlertUpdateOne {
	auo.mutation.SetAlertKey(s)
	return auo
}

// SetNillableAlertKey sets the "alert_key" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableAlertKey(s *string) *AlertUpdateOne {
	if s != nil {
		auo.SetAlertKey(*s)
	}
	return auo
}

// SetStartTime sets the "start_time" field.
func (auo *AlertUpdateOne) SetStartTime(t time.Time) *AlertUpdateOne {
	auo.mutation.SetStartTime(t)
	return auo
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableStartTime(t *time.Time) *AlertUpdateOne {
	if t != nil {
		auo.SetStartTime(*t)
	}
	return auo
}

// SetLastTime sets the "last_time" field.
func (auo *AlertUpdateOne) SetLastTime(t time.Time) *AlertUpdateOne {
	auo.mutation.SetLastTime(t)
	return auo
}

// SetNillableLastTime sets the "last_time" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableLastTime(t *time.Time) *AlertUpdateOne {
	if t != nil {
		auo.SetLastTime(*t)
	}
	return auo
}

// SetEndTime sets the "end_time" field.
func (auo *AlertUpdateOne) SetEndTime(t time.Time) *AlertUpdateOne {
	auo.mutation.SetEndTime(t)
	return auo
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableEndTime(t *time.Time) *AlertUpdateOne {
	if t != nil {
		auo.SetEndTime(*t)
	}
	return auo
}

// ClearEndTime clears the value of the "end_time" field.
func (auo *AlertUpdateOne) ClearEndTime() *AlertUpdateOne {
	auo.mutation.ClearEndTime()
	return auo
}

// SetAckTime sets the "ack_time" field.
func (auo *AlertUpdateOne) SetAckTime(t time.Time) *AlertUpdateOne {
	auo.mutation.SetAckTime(t)
	return auo
}

// SetNillableAckTime sets the "ack_time" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableAckTime(t *time.Time) *AlertUpdateOne {
	if t != nil {
		auo.SetAckTime(*t)
	}
	return auo
}

// ClearAckTime clears the value of the "ack_time" field.
func (auo *AlertUpdateOne) ClearAckTime() *AlertUpdateOne {
	auo.mutation.ClearAckTime()
	return auo
}

// SetCloseTime sets the "close_time" field.
func (auo *AlertUpdateOne) SetCloseTime(t time.Time) *AlertUpdateOne {
	auo.mutation.SetCloseTime(t)
	return auo
}

// SetNillableCloseTime sets the "close_time" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableCloseTime(t *time.Time) *AlertUpdateOne {
	if t != nil {
		auo.SetCloseTime(*t)
	}
	return auo
}

// ClearCloseTime clears the value of the "close_time" field.
func (auo *AlertUpdateOne) ClearCloseTime() *AlertUpdateOne {
	auo.mutation.ClearCloseTime()
	return auo
}

// SetLabels sets the "labels" field.
func (auo *AlertUpdateOne) SetLabels(m map[string]string) *AlertUpdateOne {
	auo.mutation.SetLabels(m)
	return auo
}

// SetEverMuted sets the "ever_muted" field.
func (auo *AlertUpdateOne) SetEverMuted(b bool) *AlertUpdateOne {
	auo.mutation.SetEverMuted(b)
	return auo
}

// SetNillableEverMuted sets the "ever_muted" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableEverMuted(b *bool) *AlertUpdateOne {
	if b != nil {
		auo.SetEverMuted(*b)
	}
	return auo
}

// SetEventCnt sets the "event_cnt" field.
func (auo *AlertUpdateOne) SetEventCnt(i int) *AlertUpdateOne {
	auo.mutation.ResetEventCnt()
	auo.mutation.SetEventCnt(i)
	return auo
}

// SetNillableEventCnt sets the "event_cnt" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableEventCnt(i *int) *AlertUpdateOne {
	if i != nil {
		auo.SetEventCnt(*i)
	}
	return auo
}

// AddEventCnt adds i to the "event_cnt" field.
func (auo *AlertUpdateOne) AddEventCnt(i int) *AlertUpdateOne {
	auo.mutation.AddEventCnt(i)
	return auo
}

// SetRawData sets the "raw_data" field.
func (auo *AlertUpdateOne) SetRawData(m map[string]interface{}) *AlertUpdateOne {
	auo.mutation.SetRawData(m)
	return auo
}

// SetIntegrationsID sets the "integrations_id" field.
func (auo *AlertUpdateOne) SetIntegrationsID(u uuid.UUID) *AlertUpdateOne {
	auo.mutation.SetIntegrationsID(u)
	return auo
}

// SetNillableIntegrationsID sets the "integrations_id" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableIntegrationsID(u *uuid.UUID) *AlertUpdateOne {
	if u != nil {
		auo.SetIntegrationsID(*u)
	}
	return auo
}

// SetSpaceID sets the "space_id" field.
func (auo *AlertUpdateOne) SetSpaceID(s string) *AlertUpdateOne {
	auo.mutation.SetSpaceID(s)
	return auo
}

// SetNillableSpaceID sets the "space_id" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableSpaceID(s *string) *AlertUpdateOne {
	if s != nil {
		auo.SetSpaceID(*s)
	}
	return auo
}

// SetIncidentID sets the "incident_id" field.
func (auo *AlertUpdateOne) SetIncidentID(s string) *AlertUpdateOne {
	auo.mutation.SetIncidentID(s)
	return auo
}

// SetNillableIncidentID sets the "incident_id" field if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableIncidentID(s *string) *AlertUpdateOne {
	if s != nil {
		auo.SetIncidentID(*s)
	}
	return auo
}

// ClearIncidentID clears the value of the "incident_id" field.
func (auo *AlertUpdateOne) ClearIncidentID() *AlertUpdateOne {
	auo.mutation.ClearIncidentID()
	return auo
}

// SetRawAlertID sets the "raw_alert" edge to the RawAlert entity by ID.
func (auo *AlertUpdateOne) SetRawAlertID(id uuid.UUID) *AlertUpdateOne {
	auo.mutation.SetRawAlertID(id)
	return auo
}

// SetNillableRawAlertID sets the "raw_alert" edge to the RawAlert entity by ID if the given value is not nil.
func (auo *AlertUpdateOne) SetNillableRawAlertID(id *uuid.UUID) *AlertUpdateOne {
	if id != nil {
		auo = auo.SetRawAlertID(*id)
	}
	return auo
}

// SetRawAlert sets the "raw_alert" edge to the RawAlert entity.
func (auo *AlertUpdateOne) SetRawAlert(r *RawAlert) *AlertUpdateOne {
	return auo.SetRawAlertID(r.ID)
}

// AddIncidentIDs adds the "incident" edge to the Incident entity by IDs.
func (auo *AlertUpdateOne) AddIncidentIDs(ids ...uuid.UUID) *AlertUpdateOne {
	auo.mutation.AddIncidentIDs(ids...)
	return auo
}

// AddIncident adds the "incident" edges to the Incident entity.
func (auo *AlertUpdateOne) AddIncident(i ...*Incident) *AlertUpdateOne {
	ids := make([]uuid.UUID, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return auo.AddIncidentIDs(ids...)
}

// Mutation returns the AlertMutation object of the builder.
func (auo *AlertUpdateOne) Mutation() *AlertMutation {
	return auo.mutation
}

// ClearRawAlert clears the "raw_alert" edge to the RawAlert entity.
func (auo *AlertUpdateOne) ClearRawAlert() *AlertUpdateOne {
	auo.mutation.ClearRawAlert()
	return auo
}

// ClearIncident clears all "incident" edges to the Incident entity.
func (auo *AlertUpdateOne) ClearIncident() *AlertUpdateOne {
	auo.mutation.ClearIncident()
	return auo
}

// RemoveIncidentIDs removes the "incident" edge to Incident entities by IDs.
func (auo *AlertUpdateOne) RemoveIncidentIDs(ids ...uuid.UUID) *AlertUpdateOne {
	auo.mutation.RemoveIncidentIDs(ids...)
	return auo
}

// RemoveIncident removes "incident" edges to Incident entities.
func (auo *AlertUpdateOne) RemoveIncident(i ...*Incident) *AlertUpdateOne {
	ids := make([]uuid.UUID, len(i))
	for j := range i {
		ids[j] = i[j].ID
	}
	return auo.RemoveIncidentIDs(ids...)
}

// Where appends a list predicates to the AlertUpdate builder.
func (auo *AlertUpdateOne) Where(ps ...predicate.Alert) *AlertUpdateOne {
	auo.mutation.Where(ps...)
	return auo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (auo *AlertUpdateOne) Select(field string, fields ...string) *AlertUpdateOne {
	auo.fields = append([]string{field}, fields...)
	return auo
}

// Save executes the query and returns the updated Alert entity.
func (auo *AlertUpdateOne) Save(ctx context.Context) (*Alert, error) {
	auo.defaults()
	return withHooks(ctx, auo.sqlSave, auo.mutation, auo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (auo *AlertUpdateOne) SaveX(ctx context.Context) *Alert {
	node, err := auo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (auo *AlertUpdateOne) Exec(ctx context.Context) error {
	_, err := auo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (auo *AlertUpdateOne) ExecX(ctx context.Context) {
	if err := auo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (auo *AlertUpdateOne) defaults() {
	if _, ok := auo.mutation.UpdatedAt(); !ok {
		v := alert.UpdateDefaultUpdatedAt()
		auo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (auo *AlertUpdateOne) check() error {
	if v, ok := auo.mutation.AlertSeverity(); ok {
		if err := alert.AlertSeverityValidator(v); err != nil {
			return &ValidationError{Name: "alert_severity", err: fmt.Errorf(`ent: validator failed for field "Alert.alert_severity": %w`, err)}
		}
	}
	if v, ok := auo.mutation.AlertStatus(); ok {
		if err := alert.AlertStatusValidator(v); err != nil {
			return &ValidationError{Name: "alert_status", err: fmt.Errorf(`ent: validator failed for field "Alert.alert_status": %w`, err)}
		}
	}
	if v, ok := auo.mutation.Progress(); ok {
		if err := alert.ProgressValidator(v); err != nil {
			return &ValidationError{Name: "progress", err: fmt.Errorf(`ent: validator failed for field "Alert.progress": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (auo *AlertUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AlertUpdateOne {
	auo.modifiers = append(auo.modifiers, modifiers...)
	return auo
}

func (auo *AlertUpdateOne) sqlSave(ctx context.Context) (_node *Alert, err error) {
	if err := auo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(alert.Table, alert.Columns, sqlgraph.NewFieldSpec(alert.FieldID, field.TypeUUID))
	id, ok := auo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Alert.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := auo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, alert.FieldID)
		for _, f := range fields {
			if !alert.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != alert.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := auo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := auo.mutation.UpdatedAt(); ok {
		_spec.SetField(alert.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := auo.mutation.RawAlertRefID(); ok {
		_spec.SetField(alert.FieldRawAlertRefID, field.TypeString, value)
	}
	if auo.mutation.RawAlertRefIDCleared() {
		_spec.ClearField(alert.FieldRawAlertRefID, field.TypeString)
	}
	if value, ok := auo.mutation.Title(); ok {
		_spec.SetField(alert.FieldTitle, field.TypeString, value)
	}
	if value, ok := auo.mutation.Description(); ok {
		_spec.SetField(alert.FieldDescription, field.TypeString, value)
	}
	if auo.mutation.DescriptionCleared() {
		_spec.ClearField(alert.FieldDescription, field.TypeString)
	}
	if value, ok := auo.mutation.AlertSeverity(); ok {
		_spec.SetField(alert.FieldAlertSeverity, field.TypeEnum, value)
	}
	if value, ok := auo.mutation.AlertStatus(); ok {
		_spec.SetField(alert.FieldAlertStatus, field.TypeEnum, value)
	}
	if value, ok := auo.mutation.Progress(); ok {
		_spec.SetField(alert.FieldProgress, field.TypeEnum, value)
	}
	if value, ok := auo.mutation.AlertKey(); ok {
		_spec.SetField(alert.FieldAlertKey, field.TypeString, value)
	}
	if value, ok := auo.mutation.StartTime(); ok {
		_spec.SetField(alert.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := auo.mutation.LastTime(); ok {
		_spec.SetField(alert.FieldLastTime, field.TypeTime, value)
	}
	if value, ok := auo.mutation.EndTime(); ok {
		_spec.SetField(alert.FieldEndTime, field.TypeTime, value)
	}
	if auo.mutation.EndTimeCleared() {
		_spec.ClearField(alert.FieldEndTime, field.TypeTime)
	}
	if value, ok := auo.mutation.AckTime(); ok {
		_spec.SetField(alert.FieldAckTime, field.TypeTime, value)
	}
	if auo.mutation.AckTimeCleared() {
		_spec.ClearField(alert.FieldAckTime, field.TypeTime)
	}
	if value, ok := auo.mutation.CloseTime(); ok {
		_spec.SetField(alert.FieldCloseTime, field.TypeTime, value)
	}
	if auo.mutation.CloseTimeCleared() {
		_spec.ClearField(alert.FieldCloseTime, field.TypeTime)
	}
	if value, ok := auo.mutation.Labels(); ok {
		_spec.SetField(alert.FieldLabels, field.TypeJSON, value)
	}
	if value, ok := auo.mutation.EverMuted(); ok {
		_spec.SetField(alert.FieldEverMuted, field.TypeBool, value)
	}
	if value, ok := auo.mutation.EventCnt(); ok {
		_spec.SetField(alert.FieldEventCnt, field.TypeInt, value)
	}
	if value, ok := auo.mutation.AddedEventCnt(); ok {
		_spec.AddField(alert.FieldEventCnt, field.TypeInt, value)
	}
	if value, ok := auo.mutation.RawData(); ok {
		_spec.SetField(alert.FieldRawData, field.TypeJSON, value)
	}
	if value, ok := auo.mutation.IntegrationsID(); ok {
		_spec.SetField(alert.FieldIntegrationsID, field.TypeUUID, value)
	}
	if value, ok := auo.mutation.SpaceID(); ok {
		_spec.SetField(alert.FieldSpaceID, field.TypeString, value)
	}
	if value, ok := auo.mutation.IncidentID(); ok {
		_spec.SetField(alert.FieldIncidentID, field.TypeString, value)
	}
	if auo.mutation.IncidentIDCleared() {
		_spec.ClearField(alert.FieldIncidentID, field.TypeString)
	}
	if auo.mutation.RawAlertCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   alert.RawAlertTable,
			Columns: []string{alert.RawAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(rawalert.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := auo.mutation.RawAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   alert.RawAlertTable,
			Columns: []string{alert.RawAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(rawalert.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if auo.mutation.IncidentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   alert.IncidentTable,
			Columns: alert.IncidentPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(incident.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := auo.mutation.RemovedIncidentIDs(); len(nodes) > 0 && !auo.mutation.IncidentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   alert.IncidentTable,
			Columns: alert.IncidentPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(incident.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := auo.mutation.IncidentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   alert.IncidentTable,
			Columns: alert.IncidentPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(incident.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(auo.modifiers...)
	_node = &Alert{config: auo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, auo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{alert.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	auo.mutation.done = true
	return _node, nil
}
