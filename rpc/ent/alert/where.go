// Code generated by ent, DO NOT EDIT.

package alert

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	uuid "github.com/gofrs/uuid/v5"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldUpdatedAt, v))
}

// RawAlertRefID applies equality check predicate on the "raw_alert_ref_id" field. It's identical to RawAlertRefIDEQ.
func RawAlertRefID(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldRawAlertRefID, v))
}

// Title applies equality check predicate on the "title" field. It's identical to TitleEQ.
func Title(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldTitle, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldDescription, v))
}

// AlertKey applies equality check predicate on the "alert_key" field. It's identical to AlertKeyEQ.
func AlertKey(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldAlertKey, v))
}

// StartTime applies equality check predicate on the "start_time" field. It's identical to StartTimeEQ.
func StartTime(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldStartTime, v))
}

// LastTime applies equality check predicate on the "last_time" field. It's identical to LastTimeEQ.
func LastTime(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldLastTime, v))
}

// EndTime applies equality check predicate on the "end_time" field. It's identical to EndTimeEQ.
func EndTime(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldEndTime, v))
}

// AckTime applies equality check predicate on the "ack_time" field. It's identical to AckTimeEQ.
func AckTime(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldAckTime, v))
}

// CloseTime applies equality check predicate on the "close_time" field. It's identical to CloseTimeEQ.
func CloseTime(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldCloseTime, v))
}

// EverMuted applies equality check predicate on the "ever_muted" field. It's identical to EverMutedEQ.
func EverMuted(v bool) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldEverMuted, v))
}

// EventCnt applies equality check predicate on the "event_cnt" field. It's identical to EventCntEQ.
func EventCnt(v int) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldEventCnt, v))
}

// IntegrationsID applies equality check predicate on the "integrations_id" field. It's identical to IntegrationsIDEQ.
func IntegrationsID(v uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldIntegrationsID, v))
}

// SpaceID applies equality check predicate on the "space_id" field. It's identical to SpaceIDEQ.
func SpaceID(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldSpaceID, v))
}

// IncidentID applies equality check predicate on the "incident_id" field. It's identical to IncidentIDEQ.
func IncidentID(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldIncidentID, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldUpdatedAt, v))
}

// RawAlertRefIDEQ applies the EQ predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDEQ(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldRawAlertRefID, v))
}

// RawAlertRefIDNEQ applies the NEQ predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDNEQ(v string) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldRawAlertRefID, v))
}

// RawAlertRefIDIn applies the In predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDIn(vs ...string) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldRawAlertRefID, vs...))
}

// RawAlertRefIDNotIn applies the NotIn predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDNotIn(vs ...string) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldRawAlertRefID, vs...))
}

// RawAlertRefIDGT applies the GT predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDGT(v string) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldRawAlertRefID, v))
}

// RawAlertRefIDGTE applies the GTE predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDGTE(v string) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldRawAlertRefID, v))
}

// RawAlertRefIDLT applies the LT predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDLT(v string) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldRawAlertRefID, v))
}

// RawAlertRefIDLTE applies the LTE predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDLTE(v string) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldRawAlertRefID, v))
}

// RawAlertRefIDContains applies the Contains predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDContains(v string) predicate.Alert {
	return predicate.Alert(sql.FieldContains(FieldRawAlertRefID, v))
}

// RawAlertRefIDHasPrefix applies the HasPrefix predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDHasPrefix(v string) predicate.Alert {
	return predicate.Alert(sql.FieldHasPrefix(FieldRawAlertRefID, v))
}

// RawAlertRefIDHasSuffix applies the HasSuffix predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDHasSuffix(v string) predicate.Alert {
	return predicate.Alert(sql.FieldHasSuffix(FieldRawAlertRefID, v))
}

// RawAlertRefIDIsNil applies the IsNil predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDIsNil() predicate.Alert {
	return predicate.Alert(sql.FieldIsNull(FieldRawAlertRefID))
}

// RawAlertRefIDNotNil applies the NotNil predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDNotNil() predicate.Alert {
	return predicate.Alert(sql.FieldNotNull(FieldRawAlertRefID))
}

// RawAlertRefIDEqualFold applies the EqualFold predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDEqualFold(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEqualFold(FieldRawAlertRefID, v))
}

// RawAlertRefIDContainsFold applies the ContainsFold predicate on the "raw_alert_ref_id" field.
func RawAlertRefIDContainsFold(v string) predicate.Alert {
	return predicate.Alert(sql.FieldContainsFold(FieldRawAlertRefID, v))
}

// TitleEQ applies the EQ predicate on the "title" field.
func TitleEQ(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldTitle, v))
}

// TitleNEQ applies the NEQ predicate on the "title" field.
func TitleNEQ(v string) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldTitle, v))
}

// TitleIn applies the In predicate on the "title" field.
func TitleIn(vs ...string) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldTitle, vs...))
}

// TitleNotIn applies the NotIn predicate on the "title" field.
func TitleNotIn(vs ...string) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldTitle, vs...))
}

// TitleGT applies the GT predicate on the "title" field.
func TitleGT(v string) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldTitle, v))
}

// TitleGTE applies the GTE predicate on the "title" field.
func TitleGTE(v string) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldTitle, v))
}

// TitleLT applies the LT predicate on the "title" field.
func TitleLT(v string) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldTitle, v))
}

// TitleLTE applies the LTE predicate on the "title" field.
func TitleLTE(v string) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldTitle, v))
}

// TitleContains applies the Contains predicate on the "title" field.
func TitleContains(v string) predicate.Alert {
	return predicate.Alert(sql.FieldContains(FieldTitle, v))
}

// TitleHasPrefix applies the HasPrefix predicate on the "title" field.
func TitleHasPrefix(v string) predicate.Alert {
	return predicate.Alert(sql.FieldHasPrefix(FieldTitle, v))
}

// TitleHasSuffix applies the HasSuffix predicate on the "title" field.
func TitleHasSuffix(v string) predicate.Alert {
	return predicate.Alert(sql.FieldHasSuffix(FieldTitle, v))
}

// TitleEqualFold applies the EqualFold predicate on the "title" field.
func TitleEqualFold(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEqualFold(FieldTitle, v))
}

// TitleContainsFold applies the ContainsFold predicate on the "title" field.
func TitleContainsFold(v string) predicate.Alert {
	return predicate.Alert(sql.FieldContainsFold(FieldTitle, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.Alert {
	return predicate.Alert(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.Alert {
	return predicate.Alert(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.Alert {
	return predicate.Alert(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionIsNil applies the IsNil predicate on the "description" field.
func DescriptionIsNil() predicate.Alert {
	return predicate.Alert(sql.FieldIsNull(FieldDescription))
}

// DescriptionNotNil applies the NotNil predicate on the "description" field.
func DescriptionNotNil() predicate.Alert {
	return predicate.Alert(sql.FieldNotNull(FieldDescription))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.Alert {
	return predicate.Alert(sql.FieldContainsFold(FieldDescription, v))
}

// AlertSeverityEQ applies the EQ predicate on the "alert_severity" field.
func AlertSeverityEQ(v AlertSeverity) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldAlertSeverity, v))
}

// AlertSeverityNEQ applies the NEQ predicate on the "alert_severity" field.
func AlertSeverityNEQ(v AlertSeverity) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldAlertSeverity, v))
}

// AlertSeverityIn applies the In predicate on the "alert_severity" field.
func AlertSeverityIn(vs ...AlertSeverity) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldAlertSeverity, vs...))
}

// AlertSeverityNotIn applies the NotIn predicate on the "alert_severity" field.
func AlertSeverityNotIn(vs ...AlertSeverity) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldAlertSeverity, vs...))
}

// AlertStatusEQ applies the EQ predicate on the "alert_status" field.
func AlertStatusEQ(v AlertStatus) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldAlertStatus, v))
}

// AlertStatusNEQ applies the NEQ predicate on the "alert_status" field.
func AlertStatusNEQ(v AlertStatus) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldAlertStatus, v))
}

// AlertStatusIn applies the In predicate on the "alert_status" field.
func AlertStatusIn(vs ...AlertStatus) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldAlertStatus, vs...))
}

// AlertStatusNotIn applies the NotIn predicate on the "alert_status" field.
func AlertStatusNotIn(vs ...AlertStatus) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldAlertStatus, vs...))
}

// ProgressEQ applies the EQ predicate on the "progress" field.
func ProgressEQ(v Progress) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldProgress, v))
}

// ProgressNEQ applies the NEQ predicate on the "progress" field.
func ProgressNEQ(v Progress) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldProgress, v))
}

// ProgressIn applies the In predicate on the "progress" field.
func ProgressIn(vs ...Progress) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldProgress, vs...))
}

// ProgressNotIn applies the NotIn predicate on the "progress" field.
func ProgressNotIn(vs ...Progress) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldProgress, vs...))
}

// AlertKeyEQ applies the EQ predicate on the "alert_key" field.
func AlertKeyEQ(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldAlertKey, v))
}

// AlertKeyNEQ applies the NEQ predicate on the "alert_key" field.
func AlertKeyNEQ(v string) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldAlertKey, v))
}

// AlertKeyIn applies the In predicate on the "alert_key" field.
func AlertKeyIn(vs ...string) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldAlertKey, vs...))
}

// AlertKeyNotIn applies the NotIn predicate on the "alert_key" field.
func AlertKeyNotIn(vs ...string) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldAlertKey, vs...))
}

// AlertKeyGT applies the GT predicate on the "alert_key" field.
func AlertKeyGT(v string) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldAlertKey, v))
}

// AlertKeyGTE applies the GTE predicate on the "alert_key" field.
func AlertKeyGTE(v string) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldAlertKey, v))
}

// AlertKeyLT applies the LT predicate on the "alert_key" field.
func AlertKeyLT(v string) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldAlertKey, v))
}

// AlertKeyLTE applies the LTE predicate on the "alert_key" field.
func AlertKeyLTE(v string) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldAlertKey, v))
}

// AlertKeyContains applies the Contains predicate on the "alert_key" field.
func AlertKeyContains(v string) predicate.Alert {
	return predicate.Alert(sql.FieldContains(FieldAlertKey, v))
}

// AlertKeyHasPrefix applies the HasPrefix predicate on the "alert_key" field.
func AlertKeyHasPrefix(v string) predicate.Alert {
	return predicate.Alert(sql.FieldHasPrefix(FieldAlertKey, v))
}

// AlertKeyHasSuffix applies the HasSuffix predicate on the "alert_key" field.
func AlertKeyHasSuffix(v string) predicate.Alert {
	return predicate.Alert(sql.FieldHasSuffix(FieldAlertKey, v))
}

// AlertKeyEqualFold applies the EqualFold predicate on the "alert_key" field.
func AlertKeyEqualFold(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEqualFold(FieldAlertKey, v))
}

// AlertKeyContainsFold applies the ContainsFold predicate on the "alert_key" field.
func AlertKeyContainsFold(v string) predicate.Alert {
	return predicate.Alert(sql.FieldContainsFold(FieldAlertKey, v))
}

// StartTimeEQ applies the EQ predicate on the "start_time" field.
func StartTimeEQ(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldStartTime, v))
}

// StartTimeNEQ applies the NEQ predicate on the "start_time" field.
func StartTimeNEQ(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldStartTime, v))
}

// StartTimeIn applies the In predicate on the "start_time" field.
func StartTimeIn(vs ...time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldStartTime, vs...))
}

// StartTimeNotIn applies the NotIn predicate on the "start_time" field.
func StartTimeNotIn(vs ...time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldStartTime, vs...))
}

// StartTimeGT applies the GT predicate on the "start_time" field.
func StartTimeGT(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldStartTime, v))
}

// StartTimeGTE applies the GTE predicate on the "start_time" field.
func StartTimeGTE(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldStartTime, v))
}

// StartTimeLT applies the LT predicate on the "start_time" field.
func StartTimeLT(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldStartTime, v))
}

// StartTimeLTE applies the LTE predicate on the "start_time" field.
func StartTimeLTE(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldStartTime, v))
}

// LastTimeEQ applies the EQ predicate on the "last_time" field.
func LastTimeEQ(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldLastTime, v))
}

// LastTimeNEQ applies the NEQ predicate on the "last_time" field.
func LastTimeNEQ(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldLastTime, v))
}

// LastTimeIn applies the In predicate on the "last_time" field.
func LastTimeIn(vs ...time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldLastTime, vs...))
}

// LastTimeNotIn applies the NotIn predicate on the "last_time" field.
func LastTimeNotIn(vs ...time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldLastTime, vs...))
}

// LastTimeGT applies the GT predicate on the "last_time" field.
func LastTimeGT(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldLastTime, v))
}

// LastTimeGTE applies the GTE predicate on the "last_time" field.
func LastTimeGTE(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldLastTime, v))
}

// LastTimeLT applies the LT predicate on the "last_time" field.
func LastTimeLT(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldLastTime, v))
}

// LastTimeLTE applies the LTE predicate on the "last_time" field.
func LastTimeLTE(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldLastTime, v))
}

// EndTimeEQ applies the EQ predicate on the "end_time" field.
func EndTimeEQ(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldEndTime, v))
}

// EndTimeNEQ applies the NEQ predicate on the "end_time" field.
func EndTimeNEQ(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldEndTime, v))
}

// EndTimeIn applies the In predicate on the "end_time" field.
func EndTimeIn(vs ...time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldEndTime, vs...))
}

// EndTimeNotIn applies the NotIn predicate on the "end_time" field.
func EndTimeNotIn(vs ...time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldEndTime, vs...))
}

// EndTimeGT applies the GT predicate on the "end_time" field.
func EndTimeGT(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldEndTime, v))
}

// EndTimeGTE applies the GTE predicate on the "end_time" field.
func EndTimeGTE(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldEndTime, v))
}

// EndTimeLT applies the LT predicate on the "end_time" field.
func EndTimeLT(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldEndTime, v))
}

// EndTimeLTE applies the LTE predicate on the "end_time" field.
func EndTimeLTE(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldEndTime, v))
}

// EndTimeIsNil applies the IsNil predicate on the "end_time" field.
func EndTimeIsNil() predicate.Alert {
	return predicate.Alert(sql.FieldIsNull(FieldEndTime))
}

// EndTimeNotNil applies the NotNil predicate on the "end_time" field.
func EndTimeNotNil() predicate.Alert {
	return predicate.Alert(sql.FieldNotNull(FieldEndTime))
}

// AckTimeEQ applies the EQ predicate on the "ack_time" field.
func AckTimeEQ(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldAckTime, v))
}

// AckTimeNEQ applies the NEQ predicate on the "ack_time" field.
func AckTimeNEQ(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldAckTime, v))
}

// AckTimeIn applies the In predicate on the "ack_time" field.
func AckTimeIn(vs ...time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldAckTime, vs...))
}

// AckTimeNotIn applies the NotIn predicate on the "ack_time" field.
func AckTimeNotIn(vs ...time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldAckTime, vs...))
}

// AckTimeGT applies the GT predicate on the "ack_time" field.
func AckTimeGT(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldAckTime, v))
}

// AckTimeGTE applies the GTE predicate on the "ack_time" field.
func AckTimeGTE(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldAckTime, v))
}

// AckTimeLT applies the LT predicate on the "ack_time" field.
func AckTimeLT(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldAckTime, v))
}

// AckTimeLTE applies the LTE predicate on the "ack_time" field.
func AckTimeLTE(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldAckTime, v))
}

// AckTimeIsNil applies the IsNil predicate on the "ack_time" field.
func AckTimeIsNil() predicate.Alert {
	return predicate.Alert(sql.FieldIsNull(FieldAckTime))
}

// AckTimeNotNil applies the NotNil predicate on the "ack_time" field.
func AckTimeNotNil() predicate.Alert {
	return predicate.Alert(sql.FieldNotNull(FieldAckTime))
}

// CloseTimeEQ applies the EQ predicate on the "close_time" field.
func CloseTimeEQ(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldCloseTime, v))
}

// CloseTimeNEQ applies the NEQ predicate on the "close_time" field.
func CloseTimeNEQ(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldCloseTime, v))
}

// CloseTimeIn applies the In predicate on the "close_time" field.
func CloseTimeIn(vs ...time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldCloseTime, vs...))
}

// CloseTimeNotIn applies the NotIn predicate on the "close_time" field.
func CloseTimeNotIn(vs ...time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldCloseTime, vs...))
}

// CloseTimeGT applies the GT predicate on the "close_time" field.
func CloseTimeGT(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldCloseTime, v))
}

// CloseTimeGTE applies the GTE predicate on the "close_time" field.
func CloseTimeGTE(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldCloseTime, v))
}

// CloseTimeLT applies the LT predicate on the "close_time" field.
func CloseTimeLT(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldCloseTime, v))
}

// CloseTimeLTE applies the LTE predicate on the "close_time" field.
func CloseTimeLTE(v time.Time) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldCloseTime, v))
}

// CloseTimeIsNil applies the IsNil predicate on the "close_time" field.
func CloseTimeIsNil() predicate.Alert {
	return predicate.Alert(sql.FieldIsNull(FieldCloseTime))
}

// CloseTimeNotNil applies the NotNil predicate on the "close_time" field.
func CloseTimeNotNil() predicate.Alert {
	return predicate.Alert(sql.FieldNotNull(FieldCloseTime))
}

// EverMutedEQ applies the EQ predicate on the "ever_muted" field.
func EverMutedEQ(v bool) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldEverMuted, v))
}

// EverMutedNEQ applies the NEQ predicate on the "ever_muted" field.
func EverMutedNEQ(v bool) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldEverMuted, v))
}

// EventCntEQ applies the EQ predicate on the "event_cnt" field.
func EventCntEQ(v int) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldEventCnt, v))
}

// EventCntNEQ applies the NEQ predicate on the "event_cnt" field.
func EventCntNEQ(v int) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldEventCnt, v))
}

// EventCntIn applies the In predicate on the "event_cnt" field.
func EventCntIn(vs ...int) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldEventCnt, vs...))
}

// EventCntNotIn applies the NotIn predicate on the "event_cnt" field.
func EventCntNotIn(vs ...int) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldEventCnt, vs...))
}

// EventCntGT applies the GT predicate on the "event_cnt" field.
func EventCntGT(v int) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldEventCnt, v))
}

// EventCntGTE applies the GTE predicate on the "event_cnt" field.
func EventCntGTE(v int) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldEventCnt, v))
}

// EventCntLT applies the LT predicate on the "event_cnt" field.
func EventCntLT(v int) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldEventCnt, v))
}

// EventCntLTE applies the LTE predicate on the "event_cnt" field.
func EventCntLTE(v int) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldEventCnt, v))
}

// IntegrationsIDEQ applies the EQ predicate on the "integrations_id" field.
func IntegrationsIDEQ(v uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldIntegrationsID, v))
}

// IntegrationsIDNEQ applies the NEQ predicate on the "integrations_id" field.
func IntegrationsIDNEQ(v uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldIntegrationsID, v))
}

// IntegrationsIDIn applies the In predicate on the "integrations_id" field.
func IntegrationsIDIn(vs ...uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldIntegrationsID, vs...))
}

// IntegrationsIDNotIn applies the NotIn predicate on the "integrations_id" field.
func IntegrationsIDNotIn(vs ...uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldIntegrationsID, vs...))
}

// IntegrationsIDGT applies the GT predicate on the "integrations_id" field.
func IntegrationsIDGT(v uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldIntegrationsID, v))
}

// IntegrationsIDGTE applies the GTE predicate on the "integrations_id" field.
func IntegrationsIDGTE(v uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldIntegrationsID, v))
}

// IntegrationsIDLT applies the LT predicate on the "integrations_id" field.
func IntegrationsIDLT(v uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldIntegrationsID, v))
}

// IntegrationsIDLTE applies the LTE predicate on the "integrations_id" field.
func IntegrationsIDLTE(v uuid.UUID) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldIntegrationsID, v))
}

// SpaceIDEQ applies the EQ predicate on the "space_id" field.
func SpaceIDEQ(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldSpaceID, v))
}

// SpaceIDNEQ applies the NEQ predicate on the "space_id" field.
func SpaceIDNEQ(v string) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldSpaceID, v))
}

// SpaceIDIn applies the In predicate on the "space_id" field.
func SpaceIDIn(vs ...string) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldSpaceID, vs...))
}

// SpaceIDNotIn applies the NotIn predicate on the "space_id" field.
func SpaceIDNotIn(vs ...string) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldSpaceID, vs...))
}

// SpaceIDGT applies the GT predicate on the "space_id" field.
func SpaceIDGT(v string) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldSpaceID, v))
}

// SpaceIDGTE applies the GTE predicate on the "space_id" field.
func SpaceIDGTE(v string) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldSpaceID, v))
}

// SpaceIDLT applies the LT predicate on the "space_id" field.
func SpaceIDLT(v string) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldSpaceID, v))
}

// SpaceIDLTE applies the LTE predicate on the "space_id" field.
func SpaceIDLTE(v string) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldSpaceID, v))
}

// SpaceIDContains applies the Contains predicate on the "space_id" field.
func SpaceIDContains(v string) predicate.Alert {
	return predicate.Alert(sql.FieldContains(FieldSpaceID, v))
}

// SpaceIDHasPrefix applies the HasPrefix predicate on the "space_id" field.
func SpaceIDHasPrefix(v string) predicate.Alert {
	return predicate.Alert(sql.FieldHasPrefix(FieldSpaceID, v))
}

// SpaceIDHasSuffix applies the HasSuffix predicate on the "space_id" field.
func SpaceIDHasSuffix(v string) predicate.Alert {
	return predicate.Alert(sql.FieldHasSuffix(FieldSpaceID, v))
}

// SpaceIDEqualFold applies the EqualFold predicate on the "space_id" field.
func SpaceIDEqualFold(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEqualFold(FieldSpaceID, v))
}

// SpaceIDContainsFold applies the ContainsFold predicate on the "space_id" field.
func SpaceIDContainsFold(v string) predicate.Alert {
	return predicate.Alert(sql.FieldContainsFold(FieldSpaceID, v))
}

// IncidentIDEQ applies the EQ predicate on the "incident_id" field.
func IncidentIDEQ(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEQ(FieldIncidentID, v))
}

// IncidentIDNEQ applies the NEQ predicate on the "incident_id" field.
func IncidentIDNEQ(v string) predicate.Alert {
	return predicate.Alert(sql.FieldNEQ(FieldIncidentID, v))
}

// IncidentIDIn applies the In predicate on the "incident_id" field.
func IncidentIDIn(vs ...string) predicate.Alert {
	return predicate.Alert(sql.FieldIn(FieldIncidentID, vs...))
}

// IncidentIDNotIn applies the NotIn predicate on the "incident_id" field.
func IncidentIDNotIn(vs ...string) predicate.Alert {
	return predicate.Alert(sql.FieldNotIn(FieldIncidentID, vs...))
}

// IncidentIDGT applies the GT predicate on the "incident_id" field.
func IncidentIDGT(v string) predicate.Alert {
	return predicate.Alert(sql.FieldGT(FieldIncidentID, v))
}

// IncidentIDGTE applies the GTE predicate on the "incident_id" field.
func IncidentIDGTE(v string) predicate.Alert {
	return predicate.Alert(sql.FieldGTE(FieldIncidentID, v))
}

// IncidentIDLT applies the LT predicate on the "incident_id" field.
func IncidentIDLT(v string) predicate.Alert {
	return predicate.Alert(sql.FieldLT(FieldIncidentID, v))
}

// IncidentIDLTE applies the LTE predicate on the "incident_id" field.
func IncidentIDLTE(v string) predicate.Alert {
	return predicate.Alert(sql.FieldLTE(FieldIncidentID, v))
}

// IncidentIDContains applies the Contains predicate on the "incident_id" field.
func IncidentIDContains(v string) predicate.Alert {
	return predicate.Alert(sql.FieldContains(FieldIncidentID, v))
}

// IncidentIDHasPrefix applies the HasPrefix predicate on the "incident_id" field.
func IncidentIDHasPrefix(v string) predicate.Alert {
	return predicate.Alert(sql.FieldHasPrefix(FieldIncidentID, v))
}

// IncidentIDHasSuffix applies the HasSuffix predicate on the "incident_id" field.
func IncidentIDHasSuffix(v string) predicate.Alert {
	return predicate.Alert(sql.FieldHasSuffix(FieldIncidentID, v))
}

// IncidentIDIsNil applies the IsNil predicate on the "incident_id" field.
func IncidentIDIsNil() predicate.Alert {
	return predicate.Alert(sql.FieldIsNull(FieldIncidentID))
}

// IncidentIDNotNil applies the NotNil predicate on the "incident_id" field.
func IncidentIDNotNil() predicate.Alert {
	return predicate.Alert(sql.FieldNotNull(FieldIncidentID))
}

// IncidentIDEqualFold applies the EqualFold predicate on the "incident_id" field.
func IncidentIDEqualFold(v string) predicate.Alert {
	return predicate.Alert(sql.FieldEqualFold(FieldIncidentID, v))
}

// IncidentIDContainsFold applies the ContainsFold predicate on the "incident_id" field.
func IncidentIDContainsFold(v string) predicate.Alert {
	return predicate.Alert(sql.FieldContainsFold(FieldIncidentID, v))
}

// HasRawAlert applies the HasEdge predicate on the "raw_alert" edge.
func HasRawAlert() predicate.Alert {
	return predicate.Alert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, RawAlertTable, RawAlertColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasRawAlertWith applies the HasEdge predicate on the "raw_alert" edge with a given conditions (other predicates).
func HasRawAlertWith(preds ...predicate.RawAlert) predicate.Alert {
	return predicate.Alert(func(s *sql.Selector) {
		step := newRawAlertStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasIncident applies the HasEdge predicate on the "incident" edge.
func HasIncident() predicate.Alert {
	return predicate.Alert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, IncidentTable, IncidentPrimaryKey...),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasIncidentWith applies the HasEdge predicate on the "incident" edge with a given conditions (other predicates).
func HasIncidentWith(preds ...predicate.Incident) predicate.Alert {
	return predicate.Alert(func(s *sql.Selector) {
		step := newIncidentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Alert) predicate.Alert {
	return predicate.Alert(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Alert) predicate.Alert {
	return predicate.Alert(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Alert) predicate.Alert {
	return predicate.Alert(sql.NotPredicates(p))
}
