package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/gofrs/uuid/v5"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_common/utils/orm/mixins"
)

type Alert struct {
	ent.Schema
}

func (Alert) Fields() []ent.Field {
	return []ent.Field{
		// field.String("id").Unique().Immutable().Comment("唯一ID"),
		field.String("raw_alert_ref_id").Optional().Comment("原始告警ID"), // 修改字段名，避免与edge冲突
		field.String("title").Comment("告警标题"),
		field.String("description").Optional().Comment("描述"),
		field.Enum("alert_severity").Values("Info", "Warning", "Critical").Comment("告警严重程度"),
		field.Enum("alert_status").Values("Info", "Warning", "Critical", "Ok").Comment("告警状态"),
		field.Enum("progress").Values("Triggered", "Processing", "Closed").Comment("处理进度"),
		field.String("alert_key").Comment("拥有相同alert_key的告警事件，将合并为同一个告警即为一条故障消息通知（原始告警有聚合）"),
		field.Time("start_time").Comment("开始时间"),
		field.Time("last_time").Comment("最后更新时间"),
		field.Time("end_time").Optional().Comment("结束时间"),
		field.Time("ack_time").Optional().Comment("确认时间"),
		field.Time("close_time").Optional().Comment("关闭时间"),
		field.JSON("labels", map[string]string{}).Comment("标签"),
		field.Bool("ever_muted").Default(false).Comment("是否被静默过"),
		field.Int("event_cnt").Default(1).Comment("事件计数"),
		field.JSON("raw_data", map[string]interface{}{}).Comment("原始完整数据"),
		field.UUID("integrations_id", uuid.Nil).Comment("集成ID"),
		field.String("space_id").Comment("空间ID"),
		field.String("incident_id").Optional().Comment("关联故障ID"),
	}
}

func (Alert) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("id"),
		index.Fields("integrations_id"),
		index.Fields("start_time"), // TimescaleDB超表分区字段
		index.Fields("space_id"),
		index.Fields("incident_id"),
		index.Fields("alert_key"),
		index.Fields("raw_alert_ref_id"),
	}
}

func (Alert) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixins.UUIDMixin{},
	}
}

func (Alert) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("raw_alert", RawAlert.Type).Unique().Comment("关联原始告警"), // 一对一关系
		// edge.From("integration", oncall_schema.Integration.Type).Ref("alerts").Comment("关联集成"),
		edge.From("incident", Incident.Type).Ref("alerts").Comment("关联故障"),
		// edge.From("space", oncall_schema.Space.Type).Ref("alerts").Comment("关联空间"),
	}
}

func (Alert) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.WithComments(true),
		entsql.Annotation{Table: "oncall_alerts"},
	}
}
