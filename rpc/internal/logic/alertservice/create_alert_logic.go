package alertservicelogic

import (
	"context"
	"strings"
	"time"

	"github.com/gofrs/uuid/v5"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/alert"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pb/message"
)

type CreateAlertLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateAlertLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateAlertLogic {
	return &CreateAlertLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// CreateAlert 创建告警
func (l *CreateAlertLogic) CreateAlert(in *message.CreateAlertRequest) (*message.BaseUUIDResp, error) {
	alertData := in.GetAlert()

	// 检查是否存在相同告警键的告警
	alertKey := alertData.GetAlertKey()
	integrationsID := alertData.GetIntegrationsId()

	existingAlert, err := l.svcCtx.DB.Alert.Query().
		Where(alert.AlertKey(alertKey)).
		Where(alert.IntegrationsID(uuid.FromStringOrNil(integrationsID))).
		First(l.ctx)

	// 如果存在相同告警，更新计数和最后更新时间
	if err == nil && existingAlert != nil {
		// 更新现有告警
		updatedAlert, err := l.svcCtx.DB.Alert.UpdateOneID(existingAlert.ID).
			SetLastTime(time.Now()).
			SetEventCnt(existingAlert.EventCnt + 1).
			Save(l.ctx)

		if err != nil {
			return nil, err
		}

		return &message.BaseUUIDResp{
			Id:  updatedAlert.ID.String(),
			Msg: "告警更新成功",
		}, nil
	}

	// 解析严重程度
	var severityEnum alert.AlertSeverity
	var statusEnum alert.AlertStatus

	switch strings.ToLower(alertData.GetAlertSeverity().String()) {
	case "critical":
		severityEnum = alert.AlertSeverityCritical
		statusEnum = alert.AlertStatusCritical
	case "warning":
		severityEnum = alert.AlertSeverityWarning
		statusEnum = alert.AlertStatusWarning
	default:
		severityEnum = alert.AlertSeverityInfo
		statusEnum = alert.AlertStatusInfo
	}

	// 创建新告警
	now := time.Now()

	// 解析标签
	labels := alertData.GetLabels()
	if labels == nil {
		labels = make(map[string]string)
	}

	// 构建原始数据
	rawData := map[string]interface{}{
		"title":           alertData.GetTitle(),
		"description":     alertData.GetDescription(),
		"alert_severity":  alertData.GetAlertSeverity().String(),
		"alert_key":       alertKey,
		"integrations_id": integrationsID,
		"space_id":        alertData.GetSpaceId(),
		"labels":          labels,
	}

	// 创建告警记录
	alertCreate := l.svcCtx.DB.Alert.Create().
		SetTitle(alertData.GetTitle()).
		SetDescription(alertData.GetDescription()).
		SetAlertSeverity(severityEnum).
		SetAlertStatus(statusEnum). // 初始状态与严重程度相同
		SetProgress(alert.ProgressTriggered).
		SetAlertKey(alertKey).
		SetStartTime(now).
		SetLastTime(now).
		SetLabels(labels).
		SetEventCnt(1).
		SetRawData(rawData). // 设置原始数据
		SetIntegrationsID(uuid.FromStringOrNil(integrationsID)).
		SetSpaceID(alertData.GetSpaceId())

	// 如果有原始告警ID，设置关联关系
	if rawAlertID := alertData.GetRawAlertId(); rawAlertID != "" {
		// 尝试将字符串转换为UUID
		rawAlertUUID, err := uuid.FromString(rawAlertID)
		if err == nil {
			alertCreate = alertCreate.SetRawAlertID(rawAlertUUID)
		}
	}

	// 保存告警
	newAlert, err := alertCreate.Save(l.ctx)

	if err != nil {
		return nil, err
	}

	return &message.BaseUUIDResp{
		Id:  newAlert.ID.String(),
		Msg: "告警创建成功",
	}, nil
}

// CreateStandardAlert 从处理后的告警数据创建标准告警
func (l *CreateAlertLogic) CreateStandardAlert(ctx context.Context, processedAlert map[string]interface{}, rawAlertID, spaceID string) (string, error) {
	// 提取告警信息
	title, _ := processedAlert["title"].(string)
	description, _ := processedAlert["description"].(string)
	alertSeverity, _ := processedAlert["alert_severity"].(string)
	alertKey, _ := processedAlert["alert_key"].(string)
	integrationsID, _ := processedAlert["integrations_id"].(string)
	labels, _ := processedAlert["labels"].(map[string]string)
	rawData, _ := processedAlert["raw_data"].(map[string]interface{})

	// 如果没有原始数据，使用处理后的数据作为原始数据
	// if rawData == nil {
	// 	rawData = processedAlert
	// }

	// 检查是否存在相同告警键的告警
	existingAlert, err := l.svcCtx.DB.Alert.Query().
		Where(alert.AlertKey(alertKey)).
		Where(alert.IntegrationsID(uuid.FromStringOrNil(integrationsID))).
		First(ctx)

	// 如果存在相同告警，更新计数和最后更新时间
	if err == nil && existingAlert != nil {
		// 更新现有告警
		updatedAlert, err := l.svcCtx.DB.Alert.UpdateOneID(existingAlert.ID).
			SetLastTime(time.Now()).
			SetEventCnt(existingAlert.EventCnt + 1).
			Save(ctx)

		if err != nil {
			return "", err
		}

		return updatedAlert.ID.String(), nil
	}

	// 解析严重程度
	var severityEnum alert.AlertSeverity
	var statusEnum alert.AlertStatus

	switch strings.ToLower(alertSeverity) {
	case "critical":
		severityEnum = alert.AlertSeverityCritical
		statusEnum = alert.AlertStatusCritical
	case "warning":
		severityEnum = alert.AlertSeverityWarning
		statusEnum = alert.AlertStatusWarning
	default:
		severityEnum = alert.AlertSeverityInfo
		statusEnum = alert.AlertStatusInfo
	}

	// 创建新告警
	now := time.Now()

	// 创建告警记录
	alertCreate := l.svcCtx.DB.Alert.Create().
		SetTitle(title).
		SetDescription(description).
		SetAlertSeverity(severityEnum).
		SetAlertStatus(statusEnum). // 初始状态与严重程度相同
		SetProgress(alert.ProgressTriggered).
		SetAlertKey(alertKey).
		SetStartTime(now).
		SetLastTime(now).
		SetLabels(labels).
		SetEventCnt(1).
		SetRawData(rawData). // 设置原始数据
		SetIntegrationsID(uuid.FromStringOrNil(integrationsID)).
		SetSpaceID(spaceID)

	// 如果有原始告警ID，设置关联关系
	if rawAlertID != "" {
		// 尝试将字符串转换为UUID
		rawAlertUUID, err := uuid.FromString(rawAlertID)
		if err == nil {
			alertCreate = alertCreate.SetRawAlertID(rawAlertUUID)
		}
	}

	// 保存告警
	newAlert, err := alertCreate.Save(ctx)

	if err != nil {
		return "", err
	}

	return newAlert.ID.String(), nil
}

// UpdateAlertIncidentID 更新告警关联的故障ID
func (l *CreateAlertLogic) UpdateAlertIncidentID(ctx context.Context, alertID, incidentID string) error {
	// 更新告警的故障ID
	_, err := l.svcCtx.DB.Alert.UpdateOneID(uuid.FromStringOrNil(alertID)).
		SetIncidentID(incidentID).
		Save(ctx)

	return err
}
