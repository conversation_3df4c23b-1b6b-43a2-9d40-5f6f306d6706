package channel

import (
	"bytes"
	"context"
	"crypto/md5"
	"crypto/sha1"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
)

// WPSNotifier WPS协作通知器
type WPSNotifier struct {
	appID   string
	appKey  string
	openAPI string
	client  *http.Client
}

// NewWPSNotifier 创建WPS协作通知器
func NewWPSNotifier(appID, appKey, openAPI string) *WPSNotifier {
	return &WPSNotifier{
		appID:   appID,
		appKey:  appKey,
		openAPI: openAPI,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// Type 返回通知渠道类型
func (n *WPSNotifier) Type() notifier.ChannelType {
	return "wps"
}

// Send 发送WPS协作通知
func (n *WPSNotifier) Send(ctx context.Context, data *notifier.NotificationData) *notifier.NotificationResult {
	// 检查必要参数
	if n.appID == "" || n.appKey == "" || n.openAPI == "" {
		return &notifier.NotificationResult{
			Success: false,
			Message: "WPS协作配置不完整",
			Error:   fmt.Errorf("WPS协作配置不完整"),
		}
	}

	// 检查接收人
	if len(data.Recipients) == 0 {
		return &notifier.NotificationResult{
			Success: false,
			Message: "没有指定接收人",
			Error:   fmt.Errorf("没有指定接收人"),
		}
	}

	// 获取企业token
	companyToken, err := n.getCompanyToken(ctx)
	if err != nil {
		return &notifier.NotificationResult{
			Success: false,
			Message: "获取企业token失败",
			Error:   fmt.Errorf("获取企业token失败: %w", err),
		}
	}

	// 构建消息体
	var messageBody map[string]interface{}

	// 优先使用预渲染的消息体
	if channelData, ok := data.ChannelData["wps"].(map[string]interface{}); ok {
		if preRenderedBody, ok := channelData["message_body"].(map[string]interface{}); ok {
			messageBody = preRenderedBody
		}
	}

	// 如果没有预渲染的消息体，则构建默认消息体
	if messageBody == nil {
		messageBody, err = n.buildMessageBody(data)
		if err != nil {
			return &notifier.NotificationResult{
				Success: false,
				Message: "构建消息体失败",
				Error:   fmt.Errorf("构建消息体失败: %w", err),
			}
		}
	}

	// 获取用户ID列表
	companyUIDs, err := n.getUserIDs(ctx, companyToken, data.Recipients)
	if err != nil {
		return &notifier.NotificationResult{
			Success: false,
			Message: "获取用户ID失败",
			Error:   fmt.Errorf("获取用户ID失败: %w", err),
		}
	}

	if len(companyUIDs) == 0 {
		return &notifier.NotificationResult{
			Success: false,
			Message: "没有找到有效的用户",
			Error:   fmt.Errorf("没有找到有效的用户"),
		}
	}

	// 获取企业ID
	companyID, err := n.getCompanyID(ctx, companyToken)
	if err != nil {
		return &notifier.NotificationResult{
			Success: false,
			Message: "获取企业ID失败",
			Error:   fmt.Errorf("获取企业ID失败: %w", err),
		}
	}

	// 设置接收用户
	messageBody["to_users"] = map[string]interface{}{
		"company_id":   companyID,
		"company_uids": companyUIDs,
	}

	// 从渠道数据中获取聊天组ID
	if channelData, ok := data.ChannelData["wps"].(map[string]interface{}); ok {
		if chatIDs, ok := channelData["chat_ids"].([]interface{}); ok {
			var chatList []int
			for _, chatID := range chatIDs {
				if id, ok := chatID.(int); ok {
					chatList = append(chatList, id)
				} else if idFloat, ok := chatID.(float64); ok {
					chatList = append(chatList, int(idFloat))
				}
			}
			if len(chatList) > 0 {
				messageBody["to_chats"] = chatList
			}
		}
	}

	// 发送消息
	url := fmt.Sprintf("/kopen/woa/v2/dev/app/messages?company_token=%s", companyToken)
	err = n.sendRequest(ctx, "POST", url, messageBody)
	if err != nil {
		return &notifier.NotificationResult{
			Success: false,
			Message: "发送消息失败",
			Error:   fmt.Errorf("发送消息失败: %w", err),
		}
	}

	return &notifier.NotificationResult{
		Success: true,
		Message: "成功发送WPS协作通知",
	}
}

// getCompanyToken 获取企业token
func (n *WPSNotifier) getCompanyToken(ctx context.Context) (string, error) {
	url := fmt.Sprintf("/oauthapi/v3/inner/company/token?app_id=%s", n.appID)

	var response map[string]interface{}
	err := n.sendRequest(ctx, "GET", url, nil, &response)
	if err != nil {
		return "", err
	}

	if companyToken, ok := response["company_token"].(string); ok {
		return companyToken, nil
	}

	return "", fmt.Errorf("响应中未找到company_token")
}

// getUserIDs 获取用户ID列表
func (n *WPSNotifier) getUserIDs(ctx context.Context, companyToken string, emails []string) ([]string, error) {
	var companyUIDs []string

	for _, email := range emails {
		url := fmt.Sprintf("/oauthapi/v3/company/company_user/byemail?company_token=%s&email=%s", companyToken, email)

		var response map[string]interface{}
		err := n.sendRequest(ctx, "GET", url, nil, &response)
		if err != nil {
			continue // 跳过失败的用户
		}

		if companyUID, ok := response["company_uid"].(string); ok {
			companyUIDs = append(companyUIDs, companyUID)
		}
	}

	return companyUIDs, nil
}

// getCompanyID 获取企业ID
func (n *WPSNotifier) getCompanyID(ctx context.Context, companyToken string) (string, error) {
	url := fmt.Sprintf("/plus/v1/company?company_token=%s", companyToken)

	var response map[string]interface{}
	err := n.sendRequest(ctx, "GET", url, nil, &response)
	if err != nil {
		return "", err
	}

	if company, ok := response["company"].(map[string]interface{}); ok {
		if companyID, ok := company["company_id"].(string); ok {
			return companyID, nil
		}
	}

	return "", fmt.Errorf("响应中未找到company_id")
}

// buildMessageBody 构建消息体
func (n *WPSNotifier) buildMessageBody(data *notifier.NotificationData) (map[string]interface{}, error) {
	// 使用渲染后的标题和内容，如果没有则使用原始内容
	title := data.Title
	content := data.Content
	if data.RenderedTitle != "" {
		title = data.RenderedTitle
	}
	if data.RenderedContent != "" {
		content = data.RenderedContent
	}

	// 根据严重程度确定颜色和图标
	color, icon := n.getSeverityStyle(data.Severity)

	// 构建消息体
	messageBody := map[string]interface{}{
		"app_key":  n.appID,
		"msg_type": 23,
		"content": map[string]interface{}{
			"type": 23,
			"content": map[string]interface{}{
				"header": map[string]interface{}{
					"template": color,
					"title": map[string]interface{}{
						"tag": "text",
						"content": map[string]interface{}{
							"text": fmt.Sprintf("%s %s", icon, title),
							"type": "plainText",
						},
					},
				},
				"elements": n.buildMessageElements(data, content),
			},
		},
	}

	return messageBody, nil
}

// getSeverityStyle 根据严重程度获取样式
func (n *WPSNotifier) getSeverityStyle(severity string) (string, string) {
	switch severity {
	case "critical":
		return "red", "🚨"
	case "warning":
		return "yellow", "⚠️"
	case "info":
		return "green", "ℹ️"
	default:
		return "grey", "❓"
	}
}

// buildMessageElements 构建消息元素
func (n *WPSNotifier) buildMessageElements(data *notifier.NotificationData, content string) []map[string]interface{} {
	elements := []map[string]interface{}{
		{"tag": "hr", "style": "solid"},
	}

	// 添加基本信息
	basicInfo := fmt.Sprintf("**📊 告警概览**\n\n- 严重程度：%s\n- 告警ID：%s", data.Severity, data.AlertID)
	if data.IncidentID != "" {
		basicInfo += fmt.Sprintf("\n- 故障ID：%s", data.IncidentID)
	}

	elements = append(elements, map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": basicInfo,
					"type": "markdown",
				},
			},
		},
	})

	// 添加告警内容
	if content != "" {
		elements = append(elements, map[string]interface{}{"tag": "hr", "style": "dashed"})
		elements = append(elements, map[string]interface{}{
			"tag": "div",
			"fields": []map[string]interface{}{
				{
					"tag": "text",
					"content": map[string]interface{}{
						"text": fmt.Sprintf("**📝 告警详情**\n\n%s", content),
						"type": "markdown",
					},
				},
			},
		})
	}

	// 添加标签信息
	if len(data.Labels) > 0 {
		elements = append(elements, map[string]interface{}{"tag": "hr", "style": "dashed"})

		labelText := "**🏷️ 标签信息**\n\n"
		for k, v := range data.Labels {
			labelText += fmt.Sprintf("- %s: %s\n", k, v)
		}

		elements = append(elements, map[string]interface{}{
			"tag": "div",
			"fields": []map[string]interface{}{
				{
					"tag": "text",
					"content": map[string]interface{}{
						"text": labelText,
						"type": "markdown",
					},
				},
			},
		})
	}

	// 添加时间信息
	elements = append(elements, map[string]interface{}{"tag": "hr", "style": "dashed"})
	elements = append(elements, map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": fmt.Sprintf("**⏰ 通知时间**\n\n%s", time.Now().Format("2006-01-02 15:04:05")),
					"type": "markdown",
				},
			},
		},
	})

	return elements
}

// generateSignature 生成WPS-3签名
func (n *WPSNotifier) generateSignature(contentMD5, url, date string) string {
	sha1Hash := sha1.New()
	sha1Hash.Write([]byte(n.appKey))
	sha1Hash.Write([]byte(contentMD5))
	sha1Hash.Write([]byte(url))
	sha1Hash.Write([]byte("application/json"))
	sha1Hash.Write([]byte(date))

	return fmt.Sprintf("WPS-3:%s:%x", n.appID, sha1Hash.Sum(nil))
}

// sendRequest 发送HTTP请求
func (n *WPSNotifier) sendRequest(ctx context.Context, method, uri string, body interface{}, result ...interface{}) error {
	var bodyBytes []byte
	var contentMD5 string

	// 处理请求体
	if body != nil && (method == "POST" || method == "PUT" || method == "DELETE") {
		var err error
		bodyBytes, err = json.Marshal(body)
		if err != nil {
			return fmt.Errorf("序列化请求体失败: %w", err)
		}
		contentMD5 = fmt.Sprintf("%x", md5.Sum(bodyBytes))
	} else {
		contentMD5 = fmt.Sprintf("%x", md5.Sum([]byte("")))
	}

	// 生成时间戳
	date := time.Now().UTC().Format("Mon, 02 Jan 2006 15:04:05 GMT")

	// 生成签名
	signature := n.generateSignature(contentMD5, uri, date)

	// 构建请求
	url := n.openAPI + uri
	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewReader(bodyBytes))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Auth", signature)
	req.Header.Set("Date", date)
	req.Header.Set("Content-Md5", contentMD5)

	// 发送请求
	resp, err := n.client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应（如果需要）
	if len(result) > 0 && result[0] != nil {
		if err := json.Unmarshal(respBody, result[0]); err != nil {
			return fmt.Errorf("解析响应失败: %w", err)
		}
	}

	return nil
}
