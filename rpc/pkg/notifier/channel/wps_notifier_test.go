package channel

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier/template"
)

// MockWPSConfig Mock WPS配置
type MockWPSConfig struct {
	AppID   string
	AppKey  string
	OpenAPI string
}

// GetMockWPSConfig 获取Mock WPS配置
func GetMockWPSConfig() MockWPSConfig {
	return MockWPSConfig{
		AppID:   "AK20231216PDPGPI",
		AppKey:  "deff6800d41d3b296eb41ce4a01094ef",
		OpenAPI: "https://openapi.wps.cn",
	}
}

// TestWPSNotifier_AliyunEventAlert 测试阿里云事件告警通知
func TestWPSNotifier_AliyunEventAlert(t *testing.T) {
	config := GetMockWPSConfig()
	
	// 创建WPS通知器
	wpsNotifier := NewWPSNotifier(config.AppID, config.AppKey, config.OpenAPI)
	
	// 创建模板构建器
	templateBuilder := template.NewWPSTemplateBuilder(config.AppID)
	
	// 模拟阿里云事件告警数据
	alertData := createMockAliyunEventData()
	
	// 构建通知数据
	notificationData := &notifier.NotificationData{
		Title:      "实例遭受DDoS攻击中",
		Content:    "阿里云ECS实例正在遭受DDoS攻击",
		AlertID:    "alert-12345",
		IncidentID: "incident-67890",
		Severity:   "critical",
		Recipients: []string{
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
		},
		Labels:     alertData.Labels,
		AlertData:  alertData.RawData,
		ChannelData: map[string]interface{}{
			"wps": map[string]interface{}{
				"chat_ids": []int{41887203}, // 测试聊天组ID
			},
		},
	}
	
	// 使用模板构建消息
	messageBody := templateBuilder.BuildAliyunEventTemplate(notificationData)
	
	// 打印消息体用于调试
	messageJSON, _ := json.MarshalIndent(messageBody, "", "  ")
	fmt.Printf("阿里云事件告警消息体:\n%s\n", string(messageJSON))
	
	// 注意：这里不实际发送消息，只是构建和验证消息格式
	fmt.Printf("WPS通知器类型: %s\n", wpsNotifier.Type())
	fmt.Printf("消息构建成功，包含 %d 个元素\n", len(messageBody["content"].(map[string]interface{})["content"].(map[string]interface{})["elements"].([]map[string]interface{})))
}

// TestWPSNotifier_AliyunMetricsAlert 测试阿里云指标告警通知
func TestWPSNotifier_AliyunMetricsAlert(t *testing.T) {
	config := GetMockWPSConfig()
	
	// 创建模板构建器
	templateBuilder := template.NewWPSTemplateBuilder(config.AppID)
	
	// 模拟阿里云指标告警数据
	alertData := createMockAliyunMetricsData()
	
	// 构建通知数据
	notificationData := &notifier.NotificationData{
		Title:      "CPU使用率过高",
		Content:    "ECS实例CPU使用率超过90%",
		AlertID:    "alert-54321",
		IncidentID: "incident-09876",
		Severity:   "warning",
		Recipients: []string{
			"<EMAIL>",
			"<EMAIL>",
		},
		Labels:     alertData.Labels,
		AlertData:  alertData.RawData,
		ChannelData: map[string]interface{}{
			"wps": map[string]interface{}{
				"chat_ids": []int{41887203},
			},
		},
	}
	
	// 使用模板构建消息
	messageBody := templateBuilder.BuildAliyunMetricsTemplate(notificationData)
	
	// 打印消息体用于调试
	messageJSON, _ := json.MarshalIndent(messageBody, "", "  ")
	fmt.Printf("阿里云指标告警消息体:\n%s\n", string(messageJSON))
	
	fmt.Printf("消息构建成功，包含 %d 个元素\n", len(messageBody["content"].(map[string]interface{})["content"].(map[string]interface{})["elements"].([]map[string]interface{})))
}

// TestWPSNotifier_GenericAlert 测试通用告警通知
func TestWPSNotifier_GenericAlert(t *testing.T) {
	config := GetMockWPSConfig()
	
	// 创建模板构建器
	templateBuilder := template.NewWPSTemplateBuilder(config.AppID)
	
	// 构建通用告警数据
	notificationData := &notifier.NotificationData{
		Title:           "系统告警",
		Content:         "系统出现异常，请及时处理",
		RenderedTitle:   "🚨 [严重] 系统告警",
		RenderedContent: "**告警详情**\n\n系统出现异常，CPU使用率过高，内存不足。\n\n**处理建议**\n\n1. 检查系统负载\n2. 清理内存\n3. 重启服务",
		AlertID:         "alert-generic-001",
		IncidentID:      "incident-generic-001",
		Severity:        "critical",
		Recipients: []string{
			"<EMAIL>",
		},
		Labels: map[string]string{
			"service":     "web-server",
			"environment": "production",
			"region":      "beijing",
			"team":        "ops",
		},
		ChannelData: map[string]interface{}{
			"wps": map[string]interface{}{
				"chat_ids": []int{41887203},
			},
		},
	}
	
	// 使用模板构建消息
	messageBody := templateBuilder.BuildGenericTemplate(notificationData)
	
	// 打印消息体用于调试
	messageJSON, _ := json.MarshalIndent(messageBody, "", "  ")
	fmt.Printf("通用告警消息体:\n%s\n", string(messageJSON))
	
	fmt.Printf("消息构建成功，包含 %d 个元素\n", len(messageBody["content"].(map[string]interface{})["content"].(map[string]interface{})["elements"].([]map[string]interface{})))
}

// TestWPSNotifier_MessageSigning 测试WPS消息签名
func TestWPSNotifier_MessageSigning(t *testing.T) {
	config := GetMockWPSConfig()
	
	// 创建WPS通知器
	wpsNotifier := NewWPSNotifier(config.AppID, config.AppKey, config.OpenAPI)
	
	// 测试签名生成
	contentMD5 := "d41d8cd98f00b204e9800998ecf8427e"
	url := "/test/api"
	date := "Mon, 02 Jan 2006 15:04:05 GMT"
	
	signature := wpsNotifier.generateSignature(contentMD5, url, date)
	
	fmt.Printf("签名测试:\n")
	fmt.Printf("Content-MD5: %s\n", contentMD5)
	fmt.Printf("URL: %s\n", url)
	fmt.Printf("Date: %s\n", date)
	fmt.Printf("Signature: %s\n", signature)
	
	// 验证签名格式
	expectedPrefix := fmt.Sprintf("WPS-3:%s:", config.AppID)
	if len(signature) < len(expectedPrefix) || signature[:len(expectedPrefix)] != expectedPrefix {
		t.Errorf("签名格式错误，期望前缀: %s, 实际签名: %s", expectedPrefix, signature)
	} else {
		fmt.Printf("签名格式验证通过\n")
	}
}

// createMockAliyunEventData 创建模拟阿里云事件数据
func createMockAliyunEventData() *MockAlertData {
	labels := map[string]string{
		"severity":           "CRITICAL",
		"strategy_name":      "实例遭受DDoS攻击中",
		"product":            "ECS",
		"service_type_zh":    "云服务器ECS",
		"event_name_zh":      "实例遭受DDoS攻击中",
		"event_type":         "Exception",
		"region_name_zh":     "华北2（北京）",
		"instance_name":      "test-server-001",
		"instance_id":        "i-bp1234567890abcdef",
		"event_id":           "evt-bp1234567890abcdef",
		"reason":             "实例正在遭受DDoS攻击",
		"reason_code":        "DDoS.Attack.Detected",
		"eip_address":        "47.93.123.456",
		"private_ip_address": "************",
		"publish_time":       "2024-01-15T10:30:00Z",
		"execute_start_time": "2024-01-15T10:30:00Z",
		"event_status":       "Normal",
	}
	
	rawData := map[string]interface{}{
		"severity":      "CRITICAL",
		"strategyName":  "实例遭受DDoS攻击中",
		"alert": map[string]interface{}{
			"product":     "ECS",
			"alertStatus": "ALERT",
			"eventType":   "Exception",
			"groupName":   "默认应用分组",
			"meta": map[string]interface{}{
				"sysEventMeta": map[string]interface{}{
					"serviceTypeZh": "云服务器ECS",
					"eventNameZh":   "实例遭受DDoS攻击中",
					"eventType":     "Exception",
					"regionNameZh":  "华北2（北京）",
					"instanceName":  "test-server-001",
				},
			},
			"eventContentMap": map[string]interface{}{
				"eventId":           "evt-bp1234567890abcdef",
				"instanceId":        "i-bp1234567890abcdef",
				"reason":            "实例正在遭受DDoS攻击",
				"reasonCode":        "DDoS.Attack.Detected",
				"eipAddress":        "47.93.123.456",
				"privateIpAddress":  []string{"************"},
				"publishTime":       "2024-01-15T10:30:00Z",
				"executeStartTime":  "2024-01-15T10:30:00Z",
				"eventStatus":       "Normal",
			},
		},
	}
	
	return &MockAlertData{
		Labels:  labels,
		RawData: rawData,
	}
}

// createMockAliyunMetricsData 创建模拟阿里云指标数据
func createMockAliyunMetricsData() *MockAlertData {
	labels := map[string]string{
		"alert_name":     "CPU使用率过高",
		"metric_name":    "CPUUtilization",
		"current_value":  "95.6",
		"unit":           "%",
		"trigger_level":  "WARN",
		"alert_state":    "ALERT",
		"expression":     "CPUUtilization >= 90",
		"instance_name":  "web-server-prod-001",
		"region_name":    "cn-beijing",
		"instance_id":    "i-bp1234567890abcdef",
		"alert_time":     "2024-01-15 18:30:00",
		"rule_id":        "rule-12345",
	}
	
	rawData := map[string]interface{}{
		"alertName":     "CPU使用率过高",
		"metricName":    "CPUUtilization",
		"curValue":      "95.6",
		"unit":          "%",
		"triggerLevel":  "WARN",
		"alertState":    "ALERT",
		"expression":    "CPUUtilization >= 90",
		"instanceName":  "web-server-prod-001",
		"regionName":    "cn-beijing",
		"timestamp":     "1705315800000",
		"ruleId":        "rule-12345",
		"dimensions":    "{instanceId=i-bp1234567890abcdef,userId=123456789}",
	}
	
	return &MockAlertData{
		Labels:  labels,
		RawData: rawData,
	}
}

// MockAlertData 模拟告警数据
type MockAlertData struct {
	Labels  map[string]string
	RawData map[string]interface{}
}
