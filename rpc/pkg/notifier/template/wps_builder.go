package template

import (
	"fmt"
	"time"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
)

// WPSMessageBuilder WPS消息构建器
type WPSMessageBuilder struct {
	appID string
}

// NewWPSMessageBuilder 创建WPS消息构建器
func NewWPSMessageBuilder(appID string) *WPSMessageBuilder {
	return &WPSMessageBuilder{
		appID: appID,
	}
}

// BuildAliyunEventMessage 构建阿里云事件告警消息
func (b *WPSMessageBuilder) BuildAliyunEventMessage(data *notifier.NotificationData) map[string]interface{} {
	color, icon := b.getSeverityStyle(data.Severity)

	// 构建标题
	serviceType := b.getLabel(data.Labels, "service_type_zh", "阿里云")
	title := fmt.Sprintf("%s [%s] %s", icon, serviceType, data.Title)

	// 构建元素
	elements := []map[string]interface{}{
		{"tag": "hr", "style": "solid"},
		b.buildEventOverview(data),
		{"tag": "hr", "style": "dashed"},
		b.buildResourceInfo(data),
		{"tag": "hr", "style": "dashed"},
		b.buildEventDetails(data),
		{"tag": "hr", "style": "dashed"},
		b.buildTimeInfo(data),
	}

	return map[string]interface{}{
		"app_key":  b.appID,
		"msg_type": 23,
		"content": map[string]interface{}{
			"type": 23,
			"content": map[string]interface{}{
				"header": map[string]interface{}{
					"template": color,
					"title": map[string]interface{}{
						"tag": "text",
						"content": map[string]interface{}{
							"text": title,
							"type": "plainText",
						},
					},
				},
				"elements": elements,
			},
		},
	}
}

// BuildAliyunMetricsMessage 构建阿里云指标告警消息
func (b *WPSMessageBuilder) BuildAliyunMetricsMessage(data *notifier.NotificationData) map[string]interface{} {
	color, icon := b.getSeverityStyle(data.Severity)

	// 构建标题
	title := fmt.Sprintf("%s [阿里云] %s", icon, data.Title)

	// 构建元素
	elements := []map[string]interface{}{
		{"tag": "hr", "style": "solid"},
		b.buildMetricsOverview(data),
		{"tag": "hr", "style": "dashed"},
		b.buildResourceInfo(data),
		{"tag": "hr", "style": "dashed"},
		b.buildSimpleTimeInfo(data),
	}

	return map[string]interface{}{
		"app_key":  b.appID,
		"msg_type": 23,
		"content": map[string]interface{}{
			"type": 23,
			"content": map[string]interface{}{
				"header": map[string]interface{}{
					"template": color,
					"title": map[string]interface{}{
						"tag": "text",
						"content": map[string]interface{}{
							"text": title,
							"type": "plainText",
						},
					},
				},
				"elements": elements,
			},
		},
	}
}

// BuildGenericMessage 构建通用告警消息
func (b *WPSMessageBuilder) BuildGenericMessage(data *notifier.NotificationData) map[string]interface{} {
	color, icon := b.getSeverityStyle(data.Severity)

	// 使用渲染后的标题，如果没有则使用原始标题
	title := data.Title
	if data.RenderedTitle != "" {
		title = data.RenderedTitle
	}
	title = fmt.Sprintf("%s %s", icon, title)

	// 构建元素
	elements := []map[string]interface{}{
		{"tag": "hr", "style": "solid"},
		b.buildBasicOverview(data),
	}

	// 添加内容（如果有）
	if data.Content != "" || data.RenderedContent != "" {
		elements = append(elements,
			map[string]interface{}{"tag": "hr", "style": "dashed"},
			b.buildContentInfo(data),
		)
	}

	// 添加标签（如果有）
	if len(data.Labels) > 0 {
		elements = append(elements,
			map[string]interface{}{"tag": "hr", "style": "dashed"},
			b.buildLabelsInfo(data),
		)
	}

	// 添加时间信息
	elements = append(elements,
		map[string]interface{}{"tag": "hr", "style": "dashed"},
		b.buildNotificationTime(),
	)

	return map[string]interface{}{
		"app_key":  b.appID,
		"msg_type": 23,
		"content": map[string]interface{}{
			"type": 23,
			"content": map[string]interface{}{
				"header": map[string]interface{}{
					"template": color,
					"title": map[string]interface{}{
						"tag": "text",
						"content": map[string]interface{}{
							"text": title,
							"type": "plainText",
						},
					},
				},
				"elements": elements,
			},
		},
	}
}

// getSeverityStyle 根据严重程度获取样式
func (b *WPSMessageBuilder) getSeverityStyle(severity string) (string, string) {
	switch severity {
	case "critical":
		return "red", "🚨"
	case "warning":
		return "yellow", "⚠️"
	case "info":
		return "green", "ℹ️"
	default:
		return "grey", "❓"
	}
}

// getLabel 获取标签值，如果不存在则返回默认值
func (b *WPSMessageBuilder) getLabel(labels map[string]string, key, defaultValue string) string {
	if value, ok := labels[key]; ok && value != "" {
		return value
	}
	return defaultValue
}

// buildEventOverview 构建事件概览
func (b *WPSMessageBuilder) buildEventOverview(data *notifier.NotificationData) map[string]interface{} {
	text := "**📊 事件概览**\n\n"
	text += fmt.Sprintf("- 告警等级：%s\n", b.getLabel(data.Labels, "severity", data.Severity))
	text += fmt.Sprintf("- 事件状态：%s\n", b.getLabel(data.Labels, "event_status", "未知"))
	text += fmt.Sprintf("- 事件类型：%s\n", b.getLabel(data.Labels, "event_type", "未知"))
	text += fmt.Sprintf("- 所属地域：%s\n", b.getLabel(data.Labels, "region_name_zh", "未知"))
	text += fmt.Sprintf("- 业务分组：%s", b.getLabel(data.Labels, "group_name", "未知"))

	return map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": text,
					"type": "markdown",
				},
			},
		},
	}
}

// buildMetricsOverview 构建指标概览
func (b *WPSMessageBuilder) buildMetricsOverview(data *notifier.NotificationData) map[string]interface{} {
	text := "**📊 告警概览**\n\n"
	text += fmt.Sprintf("- 指标名称：%s\n", b.getLabel(data.Labels, "metric_name", "未知"))

	currentValue := b.getLabel(data.Labels, "current_value", "0")
	unit := b.getLabel(data.Labels, "unit", "")
	text += fmt.Sprintf("- 当前值：%s%s\n", currentValue, unit)

	text += fmt.Sprintf("- 触发条件：%s\n", b.getLabel(data.Labels, "expression", "未知"))
	text += fmt.Sprintf("- 告警等级：%s\n", b.getLabel(data.Labels, "trigger_level", data.Severity))
	text += fmt.Sprintf("- 告警状态：%s", b.getLabel(data.Labels, "alert_state", "未知"))

	return map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": text,
					"type": "markdown",
				},
			},
		},
	}
}

// buildResourceInfo 构建资源信息
func (b *WPSMessageBuilder) buildResourceInfo(data *notifier.NotificationData) map[string]interface{} {
	text := "**🖥️ 资源信息**\n\n"
	text += fmt.Sprintf("- 实例名称：%s\n", b.getLabel(data.Labels, "instance_name", "未知"))
	text += fmt.Sprintf("- 实例ID：%s\n", b.getLabel(data.Labels, "instance_id", "未知"))
	text += fmt.Sprintf("- 私有IP：%s\n", b.getLabel(data.Labels, "private_ip_address", "无"))
	text += fmt.Sprintf("- 公网IP：%s\n", b.getLabel(data.Labels, "public_ip_address", "无"))
	text += fmt.Sprintf("- 弹性IP：%s", b.getLabel(data.Labels, "eip_address", "未知"))

	return map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": text,
					"type": "markdown",
				},
			},
		},
	}
}

// buildEventDetails 构建事件详情
func (b *WPSMessageBuilder) buildEventDetails(data *notifier.NotificationData) map[string]interface{} {
	text := "**🔍 事件详情**\n\n"
	text += fmt.Sprintf("- 事件ID：%s\n", b.getLabel(data.Labels, "event_id", "未知"))
	text += fmt.Sprintf("- 原因：%s\n", b.getLabel(data.Labels, "reason", "未知"))
	text += fmt.Sprintf("- 事件代码：%s", b.getLabel(data.Labels, "reason_code", "未知"))

	return map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": text,
					"type": "markdown",
				},
			},
		},
	}
}

// buildTimeInfo 构建时间信息
func (b *WPSMessageBuilder) buildTimeInfo(data *notifier.NotificationData) map[string]interface{} {
	text := "**⏰ 时间信息**\n\n"
	text += fmt.Sprintf("- 发布时间：%s\n", b.formatTime(b.getLabel(data.Labels, "publish_time", "未知")))
	text += fmt.Sprintf("- 开始时间：%s\n", b.formatTime(b.getLabel(data.Labels, "execute_start_time", "未知")))
	text += fmt.Sprintf("- 结束时间：%s\n", b.formatTime(b.getLabel(data.Labels, "execute_finish_time", "未知")))
	text += fmt.Sprintf("- 通知时间：%s", time.Now().Format("2006-01-02 15:04:05"))

	return map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": text,
					"type": "markdown",
				},
			},
		},
	}
}

// buildBasicOverview 构建基本概览
func (b *WPSMessageBuilder) buildBasicOverview(data *notifier.NotificationData) map[string]interface{} {
	text := "**📊 告警概览**\n\n"
	text += fmt.Sprintf("- 严重程度：%s\n", data.Severity)

	if data.AlertID != "" {
		text += fmt.Sprintf("- 告警ID：%s\n", data.AlertID)
	}

	if data.IncidentID != "" {
		text += fmt.Sprintf("- 故障ID：%s", data.IncidentID)
	}

	return map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": text,
					"type": "markdown",
				},
			},
		},
	}
}

// buildContentInfo 构建内容信息
func (b *WPSMessageBuilder) buildContentInfo(data *notifier.NotificationData) map[string]interface{} {
	content := data.Content
	if data.RenderedContent != "" {
		content = data.RenderedContent
	}

	text := fmt.Sprintf("**📝 告警详情**\n\n%s", content)

	return map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": text,
					"type": "markdown",
				},
			},
		},
	}
}

// buildLabelsInfo 构建标签信息
func (b *WPSMessageBuilder) buildLabelsInfo(data *notifier.NotificationData) map[string]interface{} {
	text := "**🏷️ 标签信息**\n\n"

	for key, value := range data.Labels {
		text += fmt.Sprintf("- %s: %s\n", key, value)
	}

	return map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": text,
					"type": "markdown",
				},
			},
		},
	}
}

// buildNotificationTime 构建通知时间
func (b *WPSMessageBuilder) buildNotificationTime() map[string]interface{} {
	text := fmt.Sprintf("**⏰ 通知时间**\n\n%s", time.Now().Format("2006-01-02 15:04:05"))

	return map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": text,
					"type": "markdown",
				},
			},
		},
	}
}

// formatTime 格式化时间字符串
func (b *WPSMessageBuilder) formatTime(timeStr string) string {
	if timeStr == "未知" || timeStr == "" {
		return "未知"
	}

	// 尝试解析ISO时间格式
	if t, err := time.Parse(time.RFC3339, timeStr); err == nil {
		// 转换为东八区时间
		cst := time.FixedZone("CST", 8*3600)
		return t.In(cst).Format("2006-01-02 15:04:05")
	}

	// 如果解析失败，返回原始字符串
	return timeStr
}

// buildSimpleTimeInfo 构建简单时间信息
func (b *WPSMessageBuilder) buildSimpleTimeInfo(data *notifier.NotificationData) map[string]interface{} {
	alertTime := b.getLabel(data.Labels, "alert_time", time.Now().Format("2006-01-02 15:04:05"))
	text := fmt.Sprintf("**⏰ 告警时间**\n\n%s", alertTime)

	return map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": text,
					"type": "markdown",
				},
			},
		},
	}
}
