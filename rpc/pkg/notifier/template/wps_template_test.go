package template

import (
	"encoding/json"
	"fmt"
	"testing"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
)

// TestWPSTemplateRenderer_AliyunEventTemplate 测试阿里云事件模板渲染
func TestWPSTemplateRenderer_AliyunEventTemplate(t *testing.T) {
	renderer := NewWPSTemplateRenderer("AK20231216PDPGPI")
	
	// 模拟阿里云事件告警数据
	notificationData := &notifier.NotificationData{
		Title:    "实例遭受DDoS攻击中",
		Content:  "阿里云ECS实例正在遭受DDoS攻击",
		Severity: "critical",
		Labels: map[string]string{
			"severity":           "CRITICAL",
			"service_type_zh":    "云服务器ECS",
			"event_status":       "Normal",
			"event_type":         "Exception",
			"region_name_zh":     "华北2（北京）",
			"group_name":         "默认应用分组",
			"instance_name":      "test-server-001",
			"instance_id":        "i-bp1234567890abcdef",
			"private_ip_address": "************",
			"eip_address":        "47.93.123.456",
			"event_id":           "evt-bp1234567890abcdef",
			"reason":             "实例正在遭受DDoS攻击",
			"reason_code":        "DDoS.Attack.Detected",
			"publish_time":       "2024-01-15T10:30:00Z",
			"execute_start_time": "2024-01-15T10:30:00Z",
		},
		AlertData: map[string]interface{}{
			"source": "aliyun_event",
		},
	}
	
	// 渲染模板
	result, err := renderer.RenderTemplate(GetAliyunEventTemplate(), notificationData)
	if err != nil {
		t.Fatalf("渲染阿里云事件模板失败: %v", err)
	}
	
	// 验证结果
	if result["app_key"] != "AK20231216PDPGPI" {
		t.Errorf("app_key不正确，期望: AK20231216PDPGPI, 实际: %v", result["app_key"])
	}
	
	if result["msg_type"] != float64(23) {
		t.Errorf("msg_type不正确，期望: 23, 实际: %v", result["msg_type"])
	}
	
	// 打印结果用于调试
	resultJSON, _ := json.MarshalIndent(result, "", "  ")
	fmt.Printf("阿里云事件模板渲染结果:\n%s\n", string(resultJSON))
}

// TestWPSTemplateRenderer_AliyunMetricsTemplate 测试阿里云指标模板渲染
func TestWPSTemplateRenderer_AliyunMetricsTemplate(t *testing.T) {
	renderer := NewWPSTemplateRenderer("AK20231216PDPGPI")
	
	// 模拟阿里云指标告警数据
	notificationData := &notifier.NotificationData{
		Title:    "CPU使用率过高",
		Content:  "ECS实例CPU使用率超过90%",
		Severity: "warning",
		Labels: map[string]string{
			"metric_name":    "CPUUtilization",
			"current_value":  "95.6",
			"unit":           "%",
			"trigger_level":  "WARN",
			"alert_state":    "ALERT",
			"expression":     "CPUUtilization >= 90",
			"instance_name":  "web-server-prod-001",
			"region_name":    "cn-beijing",
			"instance_id":    "i-bp1234567890abcdef",
			"user_id":        "123456789",
			"alert_time":     "2024-01-15 18:30:00",
		},
		AlertData: map[string]interface{}{
			"source": "aliyun_metrics",
		},
	}
	
	// 渲染模板
	result, err := renderer.RenderTemplate(GetAliyunMetricsTemplate(), notificationData)
	if err != nil {
		t.Fatalf("渲染阿里云指标模板失败: %v", err)
	}
	
	// 验证结果
	if result["app_key"] != "AK20231216PDPGPI" {
		t.Errorf("app_key不正确，期望: AK20231216PDPGPI, 实际: %v", result["app_key"])
	}
	
	// 打印结果用于调试
	resultJSON, _ := json.MarshalIndent(result, "", "  ")
	fmt.Printf("阿里云指标模板渲染结果:\n%s\n", string(resultJSON))
}

// TestWPSTemplateRenderer_GenericTemplate 测试通用模板渲染
func TestWPSTemplateRenderer_GenericTemplate(t *testing.T) {
	renderer := NewWPSTemplateRenderer("AK20231216PDPGPI")
	
	// 模拟通用告警数据
	notificationData := &notifier.NotificationData{
		Title:    "系统告警",
		Content:  "系统出现异常，请及时处理",
		Severity: "critical",
		Labels: map[string]string{
			"service":      "web-server",
			"environment":  "production",
			"region":       "beijing",
			"team":         "ops",
			"cpu_usage":    "95%",
			"memory_usage": "88%",
		},
		AlertData: map[string]interface{}{
			"id":          "alert-generic-001",
			"incident_id": "incident-generic-001",
			"source":      "monitoring-system",
		},
	}
	
	// 渲染模板
	result, err := renderer.RenderTemplate(GetGenericTemplate(), notificationData)
	if err != nil {
		t.Fatalf("渲染通用模板失败: %v", err)
	}
	
	// 验证结果
	if result["app_key"] != "AK20231216PDPGPI" {
		t.Errorf("app_key不正确，期望: AK20231216PDPGPI, 实际: %v", result["app_key"])
	}
	
	// 打印结果用于调试
	resultJSON, _ := json.MarshalIndent(result, "", "  ")
	fmt.Printf("通用模板渲染结果:\n%s\n", string(resultJSON))
}

// TestWPSTemplateRenderer_TemplateFunctions 测试模板函数
func TestWPSTemplateRenderer_TemplateFunctions(t *testing.T) {
	renderer := NewWPSTemplateRenderer("AK20231216PDPGPI")
	
	// 测试模板函数的简单模板
	testTemplate := `{
  "app_key": "{{.AppID}}",
  "test_data": {
    "upper_severity": "{{.Severity | upper}}",
    "lower_title": "{{.Title | lower}}",
    "default_value": "{{.Labels.missing_key | default \"默认值\"}}",
    "time": "{{.Time}}",
    "icon": "{{.Icon}}",
    "color": "{{.Color}}"
  }
}`
	
	notificationData := &notifier.NotificationData{
		Title:    "Test Alert",
		Severity: "warning",
		Labels: map[string]string{
			"existing_key": "existing_value",
		},
	}
	
	// 渲染模板
	result, err := renderer.RenderTemplate(testTemplate, notificationData)
	if err != nil {
		t.Fatalf("渲染测试模板失败: %v", err)
	}
	
	// 验证模板函数
	testData := result["test_data"].(map[string]interface{})
	
	if testData["upper_severity"] != "WARNING" {
		t.Errorf("upper函数不正确，期望: WARNING, 实际: %v", testData["upper_severity"])
	}
	
	if testData["lower_title"] != "test alert" {
		t.Errorf("lower函数不正确，期望: test alert, 实际: %v", testData["lower_title"])
	}
	
	if testData["default_value"] != "默认值" {
		t.Errorf("default函数不正确，期望: 默认值, 实际: %v", testData["default_value"])
	}
	
	if testData["icon"] != "⚠️" {
		t.Errorf("icon不正确，期望: ⚠️, 实际: %v", testData["icon"])
	}
	
	if testData["color"] != "yellow" {
		t.Errorf("color不正确，期望: yellow, 实际: %v", testData["color"])
	}
	
	// 打印结果用于调试
	resultJSON, _ := json.MarshalIndent(result, "", "  ")
	fmt.Printf("模板函数测试结果:\n%s\n", string(resultJSON))
}

// TestWPSTemplateRenderer_InvalidTemplate 测试无效模板
func TestWPSTemplateRenderer_InvalidTemplate(t *testing.T) {
	renderer := NewWPSTemplateRenderer("AK20231216PDPGPI")
	
	// 无效的JSON模板
	invalidTemplate := `{
  "app_key": "{{.AppID}}",
  "invalid_json": {{.InvalidField}
}`
	
	notificationData := &notifier.NotificationData{
		Title:    "Test Alert",
		Severity: "info",
	}
	
	// 渲染模板应该失败
	_, err := renderer.RenderTemplate(invalidTemplate, notificationData)
	if err == nil {
		t.Error("期望渲染无效模板时返回错误，但没有返回错误")
	}
	
	fmt.Printf("无效模板错误（期望的）: %v\n", err)
}

// TestWPSTemplateRenderer_EmptyData 测试空数据
func TestWPSTemplateRenderer_EmptyData(t *testing.T) {
	renderer := NewWPSTemplateRenderer("AK20231216PDPGPI")
	
	// 使用空数据
	notificationData := &notifier.NotificationData{
		Severity: "info",
	}
	
	// 渲染通用模板
	result, err := renderer.RenderTemplate(GetGenericTemplate(), notificationData)
	if err != nil {
		t.Fatalf("渲染空数据模板失败: %v", err)
	}
	
	// 验证结果
	if result["app_key"] != "AK20231216PDPGPI" {
		t.Errorf("app_key不正确，期望: AK20231216PDPGPI, 实际: %v", result["app_key"])
	}
	
	// 打印结果用于调试
	resultJSON, _ := json.MarshalIndent(result, "", "  ")
	fmt.Printf("空数据模板渲染结果:\n%s\n", string(resultJSON))
}
