package template

import (
	"encoding/json"
	"fmt"
	"testing"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
)

// TestWPSMessageBuilder_AliyunEvent 测试阿里云事件消息构建
func TestWPSMessageBuilder_AliyunEvent(t *testing.T) {
	builder := NewWPSMessageBuilder("AK20231216PDPGPI")
	
	// 模拟阿里云事件告警数据
	notificationData := &notifier.NotificationData{
		Title:    "实例遭受DDoS攻击中",
		Content:  "阿里云ECS实例正在遭受DDoS攻击",
		Severity: "critical",
		AlertID:  "alert-001",
		Labels: map[string]string{
			"severity":           "CRITICAL",
			"service_type_zh":    "云服务器ECS",
			"event_status":       "Normal",
			"event_type":         "Exception",
			"region_name_zh":     "华北2（北京）",
			"group_name":         "默认应用分组",
			"instance_name":      "test-server-001",
			"instance_id":        "i-bp1234567890abcdef",
			"private_ip_address": "************",
			"eip_address":        "47.93.123.456",
			"event_id":           "evt-bp1234567890abcdef",
			"reason":             "实例正在遭受DDoS攻击",
			"reason_code":        "DDoS.Attack.Detected",
			"publish_time":       "2024-01-15T10:30:00Z",
			"execute_start_time": "2024-01-15T10:30:00Z",
		},
	}
	
	// 构建消息
	message := builder.BuildAliyunEventMessage(notificationData)
	
	// 验证结果
	if message["app_key"] != "AK20231216PDPGPI" {
		t.Errorf("app_key不正确，期望: AK20231216PDPGPI, 实际: %v", message["app_key"])
	}
	
	if message["msg_type"] != 23 {
		t.Errorf("msg_type不正确，期望: 23, 实际: %v", message["msg_type"])
	}
	
	// 验证内容结构
	content := message["content"].(map[string]interface{})
	contentData := content["content"].(map[string]interface{})
	
	// 验证标题
	header := contentData["header"].(map[string]interface{})
	if header["template"] != "red" {
		t.Errorf("模板颜色不正确，期望: red, 实际: %v", header["template"])
	}
	
	// 验证元素数量
	elements := contentData["elements"].([]map[string]interface{})
	if len(elements) != 8 { // hr + overview + hr + resource + hr + details + hr + time
		t.Errorf("元素数量不正确，期望: 8, 实际: %d", len(elements))
	}
	
	// 打印结果用于调试
	messageJSON, _ := json.MarshalIndent(message, "", "  ")
	fmt.Printf("阿里云事件消息构建结果:\n%s\n", string(messageJSON))
}

// TestWPSMessageBuilder_AliyunMetrics 测试阿里云指标消息构建
func TestWPSMessageBuilder_AliyunMetrics(t *testing.T) {
	builder := NewWPSMessageBuilder("AK20231216PDPGPI")
	
	// 模拟阿里云指标告警数据
	notificationData := &notifier.NotificationData{
		Title:    "CPU使用率过高",
		Content:  "ECS实例CPU使用率超过90%",
		Severity: "warning",
		AlertID:  "alert-002",
		Labels: map[string]string{
			"metric_name":    "CPUUtilization",
			"current_value":  "95.6",
			"unit":           "%",
			"trigger_level":  "WARN",
			"alert_state":    "ALERT",
			"expression":     "CPUUtilization >= 90",
			"instance_name":  "web-server-prod-001",
			"region_name":    "cn-beijing",
			"instance_id":    "i-bp1234567890abcdef",
			"user_id":        "123456789",
			"alert_time":     "2024-01-15 18:30:00",
		},
	}
	
	// 构建消息
	message := builder.BuildAliyunMetricsMessage(notificationData)
	
	// 验证结果
	if message["app_key"] != "AK20231216PDPGPI" {
		t.Errorf("app_key不正确，期望: AK20231216PDPGPI, 实际: %v", message["app_key"])
	}
	
	// 验证模板颜色
	content := message["content"].(map[string]interface{})
	contentData := content["content"].(map[string]interface{})
	header := contentData["header"].(map[string]interface{})
	if header["template"] != "yellow" {
		t.Errorf("模板颜色不正确，期望: yellow, 实际: %v", header["template"])
	}
	
	// 打印结果用于调试
	messageJSON, _ := json.MarshalIndent(message, "", "  ")
	fmt.Printf("阿里云指标消息构建结果:\n%s\n", string(messageJSON))
}

// TestWPSMessageBuilder_Generic 测试通用消息构建
func TestWPSMessageBuilder_Generic(t *testing.T) {
	builder := NewWPSMessageBuilder("AK20231216PDPGPI")
	
	// 模拟通用告警数据
	notificationData := &notifier.NotificationData{
		Title:           "系统告警",
		Content:         "系统出现异常，请及时处理",
		RenderedTitle:   "🚨 [严重] 系统告警",
		RenderedContent: "**告警详情**\n\n系统出现异常，CPU使用率过高，内存不足。",
		AlertID:         "alert-generic-001",
		IncidentID:      "incident-generic-001",
		Severity:        "critical",
		Labels: map[string]string{
			"service":      "web-server",
			"environment":  "production",
			"region":       "beijing",
			"team":         "ops",
			"cpu_usage":    "95%",
			"memory_usage": "88%",
		},
	}
	
	// 构建消息
	message := builder.BuildGenericMessage(notificationData)
	
	// 验证结果
	if message["app_key"] != "AK20231216PDPGPI" {
		t.Errorf("app_key不正确，期望: AK20231216PDPGPI, 实际: %v", message["app_key"])
	}
	
	// 验证模板颜色
	content := message["content"].(map[string]interface{})
	contentData := content["content"].(map[string]interface{})
	header := contentData["header"].(map[string]interface{})
	if header["template"] != "red" {
		t.Errorf("模板颜色不正确，期望: red, 实际: %v", header["template"])
	}
	
	// 验证元素包含内容和标签
	elements := contentData["elements"].([]map[string]interface{})
	if len(elements) < 6 { // 至少包含：hr + overview + hr + content + hr + labels + hr + time
		t.Errorf("元素数量不正确，期望至少: 6, 实际: %d", len(elements))
	}
	
	// 打印结果用于调试
	messageJSON, _ := json.MarshalIndent(message, "", "  ")
	fmt.Printf("通用消息构建结果:\n%s\n", string(messageJSON))
}

// TestWPSMessageBuilder_EmptyData 测试空数据处理
func TestWPSMessageBuilder_EmptyData(t *testing.T) {
	builder := NewWPSMessageBuilder("AK20231216PDPGPI")
	
	// 最小化的通知数据
	notificationData := &notifier.NotificationData{
		Title:    "最小告警",
		Severity: "info",
	}
	
	// 构建消息
	message := builder.BuildGenericMessage(notificationData)
	
	// 验证结果
	if message["app_key"] != "AK20231216PDPGPI" {
		t.Errorf("app_key不正确，期望: AK20231216PDPGPI, 实际: %v", message["app_key"])
	}
	
	// 验证模板颜色
	content := message["content"].(map[string]interface{})
	contentData := content["content"].(map[string]interface{})
	header := contentData["header"].(map[string]interface{})
	if header["template"] != "green" {
		t.Errorf("模板颜色不正确，期望: green, 实际: %v", header["template"])
	}
	
	// 打印结果用于调试
	messageJSON, _ := json.MarshalIndent(message, "", "  ")
	fmt.Printf("空数据消息构建结果:\n%s\n", string(messageJSON))
}

// TestWPSMessageBuilder_SeverityStyles 测试严重程度样式
func TestWPSMessageBuilder_SeverityStyles(t *testing.T) {
	builder := NewWPSMessageBuilder("AK20231216PDPGPI")
	
	testCases := []struct {
		severity      string
		expectedColor string
		expectedIcon  string
	}{
		{"critical", "red", "🚨"},
		{"warning", "yellow", "⚠️"},
		{"info", "green", "ℹ️"},
		{"unknown", "grey", "❓"},
	}
	
	for _, tc := range testCases {
		color, icon := builder.getSeverityStyle(tc.severity)
		if color != tc.expectedColor {
			t.Errorf("严重程度 %s 的颜色不正确，期望: %s, 实际: %s", tc.severity, tc.expectedColor, color)
		}
		if icon != tc.expectedIcon {
			t.Errorf("严重程度 %s 的图标不正确，期望: %s, 实际: %s", tc.severity, tc.expectedIcon, icon)
		}
	}
}

// TestWPSMessageBuilder_TimeFormatting 测试时间格式化
func TestWPSMessageBuilder_TimeFormatting(t *testing.T) {
	builder := NewWPSMessageBuilder("AK20231216PDPGPI")
	
	testCases := []struct {
		input    string
		expected string
	}{
		{"2024-01-15T10:30:00Z", "2024-01-15 18:30:00"}, // UTC转CST
		{"未知", "未知"},
		{"", "未知"},
		{"invalid-time", "invalid-time"},
	}
	
	for _, tc := range testCases {
		result := builder.formatTime(tc.input)
		if result != tc.expected {
			t.Errorf("时间格式化不正确，输入: %s, 期望: %s, 实际: %s", tc.input, tc.expected, result)
		}
	}
}
