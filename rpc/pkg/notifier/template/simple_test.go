package template

import (
	"encoding/json"
	"fmt"
	"testing"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
)

// TestSimpleTemplate 测试简单模板
func TestSimpleTemplate(t *testing.T) {
	renderer := NewWPSTemplateRenderer("AK20231216PDPGPI")
	
	// 简单的测试模板
	simpleTemplate := `{
  "app_key": "{{.AppID}}",
  "msg_type": 23,
  "content": {
    "type": 23,
    "content": {
      "header": {
        "template": "{{.Color}}",
        "title": {
          "tag": "text",
          "content": {
            "text": "{{.Icon}} {{.Title}}",
            "type": "plainText"
          }
        }
      },
      "elements": [
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "严重程度: {{.Severity}}",
                "type": "markdown"
              }
            }
          ]
        }
      ]
    }
  }
}`
	
	notificationData := &notifier.NotificationData{
		Title:    "测试告警",
		Severity: "critical",
		Labels:   map[string]string{"test": "value"},
	}
	
	// 渲染模板
	result, err := renderer.RenderTemplate(simpleTemplate, notificationData)
	if err != nil {
		t.Fatalf("渲染简单模板失败: %v", err)
	}
	
	// 验证结果
	if result["app_key"] != "AK20231216PDPGPI" {
		t.Errorf("app_key不正确，期望: AK20231216PDPGPI, 实际: %v", result["app_key"])
	}
	
	// 打印结果
	resultJSON, _ := json.MarshalIndent(result, "", "  ")
	fmt.Printf("简单模板渲染结果:\n%s\n", string(resultJSON))
}

// TestWPSTemplateWithNewlines 测试包含换行符的模板
func TestWPSTemplateWithNewlines(t *testing.T) {
	renderer := NewWPSTemplateRenderer("AK20231216PDPGPI")
	
	// 使用Go的多行字符串，避免转义字符问题
	templateWithNewlines := `{
  "app_key": "{{.AppID}}",
  "msg_type": 23,
  "content": {
    "type": 23,
    "content": {
      "header": {
        "template": "{{.Color}}",
        "title": {
          "tag": "text",
          "content": {
            "text": "{{.Icon}} {{.Title}}",
            "type": "plainText"
          }
        }
      },
      "elements": [
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**告警概览**` + "\n\n" + `- 严重程度：{{.Severity}}` + "\n" + `- 标题：{{.Title}}",
                "type": "markdown"
              }
            }
          ]
        }
      ]
    }
  }
}`
	
	notificationData := &notifier.NotificationData{
		Title:    "测试告警",
		Severity: "warning",
		Labels:   map[string]string{"test": "value"},
	}
	
	// 渲染模板
	result, err := renderer.RenderTemplate(templateWithNewlines, notificationData)
	if err != nil {
		t.Fatalf("渲染换行符模板失败: %v", err)
	}
	
	// 打印结果
	resultJSON, _ := json.MarshalIndent(result, "", "  ")
	fmt.Printf("换行符模板渲染结果:\n%s\n", string(resultJSON))
}
