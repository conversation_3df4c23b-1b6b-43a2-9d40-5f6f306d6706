package template

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strings"
	"text/template"
	"time"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
)

// WPSTemplateRenderer WPS协作通知模板渲染器
type WPSTemplateRenderer struct {
	appID string
}

// NewWPSTemplateRenderer 创建WPS模板渲染器
func NewWPSTemplateRenderer(appID string) *WPSTemplateRenderer {
	return &WPSTemplateRenderer{
		appID: appID,
	}
}

// TemplateData 模板数据结构
type TemplateData struct {
	Alert    map[string]interface{} `json:"alert"`
	Labels   map[string]string      `json:"labels"`
	Severity string                 `json:"severity"`
	Title    string                 `json:"title"`
	Content  string                 `json:"content"`
	Time     string                 `json:"time"`
	AppID    string                 `json:"app_id"`
	Icon     string                 `json:"icon"`
	Color    string                 `json:"color"`
}

// RenderTemplate 渲染WPS协作通知模板
func (r *WPSTemplateRenderer) RenderTemplate(templateStr string, data *notifier.NotificationData) (map[string]interface{}, error) {
	// 准备模板数据
	templateData := r.prepareTemplateData(data)

	// 创建模板
	tmpl, err := template.New("wps_notification").Funcs(r.getTemplateFuncs()).Parse(templateStr)
	if err != nil {
		return nil, fmt.Errorf("解析模板失败: %w", err)
	}

	// 渲染模板
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, templateData); err != nil {
		return nil, fmt.Errorf("渲染模板失败: %w", err)
	}

	// 解析渲染后的JSON
	var result map[string]interface{}
	if err := json.Unmarshal(buf.Bytes(), &result); err != nil {
		return nil, fmt.Errorf("解析渲染结果失败: %w", err)
	}

	return result, nil
}

// prepareTemplateData 准备模板数据
func (r *WPSTemplateRenderer) prepareTemplateData(data *notifier.NotificationData) *TemplateData {
	// 使用渲染后的标题和内容，如果没有则使用原始内容
	title := data.Title
	content := data.Content
	if data.RenderedTitle != "" {
		title = data.RenderedTitle
	}
	if data.RenderedContent != "" {
		content = data.RenderedContent
	}

	// 根据严重程度确定颜色和图标
	color, icon := r.getSeverityStyle(data.Severity)

	return &TemplateData{
		Alert:    data.AlertData,
		Labels:   data.Labels,
		Severity: data.Severity,
		Title:    title,
		Content:  content,
		Time:     time.Now().Format("2006-01-02 15:04:05"),
		AppID:    r.appID,
		Icon:     icon,
		Color:    color,
	}
}

// getSeverityStyle 根据严重程度获取样式
func (r *WPSTemplateRenderer) getSeverityStyle(severity string) (string, string) {
	switch severity {
	case "critical":
		return "red", "🚨"
	case "warning":
		return "yellow", "⚠️"
	case "info":
		return "green", "ℹ️"
	default:
		return "grey", "❓"
	}
}

// getTemplateFuncs 获取模板函数
func (r *WPSTemplateRenderer) getTemplateFuncs() template.FuncMap {
	return template.FuncMap{
		"upper":    strings.ToUpper,
		"lower":    strings.ToLower,
		"title":    strings.Title,
		"join":     strings.Join,
		"contains": strings.Contains,
		"replace":  strings.ReplaceAll,
		"now":      time.Now,
		"formatTime": func(layout, value string) string {
			if t, err := time.Parse(time.RFC3339, value); err == nil {
				return t.Format(layout)
			}
			return value
		},
		"default": func(defaultValue, value interface{}) interface{} {
			if value == nil || value == "" {
				return defaultValue
			}
			return value
		},
	}
}

// GetAliyunEventTemplate 获取阿里云事件告警模板
func GetAliyunEventTemplate() string {
	return `{
  "app_key": "{{.AppID}}",
  "msg_type": 23,
  "content": {
    "type": 23,
    "content": {
      "header": {
        "template": "{{.Color}}",
        "title": {
          "tag": "text",
          "content": {
            "text": "{{.Icon}} [{{.Labels.service_type_zh | default \"阿里云\"}}] {{.Title}}",
            "type": "plainText"
          }
        }
      },
      "elements": [
        {
          "tag": "hr",
          "style": "solid"
        },
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**📊 事件概览**\\n\\n- 告警等级：{{.Labels.severity | default .Severity}}\\n- 事件状态：{{.Labels.event_status | default \"未知\"}}\\n- 事件类型：{{.Labels.event_type | default \"未知\"}}\\n- 所属地域：{{.Labels.region_name_zh | default \"未知\"}}\\n- 业务分组：{{.Labels.group_name | default \"未知\"}}",
                "type": "markdown"
              }
            }
          ]
        },
        {
          "tag": "hr",
          "style": "dashed"
        },
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**🖥️ 资源信息**\\n\\n- 实例名称：{{.Labels.instance_name | default \"未知\"}}\\n- 实例ID：{{.Labels.instance_id | default \"未知\"}}\\n- 私有IP：{{.Labels.private_ip_address | default \"无\"}}\\n- 公网IP：{{.Labels.public_ip_address | default \"无\"}}\\n- 弹性IP：{{.Labels.eip_address | default \"未知\"}}",
                "type": "markdown"
              }
            }
          ]
        },
        {
          "tag": "hr",
          "style": "dashed"
        },
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**🔍 事件详情**\\n\\n- 事件ID：{{.Labels.event_id | default \"未知\"}}\\n- 原因：{{.Labels.reason | default \"未知\"}}\\n- 事件代码：{{.Labels.reason_code | default \"未知\"}}",
                "type": "markdown"
              }
            }
          ]
        },
        {
          "tag": "hr",
          "style": "dashed"
        },
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**⏰ 时间信息**\\n\\n- 发布时间：{{.Labels.publish_time | formatTime \"2006-01-02 15:04:05\" | default \"未知\"}}\\n- 开始时间：{{.Labels.execute_start_time | formatTime \"2006-01-02 15:04:05\" | default \"未知\"}}\\n- 结束时间：{{.Labels.execute_finish_time | formatTime \"2006-01-02 15:04:05\" | default \"未知\"}}\\n- 通知时间：{{.Time}}",
                "type": "markdown"
              }
            }
          ]
        }
      ]
    }
  }
}`
}

// GetAliyunMetricsTemplate 获取阿里云指标告警模板
func GetAliyunMetricsTemplate() string {
	return `{
  "app_key": "{{.AppID}}",
  "msg_type": 23,
  "content": {
    "type": 23,
    "content": {
      "header": {
        "template": "{{.Color}}",
        "title": {
          "tag": "text",
          "content": {
            "text": "{{.Icon}} [阿里云] {{.Title}}",
            "type": "plainText"
          }
        }
      },
      "elements": [
        {
          "tag": "hr",
          "style": "solid"
        },
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**📊 告警概览**\\n\\n- 指标名称：{{.Labels.metric_name | default \"未知\"}}\\n- 当前值：{{.Labels.current_value | default \"0\"}}{{.Labels.unit}}\\n- 触发条件：{{.Labels.expression | default \"未知\"}}\\n- 告警等级：{{.Labels.trigger_level | default .Severity}}\\n- 告警状态：{{.Labels.alert_state | default \"未知\"}}",
                "type": "markdown"
              }
            }
          ]
        },
        {
          "tag": "hr",
          "style": "dashed"
        },
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**🖥️ 资源信息**\\n\\n- 实例名称：{{.Labels.instance_name | default \"未知\"}}\\n- 地域：{{.Labels.region_name | default \"未知\"}}\\n- 实例ID：{{.Labels.instance_id | default \"未知\"}}\\n- 用户ID：{{.Labels.user_id | default \"未知\"}}",
                "type": "markdown"
              }
            }
          ]
        },
        {
          "tag": "hr",
          "style": "dashed"
        },
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**⏰ 告警时间**\\n\\n{{.Labels.alert_time | default .Time}}",
                "type": "markdown"
              }
            }
          ]
        }
      ]
    }
  }
}`
}

// GetGenericTemplate 获取通用告警模板
func GetGenericTemplate() string {
	return `{
  "app_key": "{{.AppID}}",
  "msg_type": 23,
  "content": {
    "type": 23,
    "content": {
      "header": {
        "template": "{{.Color}}",
        "title": {
          "tag": "text",
          "content": {
            "text": "{{.Icon}} {{.Title}}",
            "type": "plainText"
          }
        }
      },
      "elements": [
        {
          "tag": "hr",
          "style": "solid"
        },
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**📊 告警概览**\\n\\n- 严重程度：{{.Severity | upper}}\\n- 告警ID：{{.Alert.id | default \"未知\"}}\\n- 故障ID：{{.Alert.incident_id | default \"未知\"}}",
                "type": "markdown"
              }
            }
          ]
        },
        {{if .Content}}
        {
          "tag": "hr",
          "style": "dashed"
        },
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**📝 告警详情**\\n\\n{{.Content}}",
                "type": "markdown"
              }
            }
          ]
        },
        {{end}}
        {{if .Labels}}
        {
          "tag": "hr",
          "style": "dashed"
        },
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**🏷️ 标签信息**\\n\\n{{range $key, $value := .Labels}}- {{$key}}: {{$value}}\\n{{end}}",
                "type": "markdown"
              }
            }
          ]
        },
        {{end}}
        {
          "tag": "hr",
          "style": "dashed"
        },
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**⏰ 通知时间**\\n\\n{{.Time}}",
                "type": "markdown"
              }
            }
          ]
        }
      ]
    }
  }
}`
}
