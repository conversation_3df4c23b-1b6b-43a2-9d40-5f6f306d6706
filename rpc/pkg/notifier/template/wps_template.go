package template

import (
	"fmt"
	"time"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
)

// WPSTemplateBuilder WPS协作通知模板构建器
type WPSTemplateBuilder struct {
	appID string
}

// NewWPSTemplateBuilder 创建WPS模板构建器
func NewWPSTemplateBuilder(appID string) *WPSTemplateBuilder {
	return &WPSTemplateBuilder{
		appID: appID,
	}
}

// BuildAliyunEventTemplate 构建阿里云事件告警模板
func (b *WPSTemplateBuilder) BuildAliyunEventTemplate(data *notifier.NotificationData) map[string]interface{} {
	// 根据严重程度确定颜色和图标
	color, icon := b.getSeverityStyle(data.Severity)

	// 构建标题
	title := b.buildEventTitle(data, icon)

	// 构建消息体
	messageBody := map[string]interface{}{
		"app_key":  b.appID,
		"msg_type": 23,
		"content": map[string]interface{}{
			"type": 23,
			"content": map[string]interface{}{
				"header": map[string]interface{}{
					"template": color,
					"title": map[string]interface{}{
						"tag": "text",
						"content": map[string]interface{}{
							"text": title,
							"type": "plainText",
						},
					},
				},
				"elements": b.buildEventElements(data),
			},
		},
	}

	return messageBody
}

// BuildAliyunMetricsTemplate 构建阿里云指标告警模板
func (b *WPSTemplateBuilder) BuildAliyunMetricsTemplate(data *notifier.NotificationData) map[string]interface{} {
	// 根据严重程度确定颜色和图标
	color, icon := b.getSeverityStyle(data.Severity)

	// 构建标题
	title := b.buildMetricsTitle(data, icon)

	// 构建消息体
	messageBody := map[string]interface{}{
		"app_key":  b.appID,
		"msg_type": 23,
		"content": map[string]interface{}{
			"type": 23,
			"content": map[string]interface{}{
				"header": map[string]interface{}{
					"template": color,
					"title": map[string]interface{}{
						"tag": "text",
						"content": map[string]interface{}{
							"text": title,
							"type": "plainText",
						},
					},
				},
				"elements": b.buildMetricsElements(data),
			},
		},
	}

	return messageBody
}

// BuildGenericTemplate 构建通用告警模板
func (b *WPSTemplateBuilder) BuildGenericTemplate(data *notifier.NotificationData) map[string]interface{} {
	// 根据严重程度确定颜色和图标
	color, icon := b.getSeverityStyle(data.Severity)

	// 使用渲染后的标题和内容，如果没有则使用原始内容
	title := data.Title
	if data.RenderedTitle != "" {
		title = data.RenderedTitle
	}

	// 构建消息体
	messageBody := map[string]interface{}{
		"app_key":  b.appID,
		"msg_type": 23,
		"content": map[string]interface{}{
			"type": 23,
			"content": map[string]interface{}{
				"header": map[string]interface{}{
					"template": color,
					"title": map[string]interface{}{
						"tag": "text",
						"content": map[string]interface{}{
							"text": fmt.Sprintf("%s %s", icon, title),
							"type": "plainText",
						},
					},
				},
				"elements": b.buildGenericElements(data),
			},
		},
	}

	return messageBody
}

// getSeverityStyle 根据严重程度获取样式
func (b *WPSTemplateBuilder) getSeverityStyle(severity string) (string, string) {
	switch severity {
	case "critical":
		return "red", "🚨"
	case "warning":
		return "yellow", "⚠️"
	case "info":
		return "green", "ℹ️"
	default:
		return "grey", "❓"
	}
}

// buildEventTitle 构建事件告警标题
func (b *WPSTemplateBuilder) buildEventTitle(data *notifier.NotificationData, icon string) string {
	// 从标签中提取产品和事件信息
	product := data.Labels["product"]
	if product == "" {
		product = data.Labels["service_type_zh"]
	}
	if product == "" {
		product = "阿里云"
	}

	eventName := data.Labels["event_name_zh"]
	if eventName == "" {
		eventName = data.Labels["strategy_name"]
	}
	if eventName == "" {
		eventName = data.Title
	}

	return fmt.Sprintf("%s [%s] %s", icon, product, eventName)
}

// buildMetricsTitle 构建指标告警标题
func (b *WPSTemplateBuilder) buildMetricsTitle(data *notifier.NotificationData, icon string) string {
	// 从标签中提取告警名称
	alertName := data.Labels["alert_name"]
	if alertName == "" {
		alertName = data.Title
	}

	return fmt.Sprintf("%s [阿里云] %s", icon, alertName)
}

// buildEventElements 构建事件告警元素
func (b *WPSTemplateBuilder) buildEventElements(data *notifier.NotificationData) []map[string]interface{} {
	elements := []map[string]interface{}{
		{"tag": "hr", "style": "solid"},
	}

	// 添加事件概览
	elements = append(elements, b.buildEventOverview(data))

	// 添加资源信息
	elements = append(elements, b.buildResourceInfo(data)...)

	// 添加事件详情
	elements = append(elements, b.buildEventDetails(data)...)

	// 添加时间信息
	elements = append(elements, b.buildTimeInfo(data)...)

	return elements
}

// buildMetricsElements 构建指标告警元素
func (b *WPSTemplateBuilder) buildMetricsElements(data *notifier.NotificationData) []map[string]interface{} {
	elements := []map[string]interface{}{
		{"tag": "hr", "style": "solid"},
	}

	// 添加告警概览
	elements = append(elements, b.buildMetricsOverview(data))

	// 添加资源信息
	elements = append(elements, b.buildResourceInfo(data)...)

	// 添加时间信息
	elements = append(elements, b.buildTimeInfo(data)...)

	return elements
}

// buildGenericElements 构建通用告警元素
func (b *WPSTemplateBuilder) buildGenericElements(data *notifier.NotificationData) []map[string]interface{} {
	elements := []map[string]interface{}{
		{"tag": "hr", "style": "solid"},
	}

	// 添加基本信息
	elements = append(elements, b.buildBasicInfo(data))

	// 添加告警内容
	if data.Content != "" || data.RenderedContent != "" {
		elements = append(elements, b.buildContentInfo(data)...)
	}

	// 添加标签信息
	if len(data.Labels) > 0 {
		elements = append(elements, b.buildLabelsInfo(data)...)
	}

	// 添加时间信息
	elements = append(elements, b.buildTimeInfo(data)...)

	return elements
}

// buildEventOverview 构建事件概览
func (b *WPSTemplateBuilder) buildEventOverview(data *notifier.NotificationData) map[string]interface{} {
	overview := "**📊 事件概览**\n\n"

	if severity := data.Labels["severity"]; severity != "" {
		overview += fmt.Sprintf("- 告警等级：%s\n", severity)
	}

	if eventStatus := data.Labels["event_status"]; eventStatus != "" {
		overview += fmt.Sprintf("- 事件状态：%s\n", eventStatus)
	}

	if eventType := data.Labels["event_type"]; eventType != "" {
		overview += fmt.Sprintf("- 事件类型：%s\n", eventType)
	}

	if regionName := data.Labels["region_name_zh"]; regionName != "" {
		overview += fmt.Sprintf("- 所属地域：%s\n", regionName)
	}

	if groupName := data.Labels["group_name"]; groupName != "" {
		overview += fmt.Sprintf("- 业务分组：%s\n", groupName)
	}

	return map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": overview,
					"type": "markdown",
				},
			},
		},
	}
}

// buildMetricsOverview 构建指标概览
func (b *WPSTemplateBuilder) buildMetricsOverview(data *notifier.NotificationData) map[string]interface{} {
	overview := "**📊 告警概览**\n\n"

	if metricName := data.Labels["metric_name"]; metricName != "" {
		overview += fmt.Sprintf("- 指标名称：%s\n", metricName)
	}

	if currentValue := data.Labels["current_value"]; currentValue != "" {
		unit := data.Labels["unit"]
		overview += fmt.Sprintf("- 当前值：%s%s\n", currentValue, unit)
	}

	if expression := data.Labels["expression"]; expression != "" {
		overview += fmt.Sprintf("- 触发条件：%s\n", expression)
	}

	if triggerLevel := data.Labels["trigger_level"]; triggerLevel != "" {
		overview += fmt.Sprintf("- 告警等级：%s\n", triggerLevel)
	}

	if alertState := data.Labels["alert_state"]; alertState != "" {
		overview += fmt.Sprintf("- 告警状态：%s\n", alertState)
	}

	return map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": overview,
					"type": "markdown",
				},
			},
		},
	}
}

// buildResourceInfo 构建资源信息
func (b *WPSTemplateBuilder) buildResourceInfo(data *notifier.NotificationData) []map[string]interface{} {
	resourceInfo := "**🖥️ 资源信息**\n\n"

	if instanceName := data.Labels["instance_name"]; instanceName != "" {
		resourceInfo += fmt.Sprintf("- 实例名称：%s\n", instanceName)
	}

	if instanceID := data.Labels["instance_id"]; instanceID != "" {
		resourceInfo += fmt.Sprintf("- 实例ID：%s\n", instanceID)
	}

	if privateIP := data.Labels["private_ip_address"]; privateIP != "" {
		resourceInfo += fmt.Sprintf("- 私有IP：%s\n", privateIP)
	}

	if publicIP := data.Labels["public_ip_address"]; publicIP != "" {
		resourceInfo += fmt.Sprintf("- 公网IP：%s\n", publicIP)
	}

	if eipAddress := data.Labels["eip_address"]; eipAddress != "" {
		resourceInfo += fmt.Sprintf("- 弹性IP：%s\n", eipAddress)
	}

	if regionName := data.Labels["region_name"]; regionName != "" {
		resourceInfo += fmt.Sprintf("- 地域：%s\n", regionName)
	}

	return []map[string]interface{}{
		{"tag": "hr", "style": "dashed"},
		{
			"tag": "div",
			"fields": []map[string]interface{}{
				{
					"tag": "text",
					"content": map[string]interface{}{
						"text": resourceInfo,
						"type": "markdown",
					},
				},
			},
		},
	}
}

// buildEventDetails 构建事件详情
func (b *WPSTemplateBuilder) buildEventDetails(data *notifier.NotificationData) []map[string]interface{} {
	details := "**🔍 事件详情**\n\n"

	if eventID := data.Labels["event_id"]; eventID != "" {
		details += fmt.Sprintf("- 事件ID：%s\n", eventID)
	}

	if reason := data.Labels["reason"]; reason != "" {
		details += fmt.Sprintf("- 原因：%s\n", reason)
	}

	if reasonCode := data.Labels["reason_code"]; reasonCode != "" {
		details += fmt.Sprintf("- 事件代码：%s\n", reasonCode)
	}

	return []map[string]interface{}{
		{"tag": "hr", "style": "dashed"},
		{
			"tag": "div",
			"fields": []map[string]interface{}{
				{
					"tag": "text",
					"content": map[string]interface{}{
						"text": details,
						"type": "markdown",
					},
				},
			},
		},
	}
}

// buildTimeInfo 构建时间信息
func (b *WPSTemplateBuilder) buildTimeInfo(data *notifier.NotificationData) []map[string]interface{} {
	timeInfo := "**⏰ 时间信息**\n\n"

	// 对于事件告警
	if publishTime := data.Labels["publish_time"]; publishTime != "" {
		timeInfo += fmt.Sprintf("- 发布时间：%s\n", b.parseTime(publishTime))
	}

	if executeStartTime := data.Labels["execute_start_time"]; executeStartTime != "" {
		timeInfo += fmt.Sprintf("- 开始时间：%s\n", b.parseTime(executeStartTime))
	}

	if executeFinishTime := data.Labels["execute_finish_time"]; executeFinishTime != "" {
		timeInfo += fmt.Sprintf("- 结束时间：%s\n", b.parseTime(executeFinishTime))
	}

	// 对于指标告警
	if alertTime := data.Labels["alert_time"]; alertTime != "" {
		timeInfo += fmt.Sprintf("- 告警时间：%s\n", alertTime)
	}

	// 通知时间
	timeInfo += fmt.Sprintf("- 通知时间：%s\n", time.Now().Format("2006-01-02 15:04:05"))

	return []map[string]interface{}{
		{"tag": "hr", "style": "dashed"},
		{
			"tag": "div",
			"fields": []map[string]interface{}{
				{
					"tag": "text",
					"content": map[string]interface{}{
						"text": timeInfo,
						"type": "markdown",
					},
				},
			},
		},
	}
}

// buildBasicInfo 构建基本信息
func (b *WPSTemplateBuilder) buildBasicInfo(data *notifier.NotificationData) map[string]interface{} {
	basicInfo := "**📊 告警概览**\n\n"

	basicInfo += fmt.Sprintf("- 严重程度：%s\n", data.Severity)

	if data.AlertID != "" {
		basicInfo += fmt.Sprintf("- 告警ID：%s\n", data.AlertID)
	}

	if data.IncidentID != "" {
		basicInfo += fmt.Sprintf("- 故障ID：%s\n", data.IncidentID)
	}

	return map[string]interface{}{
		"tag": "div",
		"fields": []map[string]interface{}{
			{
				"tag": "text",
				"content": map[string]interface{}{
					"text": basicInfo,
					"type": "markdown",
				},
			},
		},
	}
}

// buildContentInfo 构建内容信息
func (b *WPSTemplateBuilder) buildContentInfo(data *notifier.NotificationData) []map[string]interface{} {
	content := data.Content
	if data.RenderedContent != "" {
		content = data.RenderedContent
	}

	return []map[string]interface{}{
		{"tag": "hr", "style": "dashed"},
		{
			"tag": "div",
			"fields": []map[string]interface{}{
				{
					"tag": "text",
					"content": map[string]interface{}{
						"text": fmt.Sprintf("**📝 告警详情**\n\n%s", content),
						"type": "markdown",
					},
				},
			},
		},
	}
}

// buildLabelsInfo 构建标签信息
func (b *WPSTemplateBuilder) buildLabelsInfo(data *notifier.NotificationData) []map[string]interface{} {
	labelText := "**🏷️ 标签信息**\n\n"

	for k, v := range data.Labels {
		labelText += fmt.Sprintf("- %s: %s\n", k, v)
	}

	return []map[string]interface{}{
		{"tag": "hr", "style": "dashed"},
		{
			"tag": "div",
			"fields": []map[string]interface{}{
				{
					"tag": "text",
					"content": map[string]interface{}{
						"text": labelText,
						"type": "markdown",
					},
				},
			},
		},
	}
}

// parseTime 解析时间字符串
func (b *WPSTemplateBuilder) parseTime(timeStr string) string {
	// 尝试解析ISO时间格式
	if t, err := time.Parse(time.RFC3339, timeStr); err == nil {
		// 转换为东八区时间
		cst := time.FixedZone("CST", 8*3600)
		return t.In(cst).Format("2006-01-02 15:04:05")
	}

	// 如果解析失败，返回原始字符串
	return timeStr
}
