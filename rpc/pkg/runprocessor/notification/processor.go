package notification

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier/channel"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier/template"
)

// Processor 通知处理器
type Processor struct {
	svcCtx *svc.ServiceContext
	logger logx.Logger
}

// NewProcessor 创建通知处理器
func NewProcessor(svcCtx *svc.ServiceContext) *Processor {
	return &Processor{
		svcCtx: svcCtx,
		logger: logx.WithContext(context.Background()),
	}
}

// ProcessNotifications 处理通知
func (p *Processor) ProcessNotifications(ctx context.Context, processedAlert map[string]interface{}, routeSpace map[string]interface{}) error {
	// 检查是否被抑制
	if suppressed, ok := processedAlert["suppressed"].(bool); ok && suppressed {
		p.logger.Infof("告警被抑制，不发送通知")
		return nil
	}

	// 提取故障ID用于构建故障数据
	incidentID, _ := processedAlert["incident_id"].(string)

	// 获取通知模板ID
	var templateID string
	// if notifyConfig, ok := routeSpace["notify_config"].(map[string]interface{}); ok && notifyConfig != nil {
	// 	if tmplID, ok := notifyConfig["template_id"].(string); ok {
	// 		templateID = tmplID
	// 	}
	// }
	templateID = "1"

	// 获取通知模板内容
	templateData := make(map[string]interface{})
	if templateID != "" {
		// 调用RPC获取通知模板
		template, err := p.getNotificationTemplate(ctx, templateID)
		if err != nil {
			p.logger.Errorf("获取通知模板失败: %v", err)
			// 继续处理，使用默认模板
		} else {
			templateData = template
		}
	}

	// 构建故障数据
	incidentData := map[string]interface{}{
		"id": incidentID,
		// 可以添加更多故障相关信息
	}

	// 创建通知管理器
	notificationManager := notifier.NewNotificationManager()

	// 注册通知渠道
	// emailNotifier := channel.NewEmailNotifier(
	// 	p.svcCtx.Config.EmailConfig.SMTPServer,
	// 	p.svcCtx.Config.EmailConfig.SMTPPort,
	// 	p.svcCtx.Config.EmailConfig.Username,
	// 	p.svcCtx.Config.EmailConfig.Password,
	// 	p.svcCtx.Config.EmailConfig.FromEmail,
	// )
	// notificationManager.RegisterNotifier(emailNotifier)

	// dingTalkNotifier := channel.NewDingTalkNotifier(
	// 	p.svcCtx.Config.DingTalkConfig.WebhookURL,
	// 	p.svcCtx.Config.DingTalkConfig.Secret,
	// )
	// notificationManager.RegisterNotifier(dingTalkNotifier)

	// webhookNotifier := channel.NewWebhookNotifier(
	// 	p.svcCtx.Config.WebhookConfig.DefaultURL,
	// )
	// notificationManager.RegisterNotifier(webhookNotifier)

	// 注册WPS协作通知器（使用Mock配置进行测试）
	wpsNotifier := channel.NewWPSNotifier(
		"AK20231216PDPGPI",                 // Mock AppID
		"deff6800d41d3b296eb41ce4a01094ef", // Mock AppKey
		"https://openapi.wps.cn",           // WPS OpenAPI地址
	)
	notificationManager.RegisterNotifier(wpsNotifier)

	// 获取通知接收人
	var recipients []string
	if notifyConfig, ok := routeSpace["notify_config"].(map[string]interface{}); ok && notifyConfig != nil {
		if users, ok := notifyConfig["users"].([]interface{}); ok {
			for _, user := range users {
				if userStr, ok := user.(string); ok {
					recipients = append(recipients, userStr)
				}
			}
		}
	}

	// 准备通知数据
	notificationData := notifier.PrepareNotificationData(
		processedAlert,
		incidentData,
		templateData,
		routeSpace,
		recipients,
	)

	// 添加WPS协作通知的渠道数据
	p.addWPSChannelData(notificationData, routeSpace)

	// 渲染模板
	if err := notifier.RenderNotificationTemplate(ctx, notificationData); err != nil {
		p.logger.Errorf("渲染通知模板失败: %v", err)
		// 继续处理，使用原始内容
	}

	// 为WPS协作通知渲染专用模板
	if err := p.renderWPSTemplate(ctx, notificationData, processedAlert); err != nil {
		p.logger.Errorf("渲染WPS模板失败: %v", err)
		// 继续处理，使用默认模板
	}

	// 获取通知渠道
	var channels []notifier.ChannelType
	if notifyConfig, ok := routeSpace["notify_config"].(map[string]interface{}); ok && notifyConfig != nil {
		if channelsRaw, ok := notifyConfig["channels"].([]interface{}); ok {
			for _, ch := range channelsRaw {
				if chStr, ok := ch.(string); ok {
					switch chStr {
					case "email":
						channels = append(channels, notifier.EmailChannel)
					case "dingtalk":
						channels = append(channels, notifier.DingTalkChannel)
					case "webhook":
						channels = append(channels, notifier.WebhookChannel)
					case "wps":
						channels = append(channels, notifier.WPSChannel)
					}
				}
			}
		}
	}

	// 如果没有配置渠道，默认使用WPS协作通知（用于测试）
	if len(channels) == 0 {
		channels = append(channels, notifier.WPSChannel)
	}

	// 发送通知
	results := notificationManager.SendToMultipleChannels(ctx, channels, notificationData)

	// 记录通知结果
	for channel, result := range results {
		if result.Success {
			p.logger.Infof("通过 %s 发送通知成功: %s", channel, result.Message)
		} else {
			p.logger.Errorf("通过 %s 发送通知失败: %s, 错误: %v", channel, result.Message, result.Error)
		}
	}

	return nil
}

// getNotificationTemplate 获取通知模板
func (p *Processor) getNotificationTemplate(ctx context.Context, templateID string) (map[string]interface{}, error) {
	// TODO: 调用oncall_rpc获取通知模板
	// 这里先返回模拟数据
	p.logger.Infof("获取通知模板: %s", templateID)

	// 模拟模板数据
	template := map[string]interface{}{
		"id":             templateID,
		"name":           "默认通知模板",
		"title_template": "【{{.Alert.alert_severity}}】{{.Alert.title}}",
		"content_template": `告警详情：
标题：{{.Alert.title}}
描述：{{.Alert.description}}
严重程度：{{.Alert.alert_severity}}
时间：{{.Alert.created_at | date "2006-01-02 15:04:05"}}
{{if .Alert.labels}}
标签：
{{range $key, $value := .Alert.labels}}
- {{$key}}: {{$value}}
{{end}}
{{end}}`,
	}

	return template, nil
}

// addWPSChannelData 添加WPS协作通知的渠道数据
func (p *Processor) addWPSChannelData(notificationData *notifier.NotificationData, routeSpace map[string]interface{}) {
	// 初始化渠道数据
	if notificationData.ChannelData == nil {
		notificationData.ChannelData = make(map[string]interface{})
	}

	// 从路由空间配置中获取WPS聊天组ID
	var chatIDs []int
	if notifyConfig, ok := routeSpace["notify_config"].(map[string]interface{}); ok && notifyConfig != nil {
		if wpsConfig, ok := notifyConfig["wps_config"].(map[string]interface{}); ok {
			if chatIDsRaw, ok := wpsConfig["chat_ids"].([]interface{}); ok {
				for _, chatID := range chatIDsRaw {
					if id, ok := chatID.(int); ok {
						chatIDs = append(chatIDs, id)
					} else if idFloat, ok := chatID.(float64); ok {
						chatIDs = append(chatIDs, int(idFloat))
					}
				}
			}
		}
	}

	// 如果没有配置聊天组ID，使用默认测试聊天组
	if len(chatIDs) == 0 {
		chatIDs = append(chatIDs, 35589691) // 默认测试聊天组ID
	}

	// 设置WPS渠道数据
	notificationData.ChannelData["wps"] = map[string]interface{}{
		"chat_ids": chatIDs,
	}

	p.logger.Infof("添加WPS渠道数据，聊天组ID: %v", chatIDs)
}

// renderWPSTemplate 渲染WPS协作通知模板
func (p *Processor) renderWPSTemplate(ctx context.Context, notificationData *notifier.NotificationData, processedAlert map[string]interface{}) error {
	// 创建WPS模板渲染器
	wpsRenderer := template.NewWPSTemplateRenderer("AK20231216PDPGPI")

	// 根据告警来源选择合适的模板
	var templateStr string
	alertSource, _ := processedAlert["source"].(string)

	switch alertSource {
	case "aliyun_event":
		templateStr = template.GetAliyunEventTemplate()
		p.logger.Infof("使用阿里云事件告警模板")
	case "aliyun_metrics":
		templateStr = template.GetAliyunMetricsTemplate()
		p.logger.Infof("使用阿里云指标告警模板")
	default:
		templateStr = template.GetGenericTemplate()
		p.logger.Infof("使用通用告警模板")
	}

	// 渲染WPS模板
	wpsMessageBody, err := wpsRenderer.RenderTemplate(templateStr, notificationData)
	if err != nil {
		return fmt.Errorf("渲染WPS模板失败: %w", err)
	}

	// 将渲染后的消息体存储到渠道数据中
	if notificationData.ChannelData == nil {
		notificationData.ChannelData = make(map[string]interface{})
	}

	if wpsData, ok := notificationData.ChannelData["wps"].(map[string]interface{}); ok {
		wpsData["message_body"] = wpsMessageBody
	} else {
		notificationData.ChannelData["wps"] = map[string]interface{}{
			"message_body": wpsMessageBody,
		}
	}

	p.logger.Infof("WPS模板渲染完成")
	return nil
}
