package notification

import (
	"context"
	"fmt"
	"testing"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
)

// MockServiceContext 模拟服务上下文
type MockServiceContext struct{}

// TestProcessor_ProcessNotifications_AliyunEvent 测试阿里云事件告警通知处理
func TestProcessor_ProcessNotifications_AliyunEvent(t *testing.T) {
	// 创建模拟的服务上下文
	// svcCtx := &MockServiceContext{}

	// 创建通知处理器
	processor := NewProcessor(&svc.ServiceContext{})

	// 模拟阿里云事件告警数据
	processedAlert := map[string]interface{}{
		"id":             "alert-aliyun-event-001",
		"incident_id":    "incident-aliyun-event-001",
		"title":          "实例遭受DDoS攻击中",
		"description":    "阿里云ECS实例正在遭受DDoS攻击",
		"alert_severity": "critical",
		"source":         "aliyun_event",
		"suppressed":     false,
		"labels": map[string]string{
			"severity":           "CRITICAL",
			"service_type_zh":    "云服务器ECS",
			"event_status":       "Normal",
			"event_type":         "Exception",
			"region_name_zh":     "华北2（北京）",
			"group_name":         "默认应用分组",
			"instance_name":      "test-server-001",
			"instance_id":        "i-bp1234567890abcdef",
			"private_ip_address": "************",
			"eip_address":        "47.93.123.456",
			"event_id":           "evt-bp1234567890abcdef",
			"reason":             "实例正在遭受DDoS攻击",
			"reason_code":        "DDoS.Attack.Detected",
			"publish_time":       "2024-01-15T10:30:00Z",
			"execute_start_time": "2024-01-15T10:30:00Z",
		},
	}

	// 模拟路由空间配置
	routeSpace := map[string]interface{}{
		"notify_config": map[string]interface{}{
			"template_id": "template-aliyun-event",
			"channels":    []interface{}{"wps"},
			"users":       []interface{}{"<EMAIL>", "<EMAIL>"},
			"wps_config": map[string]interface{}{
				"chat_ids": []interface{}{35589691},
			},
		},
	}

	// 执行通知处理
	err := processor.ProcessNotifications(context.Background(), processedAlert, routeSpace)
	if err != nil {
		t.Fatalf("处理阿里云事件告警通知失败: %v", err)
	}

	fmt.Println("阿里云事件告警通知处理完成")
}

// TestProcessor_ProcessNotifications_AliyunMetrics 测试阿里云指标告警通知处理
func TestProcessor_ProcessNotifications_AliyunMetrics(t *testing.T) {
	// 创建通知处理器
	processor := NewProcessor(&svc.ServiceContext{})

	// 模拟阿里云指标告警数据
	processedAlert := map[string]interface{}{
		"id":             "alert-aliyun-metrics-001",
		"incident_id":    "incident-aliyun-metrics-001",
		"title":          "CPU使用率过高",
		"description":    "ECS实例CPU使用率超过90%",
		"alert_severity": "warning",
		"source":         "aliyun_metrics",
		"suppressed":     false,
		"labels": map[string]string{
			"metric_name":   "CPUUtilization",
			"current_value": "95.6",
			"unit":          "%",
			"trigger_level": "WARN",
			"alert_state":   "ALERT",
			"expression":    "CPUUtilization >= 90",
			"instance_name": "web-server-prod-001",
			"region_name":   "cn-beijing",
			"instance_id":   "i-bp1234567890abcdef",
			"user_id":       "123456789",
			"alert_time":    "2024-01-15 18:30:00",
		},
	}

	// 模拟路由空间配置
	routeSpace := map[string]interface{}{
		"notify_config": map[string]interface{}{
			"template_id": "template-aliyun-metrics",
			"channels":    []interface{}{"wps"},
			"users":       []interface{}{"<EMAIL>"},
			"wps_config": map[string]interface{}{
				"chat_ids": []interface{}{35589691},
			},
		},
	}

	// 执行通知处理
	err := processor.ProcessNotifications(context.Background(), processedAlert, routeSpace)
	if err != nil {
		t.Fatalf("处理阿里云指标告警通知失败: %v", err)
	}

	fmt.Println("阿里云指标告警通知处理完成")
}

// TestProcessor_ProcessNotifications_Generic 测试通用告警通知处理
func TestProcessor_ProcessNotifications_Generic(t *testing.T) {
	// 创建通知处理器
	processor := NewProcessor(&svc.ServiceContext{})

	// 模拟通用告警数据
	processedAlert := map[string]interface{}{
		"id":             "alert-generic-001",
		"incident_id":    "incident-generic-001",
		"title":          "系统告警",
		"description":    "系统出现异常，请及时处理",
		"alert_severity": "critical",
		"source":         "monitoring-system",
		"suppressed":     false,
		"labels": map[string]string{
			"service":      "web-server",
			"environment":  "production",
			"region":       "beijing",
			"team":         "ops",
			"cpu_usage":    "95%",
			"memory_usage": "88%",
		},
	}

	// 模拟路由空间配置（没有指定渠道，使用默认WPS）
	routeSpace := map[string]interface{}{
		"notify_config": map[string]interface{}{
			"template_id": "template-generic",
			"users":       []interface{}{"<EMAIL>"},
		},
	}

	// 执行通知处理
	err := processor.ProcessNotifications(context.Background(), processedAlert, routeSpace)
	if err != nil {
		t.Fatalf("处理通用告警通知失败: %v", err)
	}

	fmt.Println("通用告警通知处理完成")
}

// TestProcessor_ProcessNotifications_Suppressed 测试被抑制的告警
func TestProcessor_ProcessNotifications_Suppressed(t *testing.T) {
	// 创建通知处理器
	processor := NewProcessor(&svc.ServiceContext{})

	// 模拟被抑制的告警数据
	processedAlert := map[string]interface{}{
		"id":             "alert-suppressed-001",
		"incident_id":    "incident-suppressed-001",
		"title":          "被抑制的告警",
		"description":    "这个告警被抑制了",
		"alert_severity": "warning",
		"source":         "test",
		"suppressed":     true, // 被抑制
	}

	// 模拟路由空间配置
	routeSpace := map[string]interface{}{
		"notify_config": map[string]interface{}{
			"channels": []interface{}{"wps"},
			"users":    []interface{}{"<EMAIL>"},
		},
	}

	// 执行通知处理
	err := processor.ProcessNotifications(context.Background(), processedAlert, routeSpace)
	if err != nil {
		t.Fatalf("处理被抑制告警失败: %v", err)
	}

	fmt.Println("被抑制告警处理完成（应该不发送通知）")
}

// TestProcessor_ProcessNotifications_MultipleChannels 测试多渠道通知
func TestProcessor_ProcessNotifications_MultipleChannels(t *testing.T) {
	// 创建通知处理器
	processor := NewProcessor(&svc.ServiceContext{})

	// 模拟告警数据
	processedAlert := map[string]interface{}{
		"id":             "alert-multi-channel-001",
		"incident_id":    "incident-multi-channel-001",
		"title":          "多渠道测试告警",
		"description":    "测试多个通知渠道",
		"alert_severity": "info",
		"source":         "test",
		"suppressed":     false,
		"labels": map[string]string{
			"test": "multi-channel",
		},
	}

	// 模拟路由空间配置（配置多个渠道）
	routeSpace := map[string]interface{}{
		"notify_config": map[string]interface{}{
			"channels": []interface{}{"wps", "email", "webhook"},
			"users":    []interface{}{"<EMAIL>"},
			"wps_config": map[string]interface{}{
				"chat_ids": []interface{}{35589691},
			},
		},
	}

	// 执行通知处理
	err := processor.ProcessNotifications(context.Background(), processedAlert, routeSpace)
	if err != nil {
		t.Fatalf("处理多渠道告警通知失败: %v", err)
	}

	fmt.Println("多渠道告警通知处理完成")
}

// TestProcessor_addWPSChannelData 测试WPS渠道数据添加
func TestProcessor_addWPSChannelData(t *testing.T) {
	processor := NewProcessor(&svc.ServiceContext{})

	// 创建通知数据
	notificationData := &notifier.NotificationData{
		Title:    "测试告警",
		Severity: "warning",
	}

	// 模拟路由空间配置
	routeSpace := map[string]interface{}{
		"notify_config": map[string]interface{}{
			"wps_config": map[string]interface{}{
				"chat_ids": []interface{}{12345, 67890},
			},
		},
	}

	// 添加WPS渠道数据
	processor.addWPSChannelData(notificationData, routeSpace)

	// 验证结果
	if notificationData.ChannelData == nil {
		t.Fatal("ChannelData为空")
	}

	wpsData, ok := notificationData.ChannelData["wps"].(map[string]interface{})
	if !ok {
		t.Fatal("WPS渠道数据不存在")
	}

	chatIDs, ok := wpsData["chat_ids"].([]int)
	if !ok {
		t.Fatal("聊天组ID不存在或类型错误")
	}

	if len(chatIDs) != 2 || chatIDs[0] != 12345 || chatIDs[1] != 67890 {
		t.Fatalf("聊天组ID不正确，期望: [12345, 67890], 实际: %v", chatIDs)
	}

	fmt.Printf("WPS渠道数据添加成功: %+v\n", wpsData)
}
