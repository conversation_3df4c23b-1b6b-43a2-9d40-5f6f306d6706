package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_common/utils/uuidx"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/runprocessor/alert"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/runprocessor/incident"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/runprocessor/notification"
)

const (
	// 队列名称
	AlertQueueKey = "alert:queue"
	// 处理中的告警集合
	AlertProcessingSetKey = "alert:processing"
	// 告警处理锁前缀
	AlertLockKeyPrefix = "alert:lock:"
	// 告警重试队列前缀
	AlertRetryKeyPrefix = "alert:retry:"
	// 告警处理状态前缀
	AlertStatusKeyPrefix = "alert:status:"
	// 锁过期时间
	LockExpiration = 5 * time.Minute
	// 状态过期时间
	StatusExpiration = 24 * time.Hour
	// 重试间隔
	RetryInterval = 1 * time.Minute
	// 最大重试次数
	MaxRetryCount = 3
)

// AlertMessage 告警消息
type AlertMessage struct {
	RawAlertID     string                 `json:"raw_alert_id"`
	IntegrationsID string                 `json:"integrations_id"`
	RawData        map[string]interface{} `json:"raw_data"`     // 兼容旧版本
	RawDataStr     string                 `json:"raw_data_str"` // 新增：原始字符串数据
	RetryCount     int                    `json:"retry_count"`
	CreatedAt      time.Time              `json:"created_at"`
}

// AlertStatus 告警处理状态
type AlertStatus struct {
	RawAlertID string    `json:"raw_alert_id"`
	Status     string    `json:"status"` // pending, processing, completed, failed
	Message    string    `json:"message"`
	UpdatedAt  time.Time `json:"updated_at"`
	RetryCount int       `json:"retry_count"`
}

// Processor 队列处理器
type Processor struct {
	svcCtx      *svc.ServiceContext
	logger      logx.Logger
	stopChan    chan struct{}
	wg          sync.WaitGroup
	isRunning   bool
	runningLock sync.Mutex

	// 子处理器
	alertProcessor        *alert.Processor
	incidentProcessor     *incident.Processor
	notificationProcessor *notification.Processor
}

// NewProcessor 创建队列处理器
func NewProcessor(svcCtx *svc.ServiceContext) *Processor {
	return &Processor{
		svcCtx:                svcCtx,
		logger:                logx.WithContext(context.Background()),
		stopChan:              make(chan struct{}),
		isRunning:             false,
		alertProcessor:        alert.NewProcessor(svcCtx),
		incidentProcessor:     incident.NewProcessor(svcCtx),
		notificationProcessor: notification.NewProcessor(svcCtx),
	}
}

// Start 启动队列处理器
func (p *Processor) Start() {
	p.runningLock.Lock()
	defer p.runningLock.Unlock()

	if p.isRunning {
		p.logger.Info("队列处理器已经在运行中")
		return
	}

	p.isRunning = true
	p.stopChan = make(chan struct{})

	// 启动消费者
	p.wg.Add(1)
	go p.consumeAlerts()

	// 启动重试处理器
	p.wg.Add(1)
	go p.processRetries()

	p.logger.Info("队列处理器已启动")
}

// Stop 停止队列处理器
func (p *Processor) Stop() {
	p.runningLock.Lock()
	defer p.runningLock.Unlock()

	if !p.isRunning {
		return
	}

	close(p.stopChan)
	p.wg.Wait()
	p.isRunning = false
	p.logger.Info("队列处理器已停止")
}

// EnqueueAlert 将告警加入队列（兼容旧版本）
func (p *Processor) EnqueueAlert(ctx context.Context, rawAlertID, integrationsID string, rawData map[string]interface{}) error {
	// 创建告警消息
	message := AlertMessage{
		RawAlertID:     rawAlertID,
		IntegrationsID: integrationsID,
		RawData:        rawData,
		RetryCount:     0,
		CreatedAt:      time.Now(),
	}

	// 序列化消息
	messageJSON, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化告警消息失败: %v", err)
	}

	// 更新告警状态为待处理
	p.updateAlertStatus(ctx, rawAlertID, "pending", "等待处理", 0)

	// 将消息加入Redis队列
	err = p.svcCtx.Redis.LPush(ctx, AlertQueueKey, string(messageJSON)).Err()
	if err != nil {
		return fmt.Errorf("将告警加入队列失败: %v", err)
	}

	p.logger.Infof("告警已加入队列: %s", rawAlertID)
	return nil
}

// EnqueueAlertWithRawString 将告警加入队列（使用原始字符串）
func (p *Processor) EnqueueAlertWithRawString(ctx context.Context, rawAlertID, integrationsID, rawDataStr string) error {
	// 创建告警消息
	message := AlertMessage{
		RawAlertID:     rawAlertID,
		IntegrationsID: integrationsID,
		RawDataStr:     rawDataStr,
		RetryCount:     0,
		CreatedAt:      time.Now(),
	}

	// 序列化消息
	messageJSON, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化告警消息失败: %v", err)
	}

	// 更新告警状态为待处理
	p.updateAlertStatus(ctx, rawAlertID, "pending", "等待处理", 0)

	// 将消息加入Redis队列
	err = p.svcCtx.Redis.LPush(ctx, AlertQueueKey, string(messageJSON)).Err()
	if err != nil {
		return fmt.Errorf("将告警加入队列失败: %v", err)
	}

	p.logger.Infof("告警已加入队列: %s", rawAlertID)
	return nil
}

// consumeAlerts 消费告警队列
func (p *Processor) consumeAlerts() {
	defer p.wg.Done()

	ctx := context.Background()
	p.logger.Info("开始消费告警队列")

	for {
		select {
		case <-p.stopChan:
			p.logger.Info("告警队列消费者已停止")
			return
		default:
			// 从队列中获取消息
			result, err := p.svcCtx.Redis.BRPop(ctx, 5*time.Second, AlertQueueKey).Result()
			if err != nil {
				if err != redis.Nil {
					p.logger.Errorf("从队列获取消息失败: %v", err)
				}
				continue
			}

			// 队列为空
			if len(result) < 2 {
				continue
			}

			// 解析消息
			var message AlertMessage
			if err := json.Unmarshal([]byte(result[1]), &message); err != nil {
				p.logger.Errorf("解析告警消息失败: %v", err)
				continue
			}

			// 处理告警
			p.processAlert(ctx, message)
		}
	}
}

// processAlert 处理单个告警，核心处理逻辑
func (p *Processor) processAlert(ctx context.Context, message AlertMessage) {
	rawAlertID := message.RawAlertID

	// 更新告警状态为处理中
	p.updateAlertStatus(ctx, rawAlertID, "processing", "正在处理", message.RetryCount)

	// 使用分布式锁确保只有一个实例处理
	lockKey := AlertLockKeyPrefix + rawAlertID
	lock := p.svcCtx.Redis.SetNX(ctx, lockKey, "1", LockExpiration)
	if !lock.Val() {
		p.logger.Infof("告警 %s 已被其他实例处理", rawAlertID)
		return
	}

	// 确保锁会被释放
	defer p.svcCtx.Redis.Del(ctx, lockKey)

	// 1. 处理告警
	var processedAlert map[string]interface{}
	var err error

	p.logger.Debug("开始处理告警: %s:%s", rawAlertID, message.RawDataStr)

	// 优先使用原始字符串数据，如果没有则使用解析后的数据
	if message.RawDataStr != "" {
		processedAlert, err = p.alertProcessor.ProcessAlertWithRawString(ctx, rawAlertID, message.IntegrationsID, message.RawDataStr)
	} else {
		processedAlert, err = p.alertProcessor.ProcessAlert(ctx, rawAlertID, message.IntegrationsID, message.RawData)
	}

	if err != nil {
		p.logger.Errorf("处理告警失败: %v", err)
		p.scheduleRetry(ctx, rawAlertID, fmt.Sprintf("处理告警失败: %v", err), message.RetryCount)
		return
	}

	p.logger.Debug("告警处理结果: %v", processedAlert)

	// 2. 获取路由空间信息
	// routeSpaceID, _ := processedAlert["space_id"].(string)
	routeSpace, err := p.alertProcessor.GetRouteSpaceInfo(ctx, 1) // 临时使用固定值
	if err != nil {
		p.logger.Errorf("获取路由空间信息失败: %v", err)
		p.scheduleRetry(ctx, rawAlertID, fmt.Sprintf("获取路由空间信息失败: %v", err), message.RetryCount)
		return
	}

	// 3. 创建标准告警
	alertID, err := p.alertProcessor.CreateStandardAlert(ctx, processedAlert)
	if err != nil {
		// 创建标准告警失败，记录错误并重试
		p.logger.Errorf("创建标准告警失败: %v", err)
		p.scheduleRetry(ctx, rawAlertID, fmt.Sprintf("创建标准告警失败: %v", err), message.RetryCount)
		return
	}

	// 添加告警ID到处理后的告警
	processedAlert["alert_id"] = alertID

	// 4. 检查是否需要创建故障
	if p.incidentProcessor.ShouldCreateIncident(processedAlert) {
		// 创建故障
		incidentID, err := p.incidentProcessor.CreateIncidentFromAlert(ctx, processedAlert, alertID, routeSpace)
		if err != nil {
			p.logger.Errorf("创建故障失败: %v", err)
			// 不重试，只记录错误
		} else {
			// 添加故障ID到处理后的告警
			processedAlert["incident_id"] = incidentID
		}
	}

	// 5. 处理通知
	err = p.notificationProcessor.ProcessNotifications(ctx, processedAlert, routeSpace)
	if err != nil {
		p.logger.Errorf("处理通知失败: %v", err)
		// 不重试，只记录错误
	}

	// 更新告警状态为完成
	p.updateAlertStatus(ctx, rawAlertID, "completed", "处理完成", message.RetryCount)
	p.logger.Infof("告警 %s 处理完成", rawAlertID)
}

// processRetries 处理重试队列
func (p *Processor) processRetries() {
	defer p.wg.Done()

	ctx := context.Background()
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	p.logger.Info("开始处理重试队列")

	for {
		select {
		case <-p.stopChan:
			p.logger.Info("重试队列处理器已停止")
			return
		case <-ticker.C:
			// 获取所有需要重试的告警
			pattern := AlertRetryKeyPrefix + "*"
			keys, err := p.svcCtx.Redis.Keys(ctx, pattern).Result()
			if err != nil {
				p.logger.Errorf("获取重试队列失败: %v", err)
				continue
			}

			now := time.Now().Unix()

			for _, key := range keys {
				// 获取重试数据
				retryDataStr, err := p.svcCtx.Redis.Get(ctx, key).Result()
				if err != nil {
					if err != redis.Nil {
						p.logger.Errorf("获取重试数据失败: %v", err)
					}
					continue
				}

				var retryData struct {
					RawAlertID string `json:"raw_alert_id"`
					Reason     string `json:"reason"`
					RetryTime  int64  `json:"retry_time"`
					RetryCount int    `json:"retry_count"`
				}

				if err := json.Unmarshal([]byte(retryDataStr), &retryData); err != nil {
					p.logger.Errorf("解析重试数据失败: %v", err)
					continue
				}

				// 检查是否到达重试时间
				if retryData.RetryTime > now {
					continue
				}

				// 检查重试次数
				if retryData.RetryCount >= MaxRetryCount {
					p.logger.Errorf("告警 %s 已达到最大重试次数，不再重试", retryData.RawAlertID)
					p.updateAlertStatus(ctx, retryData.RawAlertID, "failed", fmt.Sprintf("达到最大重试次数: %d", MaxRetryCount), retryData.RetryCount)
					p.svcCtx.Redis.Del(ctx, key)
					continue
				}

				// 获取原始告警数据
				rawAlertID := uuidx.ParseUUIDString(retryData.RawAlertID)
				// if err != nil {
				// 	p.logger.Errorf("解析原始告警ID失败: %v", err)
				// 	continue
				// }

				rawAlert, err := p.svcCtx.DB.RawAlert.Get(ctx, rawAlertID)
				if err != nil {
					p.logger.Errorf("获取原始告警数据失败: %v", err)
					continue
				}

				// 重新入队，使用原始字符串数据
				message := AlertMessage{
					RawAlertID:     retryData.RawAlertID,
					IntegrationsID: rawAlert.IntegrationsID,
					RawDataStr:     rawAlert.RawData, // 直接使用原始字符串
					RetryCount:     retryData.RetryCount + 1,
					CreatedAt:      time.Now(),
				}

				messageJSON, _ := json.Marshal(message)
				p.svcCtx.Redis.LPush(ctx, AlertQueueKey, string(messageJSON))
				p.logger.Infof("告警 %s 已重新入队，重试次数: %d", retryData.RawAlertID, retryData.RetryCount+1)

				// 删除重试记录
				p.svcCtx.Redis.Del(ctx, key)
			}
		}
	}
}

// scheduleRetry 安排重试
func (p *Processor) scheduleRetry(ctx context.Context, rawAlertID, reason string, currentRetryCount int) {
	// 记录失败原因
	p.logger.Errorf("处理告警失败，安排重试: %s, 原因: %s, 当前重试次数: %d", rawAlertID, reason, currentRetryCount)

	// 更新告警状态
	p.updateAlertStatus(ctx, rawAlertID, "failed", reason, currentRetryCount)

	// 检查重试次数
	if currentRetryCount >= MaxRetryCount {
		p.logger.Errorf("告警 %s 已达到最大重试次数，不再重试", rawAlertID)
		return
	}

	// 将重试任务添加到Redis队列
	retryKey := AlertRetryKeyPrefix + rawAlertID
	retryData, _ := json.Marshal(map[string]interface{}{
		"raw_alert_id": rawAlertID,
		"reason":       reason,
		"retry_time":   time.Now().Add(RetryInterval).Unix(),
		"retry_count":  currentRetryCount + 1,
	})

	redisErr := p.svcCtx.Redis.Set(ctx, retryKey, string(retryData), 24*time.Hour).Err()
	if redisErr != nil {
		p.logger.Errorf("添加重试任务失败: %v", redisErr)
	}
}

// updateAlertStatus 更新告警状态
func (p *Processor) updateAlertStatus(ctx context.Context, rawAlertID, status, message string, retryCount int) {
	// 创建状态对象
	alertStatus := AlertStatus{
		RawAlertID: rawAlertID,
		Status:     status,
		Message:    message,
		UpdatedAt:  time.Now(),
		RetryCount: retryCount,
	}

	// 序列化状态
	statusJSON, err := json.Marshal(alertStatus)
	if err != nil {
		p.logger.Errorf("序列化告警状态失败: %v", err)
		return
	}

	// 保存状态到Redis
	statusKey := AlertStatusKeyPrefix + rawAlertID
	err = p.svcCtx.Redis.Set(ctx, statusKey, string(statusJSON), StatusExpiration).Err()
	if err != nil {
		p.logger.Errorf("保存告警状态失败: %v", err)
	}

	// 更新数据库状态
	// 注意：需要先生成ent代码才能使用这些方法
	rawAlertUUID := uuidx.ParseUUIDString(rawAlertID)
	if err != nil {
		p.logger.Errorf("解析原始告警ID失败: %v", err)
		return
	}

	_, err = p.svcCtx.DB.RawAlert.UpdateOneID(rawAlertUUID).
		SetStatus(status).
		SetMessage(message).
		Save(ctx)
	if err != nil {
		p.logger.Errorf("更新数据库告警状态失败: %v", err)
	}
}
