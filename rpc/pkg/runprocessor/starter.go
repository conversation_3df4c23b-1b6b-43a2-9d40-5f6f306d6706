package runprocessor

import (
	"context"
	"sync"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/runprocessor/queue"
)

var (
	// 单例处理器
	processor     *queue.Processor
	processorOnce sync.Once
)

// StartAlertProcessor 启动告警处理器
func StartAlertProcessor(svcCtx *svc.ServiceContext) {
	processorOnce.Do(func() {
		processor = queue.NewProcessor(svcCtx)
		processor.Start()
		logx.Info("告警处理器已启动")
	})
}

// StopAlertProcessor 停止告警处理器
func StopAlertProcessor() {
	if processor != nil {
		processor.Stop()
		logx.Info("告警处理器已停止")
	}
}

// GetAlertProcessor 获取告警处理器实例
func GetAlertProcessor() *queue.Processor {
	return processor
}

// EnqueueRawAlert 将原始告警加入处理队列
func EnqueueRawAlert(ctx context.Context, rawAlertID, integrationsID, rawDataStr string) error {
	if processor == nil {
		return nil
	}

	// 直接使用原始字符串，不进行解析
	return processor.EnqueueAlertWithRawString(ctx, rawAlertID, integrationsID, rawDataStr)
}
