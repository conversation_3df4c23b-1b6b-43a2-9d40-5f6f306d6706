package alert

import (
	"context"
	"fmt"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"
	alertservicelogic "gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/logic/alertservice"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/ruleprocessor"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/ruleprocessor/convergence"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/ruleprocessor/deduplication"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/ruleprocessor/grouping"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/ruleprocessor/severity"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/ruleprocessor/suppression"
)

// Processor 告警处理器
type Processor struct {
	svcCtx *svc.ServiceContext
	logger logx.Logger
}

// NewProcessor 创建告警处理器
func NewProcessor(svcCtx *svc.ServiceContext) *Processor {
	return &Processor{
		svcCtx: svcCtx,
		logger: logx.WithContext(context.Background()),
	}
}

// ProcessAlertWithRawString 处理告警（使用原始字符串）
func (p *Processor) ProcessAlertWithRawString(ctx context.Context, rawAlertID, integrationsID, rawDataStr string) (map[string]interface{}, error) {
	// 1. 获取集成信息
	integrationInfo, err := p.GetIntegrationInfo(ctx, integrationsID)
	if err != nil {
		return nil, fmt.Errorf("获取集成信息失败: %v", err)
	}

	// 提取集成类型
	integrationType, _ := integrationInfo["type"].(string)
	if integrationType == "" {
		integrationType = "default" // 默认使用default-http
	}

	// 2. 使用告警处理器进行标准化处理
	processor, err := alertprocessor.NewAlertProcessor(integrationType)
	if err != nil {
		return nil, fmt.Errorf("创建告警处理器失败: %v", err)
	}

	// 根据集成类型选择处理方式
	var alertData *alertprocessor.AlertData
	alertData, err = processor.ProcessRawString(rawDataStr)
	if err != nil {
		return nil, fmt.Errorf("处理告警数据失败: %v", err)
	}

	return p.buildProcessedAlert(ctx, alertData, rawAlertID, integrationsID, integrationInfo)
}

// ProcessAlert 处理json告警
func (p *Processor) ProcessAlert(ctx context.Context, rawAlertID, integrationsID string, rawData map[string]interface{}) (map[string]interface{}, error) {
	// 1. 获取集成信息
	integrationInfo, err := p.GetIntegrationInfo(ctx, integrationsID)
	if err != nil {
		return nil, fmt.Errorf("获取集成信息失败: %v", err)
	}

	// 提取集成类型
	integrationType, _ := integrationInfo["type"].(string)
	if integrationType == "" {
		integrationType = "grafana" // 默认使用 grafana
	}

	// 2. 使用告警处理器进行标准化处理
	processor, err := alertprocessor.NewAlertProcessor(integrationType)
	if err != nil {
		return nil, fmt.Errorf("创建告警处理器失败: %v", err)
	}

	alertData, err := processor.Process(rawData)
	if err != nil {
		return nil, fmt.Errorf("处理告警数据失败: %v", err)
	}

	return p.buildProcessedAlert(ctx, alertData, rawAlertID, integrationsID, integrationInfo)
}

// buildProcessedAlert 构建处理后的告警数据
func (p *Processor) buildProcessedAlert(ctx context.Context, alertData *alertprocessor.AlertData, rawAlertID, integrationsID string, integrationInfo map[string]interface{}) (map[string]interface{}, error) {
	// 1. 将AlertData转换为map
	processedAlert := map[string]interface{}{
		"title":           alertData.Title,
		"description":     alertData.Description,
		"alert_severity":  alertData.Severity,
		"alert_key":       alertData.AlertKey,
		"labels":          alertData.Labels,
		"raw_data":        alertData.RawData,
		"raw_alert_id":    rawAlertID,
		"integrations_id": integrationsID,
	}

	// 2. 创建规则处理器
	ruleProcessor := ruleprocessor.NewRuleProcessor()

	// 注册各种规则处理器
	ruleProcessor.RegisterRule(severity.NewSeverityRule())
	ruleProcessor.RegisterRule(deduplication.NewDeduplicationRule())
	ruleProcessor.RegisterRule(grouping.NewGroupingRule())
	ruleProcessor.RegisterRule(suppression.NewSuppressionRule())
	ruleProcessor.RegisterRule(convergence.NewConvergenceRule())

	// 提取规则配置
	ruleConfigs := make(map[ruleprocessor.RuleType]map[string]interface{})

	// 从集成配置中提取规则配置
	if config, ok := integrationInfo["config"].(map[string]interface{}); ok && config != nil {
		// 提取告警分级规则
		if severityRules, ok := config["severity_rules"].(map[string]interface{}); ok {
			ruleConfigs[ruleprocessor.SeverityRule] = severityRules
		}

		// 提取告警分组规则
		if groupingRules, ok := config["grouping_rules"].(map[string]interface{}); ok {
			ruleConfigs[ruleprocessor.GroupingRule] = groupingRules
		}

		// 提取告警去重规则
		if deduplicationRules, ok := config["deduplication_rules"].(map[string]interface{}); ok {
			ruleConfigs[ruleprocessor.DeduplicationRule] = deduplicationRules
		}

		// 提取告警抑制规则
		if suppressionRules, ok := config["suppression_rules"].(map[string]interface{}); ok {
			ruleConfigs[ruleprocessor.SuppressionRule] = suppressionRules
		}

		// 提取告警收敛规则
		if convergenceRules, ok := config["convergence_rules"].(map[string]interface{}); ok {
			ruleConfigs[ruleprocessor.ConvergenceRule] = convergenceRules
		}
	}

	// 应用规则处理(未实现)
	// processedAlert, err = ruleProcessor.ProcessAllRules(ctx, processedAlert, ruleConfigs)
	// if err != nil {
	// 	return nil, fmt.Errorf("应用规则处理失败: %v", err)
	// }

	// 3. 获取路由空间信息
	spaceID, _ := integrationInfo["space_id"].(float64)
	spaceInfo, err := p.GetRouteSpaceInfo(ctx, uint64(spaceID))
	if err != nil {
		return nil, fmt.Errorf("获取路由空间信息失败: %v", err)
	}

	// 添加空间ID到处理后的告警
	if spaceIDStr, ok := spaceInfo["id"].(string); ok {
		processedAlert["space_id"] = spaceIDStr
	}

	return processedAlert, nil
}

// CreateStandardAlert 创建标准告警
func (p *Processor) CreateStandardAlert(ctx context.Context, processedAlert map[string]interface{}) (string, error) {
	// 提取必要的参数
	rawAlertID, _ := processedAlert["raw_alert_id"].(string)
	spaceID, _ := processedAlert["space_id"].(string)

	// 创建 AlertService 逻辑
	alertLogic := alertservicelogic.NewCreateAlertLogic(ctx, p.svcCtx)

	// 调用 CreateStandardAlert 方法
	alertID, err := alertLogic.CreateStandardAlert(ctx, processedAlert, rawAlertID, spaceID)
	if err != nil {
		return "", fmt.Errorf("创建标准告警失败: %v", err)
	}

	return alertID, nil
}

// GetIntegrationInfo 获取集成信息
func (p *Processor) GetIntegrationInfo(ctx context.Context, integrationsID string) (map[string]interface{}, error) {
	// 尝试使用 integration RPC 客户端获取集成信息
	// if p.svcCtx.IntegrationRpc != nil {
	// 	resp, err := p.svcCtx.IntegrationRpc.GetIntegrationById(ctx, &integration.IDReq{
	// 		Id: integrationsID,
	// 	})
	// 	if err == nil && resp != nil {
	// 		// 将 RPC 响应转换为 map
	// 		result := map[string]interface{}{
	// 			"id":       resp.Id,
	// 			"name":     resp.Name,
	// 			"type":     resp.Type,
	// 			"space_id": resp.SpaceId,
	// 		}

	// 		// 解析配置
	// 		var config map[string]interface{}
	// 		if resp.Config != "" {
	// 			if err := json.Unmarshal([]byte(resp.Config), &config); err == nil {
	// 				result["config"] = config
	// 			}
	// 		}

	// 		return result, nil
	// 	}
	// 	// 如果 RPC 调用失败，记录错误并使用模拟数据
	// 	p.logger.Errorf("通过 RPC 获取集成信息失败: %v", err)
	// }

	// 使用模拟数据
	p.logger.Infof("使用模拟的集成信息数据")
	return map[string]interface{}{
		"id":   integrationsID,
		"name": "测试集成",
		"type": "aliyun_event",
		"config": map[string]interface{}{
			"severity_rules": map[string]interface{}{
				"critical": []string{"critical", "高"},
				"warning":  []string{"warning", "中"},
				"info":     []string{"info", "低"},
			},
			"grouping_rules": map[string]interface{}{
				"pattern_rules": []interface{}{
					map[string]interface{}{
						"pattern":    ".*CPU.*",
						"group_name": "cpu-alerts",
						"field":      "title",
					},
				},
			},
			"suppression_rules": map[string]interface{}{
				"time_rules": []interface{}{
					map[string]interface{}{
						"start_time": "23:00",
						"end_time":   "06:00",
						"days":       []string{"Mon", "Tue", "Wed", "Thu", "Fri"},
						"reason":     "工作日夜间维护时段",
					},
				},
			},
			"convergence_rules": map[string]interface{}{
				"time_window_rules": []interface{}{
					map[string]interface{}{
						"window_minutes": 30.0,
						"max_count":      3.0,
						"severity":       "critical",
						"reason":         "30分钟内最多发送3条严重告警",
					},
				},
			},
		},
		"space_id": 1,
	}, nil
}

// GetRouteSpaceInfo 获取路由空间信息
func (p *Processor) GetRouteSpaceInfo(ctx context.Context, spaceID uint64) (map[string]interface{}, error) {
	// 尝试使用 routespace RPC 客户端获取路由空间信息
	// if p.svcCtx.SpaceRpc != nil {
	// 	resp, err := p.svcCtx.SpaceRpc.GetSpaceById(ctx, &space.IDReq{
	// 		Id: spaceID,
	// 	})
	// 	if err == nil && resp != nil {
	// 		// 将 RPC 响应转换为 map
	// 		result := map[string]interface{}{
	// 			"id":   resp.Id,
	// 			"name": resp.Name,
	// 		}

	// 		// 解析通知配置
	// 		notifyConfig := map[string]interface{}{}

	// 		// 处理通知渠道
	// 		var channels []string
	// 		for _, ch := range resp.NotifyChannels {
	// 			channels = append(channels, ch)
	// 		}
	// 		notifyConfig["channels"] = channels

	// 		// 处理通知用户
	// 		var users []string
	// 		for _, user := range resp.NotifyUsers {
	// 			users = append(users, user)
	// 		}
	// 		notifyConfig["users"] = users

	// 		result["notify_config"] = notifyConfig

	// 		return result, nil
	// 	}
	// 	// 如果 RPC 调用失败，记录错误并使用模拟数据
	// 	p.logger.Errorf("通过 RPC 获取路由空间信息失败: %v", err)
	// }

	// 使用模拟数据
	p.logger.Infof("使用模拟的路由空间信息数据")
	return map[string]interface{}{
		"id":   strconv.FormatUint(spaceID, 10),
		"name": "测试空间",
		"notify_config": map[string]interface{}{
			"channels": []string{"email"},
			"users":    []string{"<EMAIL>"},
		},
	}, nil
}
