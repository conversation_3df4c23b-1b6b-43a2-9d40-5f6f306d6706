package incident

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gofrs/uuid"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/incident"
	incidentlogic "gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/logic/incidentservice"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/svc"
)

// Processor 故障处理器
type Processor struct {
	svcCtx *svc.ServiceContext
	logger logx.Logger
}

// NewProcessor 创建故障处理器
func NewProcessor(svcCtx *svc.ServiceContext) *Processor {
	return &Processor{
		svcCtx: svcCtx,
		logger: logx.WithContext(context.Background()),
	}
}

// ShouldCreateIncident 判断是否需要创建故障
func (p *Processor) ShouldCreateIncident(processedAlert map[string]interface{}) bool {
	// 检查是否被抑制
	if suppressed, ok := processedAlert["suppressed"].(bool); ok && suppressed {
		return false
	}

	// 检查严重程度，只有critical和warning级别的告警才创建故障
	if severity, ok := processedAlert["alert_severity"].(string); ok {
		switch strings.ToLower(severity) {
		case "critical", "warning":
			return true
		default:
			return false
		}
	}

	// 默认不创建故障
	return false
}

// CreateIncidentFromAlert 从告警创建故障
func (p *Processor) CreateIncidentFromAlert(ctx context.Context, processedAlert map[string]interface{}, alertID string, routeSpace map[string]interface{}) (string, error) {
	// 检查是否需要创建故障
	if !p.ShouldCreateIncident(processedAlert) {
		return "", fmt.Errorf("不需要创建故障")
	}

	// 提取告警信息
	title, _ := processedAlert["title"].(string)
	description, _ := processedAlert["description"].(string)
	alertSeverity, _ := processedAlert["alert_severity"].(string)
	integrationsID, _ := processedAlert["integrations_id"].(string)
	spaceID := ""

	// 从路由空间中提取空间ID
	if rsMap, ok := routeSpace.(map[string]interface{}); ok {
		if id, ok := rsMap["id"].(string); ok {
			spaceID = id
		}
	} else if spaceIDFromAlert, ok := processedAlert["space_id"].(string); ok {
		spaceID = spaceIDFromAlert
	}

	// 提取告警分组信息
	var alertGroup string
	if labels, ok := processedAlert["labels"].(map[string]string); ok {
		if group, exists := labels["alert_group"]; exists {
			alertGroup = group
		}
	}

	// 如果没有分组，使用默认分组
	if alertGroup == "" {
		alertGroup = fmt.Sprintf("default-%s", title)
	}

	// 创建故障逻辑
	incidentLogic := incidentlogic.NewCreateIncidentLogic(ctx, p.svcCtx)

	// 检查是否存在相同告警组的故障
	existingIncident, err := p.svcCtx.DB.Incident.Query().
		Where(incident.DedupKey(alertGroup)).
		Where(incident.IntegrationsID(uuid.FromStringOrNil(integrationsID))).
		Where(incident.ProgressEQ(incident.ProgressTriggered)).
		First(ctx)

	// 如果存在相同故障，更新计数和最后更新时间
	if err == nil && existingIncident != nil {
		// 更新现有故障
		updatedIncident, err := p.svcCtx.DB.Incident.UpdateOneID(existingIncident.ID).
			SetLastTime(time.Now()).
			SetAlertCnt(existingIncident.AlertCnt + 1).
			Save(ctx)

		if err != nil {
			return "", fmt.Errorf("更新故障失败: %v", err)
		}

		// 更新告警的故障ID
		err = p.updateAlertIncidentID(ctx, alertID, updatedIncident.ID.String())
		if err != nil {
			p.logger.Errorf("更新告警故障ID失败: %v", err)
			// 不影响主流程，继续执行
		}

		p.logger.Infof("更新现有故障成功: %s, 告警ID: %s, 告警组: %s", updatedIncident.ID.String(), alertID, alertGroup)
		return updatedIncident.ID.String(), nil
	}

	// 解析严重程度
	var severityEnum incident.IncidentSeverity
	switch strings.ToLower(alertSeverity) {
	case "critical":
		severityEnum = incident.IncidentSeverityCritical
	case "warning":
		severityEnum = incident.IncidentSeverityWarning
	default:
		severityEnum = incident.IncidentSeverityInfo
	}

	// 解析分组方法
	var groupMethod incident.GroupMethod
	if method, ok := processedAlert["group_method"].(string); ok {
		switch method {
		case "pattern":
			groupMethod = incident.GroupMethodPattern
		default:
			groupMethod = incident.GroupMethodNone
		}
	} else {
		groupMethod = incident.GroupMethodNone
	}

	// 创建故障
	now := time.Now()

	// 创建故障记录
	newIncident, err := p.svcCtx.DB.Incident.Create().
		SetTitle(title).
		SetDescription(description).
		SetIncidentSeverity(severityEnum).
		SetProgress(incident.ProgressTriggered).
		SetDedupKey(alertGroup).
		SetStartTime(now).
		SetLastTime(now).
		SetEverMuted(false).
		SetSnoozedBefore(0).
		SetGroupMethod(groupMethod).
		SetAlertCnt(1).
		SetIntegrationsID(uuid.FromStringOrNil(integrationsID)).
		SetSpaceID(spaceID).
		Save(ctx)

	if err != nil {
		return "", fmt.Errorf("创建故障失败: %v", err)
	}

	// 更新告警的故障ID
	err = p.updateAlertIncidentID(ctx, alertID, newIncident.ID.String())
	if err != nil {
		p.logger.Errorf("更新告警故障ID失败: %v", err)
		// 不影响主流程，继续执行
	}

	p.logger.Infof("从告警创建故障成功: %s, 告警ID: %s, 告警组: %s", newIncident.ID.String(), alertID, alertGroup)
	return newIncident.ID.String(), nil
}

// updateAlertIncidentID 更新告警关联的故障ID
func (p *Processor) updateAlertIncidentID(ctx context.Context, alertID, incidentID string) error {
	// 更新告警的故障ID
	_, err := p.svcCtx.DB.Alert.UpdateOneID(uuid.FromStringOrNil(alertID)).
		SetIncidentID(incidentID).
		Save(ctx)

	return err
}

// UpdateIncidentStatus 更新故障状态
func (p *Processor) UpdateIncidentStatus(ctx context.Context, incidentID, status string) error {
	// 这里应该调用 incidentservice 的 UpdateIncident 方法
	// 由于我们正在重构，这里只记录日志
	p.logger.Infof("更新故障状态: %s, 状态: %s", incidentID, status)
	return nil
}

// CloseIncident 关闭故障
func (p *Processor) CloseIncident(ctx context.Context, incidentID string, reason string) error {
	// 这里应该调用 incidentservice 的 CloseIncident 方法
	// 由于我们正在重构，这里只记录日志
	p.logger.Infof("关闭故障: %s, 原因: %s", incidentID, reason)
	return nil
}
