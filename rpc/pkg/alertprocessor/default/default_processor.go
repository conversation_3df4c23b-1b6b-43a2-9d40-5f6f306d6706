package defaultprocessor

import (
	"encoding/json"
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor/types"
)

// DefaultAlertProcessor 默认告警处理器
type DefaultAlertProcessor struct{}

// Process 处理默认告警数据
func (p *DefaultAlertProcessor) Process(rawData map[string]interface{}) (*types.AlertData, error) {
	// 尝试从原始数据中提取基本信息
	title, _ := rawData["title"].(string)
	if title == "" {
		title = "Unknown Alert"
	}

	description, _ := rawData["description"].(string)

	severity, _ := rawData["severity"].(string)
	if severity == "" {
		severity = "info"
	}

	// 生成告警去重键
	alertKey := fmt.Sprintf("%s-%s", title, severity)

	// 提取标签
	labels := make(map[string]string)
	if labelsRaw, ok := rawData["labels"].(map[string]interface{}); ok {
		for k, v := range labelsRaw {
			if strVal, ok := v.(string); ok {
				labels[k] = strVal
			}
		}
	}

	return &types.AlertData{
		Title:       title,
		Description: description,
		Severity:    severity,
		AlertKey:    alertKey,
		Labels:      labels,
		RawData:     rawData,
	}, nil
}

// ProcessRawString 处理原始字符串数据
// 默认处理器尝试将字符串解析为 JSON，如果失败则创建一个包含原始字符串的告警
func (p *DefaultAlertProcessor) ProcessRawString(rawDataStr string) (*types.AlertData, error) {
	// 尝试解析为 JSON
	var rawData map[string]interface{}
	if err := json.Unmarshal([]byte(rawDataStr), &rawData); err != nil {
		// 如果解析失败，创建一个包含原始字符串的告警
		rawData = map[string]interface{}{
			"title":       "Raw Alert",
			"description": rawDataStr,
			"severity":    "info",
			"raw_string":  rawDataStr,
		}
	}

	// 调用现有的 Process 方法
	return p.Process(rawData)
}

// New 创建默认告警处理器
func New() types.AlertProcessor {
	return &DefaultAlertProcessor{}
}
