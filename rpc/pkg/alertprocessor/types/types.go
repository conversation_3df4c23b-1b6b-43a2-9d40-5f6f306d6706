package types

// AlertData 处理后的告警数据
type AlertData struct {
	Title       string                 // 告警标题
	Description string                 // 告警描述
	Severity    string                 // 告警严重程度: critical, warning, info
	AlertKey    string                 // 告警去重键
	Labels      map[string]string      // 标签
	RawData     map[string]interface{} // 原始数据
}

// AlertProcessor 告警处理器接口
type AlertProcessor interface {
	Process(rawData map[string]interface{}) (*AlertData, error)
	// ProcessRawString 处理原始字符串数据，用于特定格式的告警源
	ProcessRawString(rawDataStr string) (*AlertData, error)
}
