package zabbix

import (
	"encoding/json"
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor/types"
)

// ZabbixAlertProcessor Zabbix告警处理器
type ZabbixAlertProcessor struct{}

// Process 处理Zabbix告警数据
func (p *ZabbixAlertProcessor) Process(rawData map[string]interface{}) (*types.AlertData, error) {
	// 提取Zabbix特定的告警信息
	var title, description, severity, alertKey string
	labels := make(map[string]string)

	// 尝试从Zabbix告警格式中提取信息
	if triggerName, ok := rawData["trigger_name"].(string); ok {
		title = triggerName
	}

	if triggerDescription, ok := rawData["trigger_description"].(string); ok {
		description = triggerDescription
	}

	// 提取严重程度
	if priorityRaw, ok := rawData["trigger_priority"].(string); ok {
		switch priorityRaw {
		case "Disaster", "High":
			severity = "critical"
		case "Average", "Warning":
			severity = "warning"
		default:
			severity = "info"
		}
	}

	// 提取主机信息作为标签
	if host, ok := rawData["host"].(string); ok {
		labels["host"] = host
	}

	// 提取其他可能的标签
	if item, ok := rawData["item"].(string); ok {
		labels["item"] = item
	}

	// 生成告警去重键
	if triggerID, ok := rawData["trigger_id"].(string); ok {
		alertKey = fmt.Sprintf("zabbix-%s", triggerID)
	} else {
		alertKey = fmt.Sprintf("zabbix-%s", title)
	}

	return &types.AlertData{
		Title:       title,
		Description: description,
		Severity:    severity,
		AlertKey:    alertKey,
		Labels:      labels,
		RawData:     rawData,
	}, nil
}

// ProcessRawString 处理原始字符串数据
// Zabbix 处理器尝试将字符串解析为 JSON
func (p *ZabbixAlertProcessor) ProcessRawString(rawDataStr string) (*types.AlertData, error) {
	// 尝试解析为 JSON
	var rawData map[string]interface{}
	if err := json.Unmarshal([]byte(rawDataStr), &rawData); err != nil {
		return nil, fmt.Errorf("解析 Zabbix 告警数据失败: %v", err)
	}

	// 调用现有的 Process 方法
	return p.Process(rawData)
}

// New 创建Zabbix告警处理器
func New() types.AlertProcessor {
	return &ZabbixAlertProcessor{}
}
