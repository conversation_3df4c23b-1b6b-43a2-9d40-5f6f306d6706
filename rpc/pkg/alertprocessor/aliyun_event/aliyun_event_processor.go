package aliyunevent

import (
	"encoding/json"
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor/types"
)

// AliyunEventAlertProcessor 阿里云事件数据告警处理器
type AliyunEventAlertProcessor struct{}

// Process 处理阿里云事件告警数据
func (p *AliyunEventAlertProcessor) Process(rawData map[string]interface{}) (*types.AlertData, error) {
	// 提取阿里云特定的告警信息
	var title, description, severity, alertKey string
	labels := make(map[string]string)

	// 提取基本告警信息
	p.extractBasicInfo(rawData, &title, &description, &severity, labels)

	// 提取事件详细信息
	p.extractEventDetails(rawData, labels)

	// 提取资源信息
	p.extractResourceInfo(rawData, labels)

	// 提取时间信息
	p.extractTimeInfo(rawData, labels)

	// 提取订阅信息
	p.extractSubscriptionInfo(rawData, labels)

	// 生成告警去重键
	alertKey = p.generateAlertKey(rawData, title)

	return &types.AlertData{
		Title:       title,
		Description: description,
		Severity:    severity,
		AlertKey:    alertKey,
		Labels:      labels,
		RawData:     rawData,
	}, nil
}

// extractBasicInfo 提取基本告警信息
func (p *AliyunEventAlertProcessor) extractBasicInfo(rawData map[string]interface{}, title, description, severity *string, labels map[string]string) {
	// 提取策略名称作为标题
	if strategyName, ok := rawData["strategyName"].(string); ok {
		*title = strategyName
		labels["strategy_name"] = strategyName
	}

	// 提取严重程度
	if sev, ok := rawData["severity"].(string); ok {
		switch sev {
		case "CRITICAL":
			*severity = "critical"
		case "WARN":
			*severity = "warning"
		case "INFO":
			*severity = "info"
		case "OK":
			*severity = "info"
		default:
			*severity = "info"
		}
		labels["severity"] = sev
	}

	// 从alert字段中提取更多信息
	if alert, ok := rawData["alert"].(map[string]interface{}); ok {
		// 提取产品信息
		if product, ok := alert["product"].(string); ok {
			labels["product"] = product
		}

		// 提取告警状态
		if alertStatus, ok := alert["alertStatus"].(string); ok {
			labels["alert_status"] = alertStatus
		}

		// 提取事件类型
		if eventType, ok := alert["eventType"].(string); ok {
			labels["event_type"] = eventType
		}

		// 提取业务分组
		if groupName, ok := alert["groupName"].(string); ok {
			labels["group_name"] = groupName
		}

		// 从meta中提取系统事件信息
		if meta, ok := alert["meta"].(map[string]interface{}); ok {
			if sysEventMeta, ok := meta["sysEventMeta"].(map[string]interface{}); ok {
				p.extractSysEventMeta(sysEventMeta, title, labels)
			}
		}
	}

	// 构建描述
	*description = p.buildDescription(rawData, labels)
}

// extractSysEventMeta 提取系统事件元数据
func (p *AliyunEventAlertProcessor) extractSysEventMeta(sysEventMeta map[string]interface{}, title *string, labels map[string]string) {
	// 提取服务类型中文名
	if serviceTypeZh, ok := sysEventMeta["serviceTypeZh"].(string); ok {
		labels["service_type_zh"] = serviceTypeZh
	}

	// 提取事件名称中文
	if eventNameZh, ok := sysEventMeta["eventNameZh"].(string); ok {
		labels["event_name_zh"] = eventNameZh
		// 如果没有策略名称，使用事件名称作为标题
		if *title == "" {
			*title = eventNameZh
		}
	}

	// 提取事件类型
	if eventType, ok := sysEventMeta["eventType"].(string); ok {
		labels["sys_event_type"] = eventType
	}

	// 提取地域名称中文
	if regionNameZh, ok := sysEventMeta["regionNameZh"].(string); ok {
		labels["region_name_zh"] = regionNameZh
	}

	// 提取实例名称
	if instanceName, ok := sysEventMeta["instanceName"].(string); ok {
		labels["instance_name"] = instanceName
	}
}

// extractEventDetails 提取事件详细信息
func (p *AliyunEventAlertProcessor) extractEventDetails(rawData map[string]interface{}, labels map[string]string) {
	if alert, ok := rawData["alert"].(map[string]interface{}); ok {
		if eventContentMap, ok := alert["eventContentMap"].(map[string]interface{}); ok {
			// 提取事件状态
			if eventStatus, ok := eventContentMap["eventStatus"].(string); ok {
				labels["event_status"] = eventStatus
			}

			// 提取事件ID
			if eventId, ok := eventContentMap["eventId"].(string); ok {
				labels["event_id"] = eventId
			}

			// 提取实例ID
			if instanceId, ok := eventContentMap["instanceId"].(string); ok {
				labels["instance_id"] = instanceId
			}

			// 提取原因
			if reason, ok := eventContentMap["reason"].(string); ok {
				labels["reason"] = reason
			}

			// 提取原因代码
			if reasonCode, ok := eventContentMap["reasonCode"].(string); ok {
				labels["reason_code"] = reasonCode
			}

			// 提取弹性IP地址
			if eipAddress, ok := eventContentMap["eipAddress"].(string); ok {
				labels["eip_address"] = eipAddress
			}

			// 提取私有IP地址
			if privateIpAddress, ok := eventContentMap["privateIpAddress"].([]interface{}); ok {
				if len(privateIpAddress) > 0 {
					if ip, ok := privateIpAddress[0].(string); ok {
						labels["private_ip_address"] = ip
					}
				}
			}

			// 提取公网IP地址
			if publicIpAddress, ok := eventContentMap["publicIpAddress"].([]interface{}); ok {
				if len(publicIpAddress) > 0 {
					if ip, ok := publicIpAddress[0].(string); ok {
						labels["public_ip_address"] = ip
					}
				}
			}
		}
	}
}

// extractResourceInfo 提取资源信息
func (p *AliyunEventAlertProcessor) extractResourceInfo(rawData map[string]interface{}, labels map[string]string) {
	if alert, ok := rawData["alert"].(map[string]interface{}); ok {
		if meta, ok := alert["meta"].(map[string]interface{}); ok {
			if sysEventMeta, ok := meta["sysEventMeta"].(map[string]interface{}); ok {
				// 提取实例名称
				if instanceName, ok := sysEventMeta["instanceName"].(string); ok {
					labels["resource_instance_name"] = instanceName
				}

				// 提取地域名称
				if regionNameZh, ok := sysEventMeta["regionNameZh"].(string); ok {
					labels["resource_region"] = regionNameZh
				}
			}
		}
	}
}

// extractTimeInfo 提取时间信息
func (p *AliyunEventAlertProcessor) extractTimeInfo(rawData map[string]interface{}, labels map[string]string) {
	if alert, ok := rawData["alert"].(map[string]interface{}); ok {
		if eventContentMap, ok := alert["eventContentMap"].(map[string]interface{}); ok {
			// 提取发布时间
			if publishTime, ok := eventContentMap["publishTime"].(string); ok {
				labels["publish_time"] = publishTime
			}

			// 提取开始时间
			if executeStartTime, ok := eventContentMap["executeStartTime"].(string); ok {
				labels["execute_start_time"] = executeStartTime
			}

			// 提取结束时间
			if executeFinishTime, ok := eventContentMap["executeFinishTime"].(string); ok {
				labels["execute_finish_time"] = executeFinishTime
			}
		}
	}
}

// extractSubscriptionInfo 提取订阅信息
func (p *AliyunEventAlertProcessor) extractSubscriptionInfo(rawData map[string]interface{}, labels map[string]string) {
	if subscription, ok := rawData["subscription"].(map[string]interface{}); ok {
		// 提取订阅条件
		if conditions, ok := subscription["conditions"].([]interface{}); ok {
			for i, cond := range conditions {
				if condMap, ok := cond.(map[string]interface{}); ok {
					if field, ok := condMap["field"].(string); ok {
						labels[fmt.Sprintf("subscription_condition_%d_field", i)] = field
					}
					if op, ok := condMap["op"].(string); ok {
						labels[fmt.Sprintf("subscription_condition_%d_op", i)] = op
					}
					if value, ok := condMap["value"].(string); ok {
						labels[fmt.Sprintf("subscription_condition_%d_value", i)] = value
					}
				}
			}
		}
	}
}

// generateAlertKey 生成告警去重键
func (p *AliyunEventAlertProcessor) generateAlertKey(rawData map[string]interface{}, title string) string {
	// 优先使用事件ID作为去重键
	if alert, ok := rawData["alert"].(map[string]interface{}); ok {
		if eventContentMap, ok := alert["eventContentMap"].(map[string]interface{}); ok {
			if eventId, ok := eventContentMap["eventId"].(string); ok {
				return fmt.Sprintf("aliyun-event-%s", eventId)
			}
		}
	}

	// 其次使用策略名称
	if strategyName, ok := rawData["strategyName"].(string); ok {
		return fmt.Sprintf("aliyun-event-%s", strategyName)
	}

	// 最后使用标题
	return fmt.Sprintf("aliyun-event-%s", title)
}

// buildDescription 构建告警描述
func (p *AliyunEventAlertProcessor) buildDescription(rawData map[string]interface{}, labels map[string]string) string {
	var description string

	// 获取基本信息
	severity := labels["severity"]
	eventStatus := labels["event_status"]
	eventType := labels["event_type"]
	reason := labels["reason"]

	// 构建基本描述
	if severity != "" && eventStatus != "" {
		description = fmt.Sprintf("告警等级: %s, 事件状态: %s", severity, eventStatus)
	}

	if eventType != "" {
		if description != "" {
			description += fmt.Sprintf(", 事件类型: %s", eventType)
		} else {
			description = fmt.Sprintf("事件类型: %s", eventType)
		}
	}

	if reason != "" {
		if description != "" {
			description += fmt.Sprintf(", 原因: %s", reason)
		} else {
			description = fmt.Sprintf("原因: %s", reason)
		}
	}

	// 如果没有构建出描述，使用默认描述
	if description == "" {
		description = "阿里云事件告警"
	}

	return description
}

// ProcessRawString 处理原始字符串数据
// 阿里云事件处理器尝试将字符串解析为 JSON
func (p *AliyunEventAlertProcessor) ProcessRawString(rawDataStr string) (*types.AlertData, error) {
	// 尝试解析为 JSON
	var rawData map[string]interface{}
	if err := json.Unmarshal([]byte(rawDataStr), &rawData); err != nil {
		return nil, fmt.Errorf("解析阿里云事件告警数据失败: %v", err)
	}

	// 调用现有的 Process 方法
	return p.Process(rawData)
}

// New 创建阿里云告警处理器
func New() types.AlertProcessor {
	return &AliyunEventAlertProcessor{}
}
