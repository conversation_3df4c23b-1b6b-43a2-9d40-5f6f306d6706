package prometheus

import (
	"encoding/json"
	"fmt"
	"strings"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor/types"
)

// PrometheusAlertProcessor Prometheus告警处理器
type PrometheusAlertProcessor struct{}

// Process 处理Prometheus告警数据
func (p *PrometheusAlertProcessor) Process(rawData map[string]interface{}) (*types.AlertData, error) {
	// 提取Prometheus特定的告警信息
	var title, description, severity, alertKey string
	labels := make(map[string]string)

	// 尝试从Prometheus告警格式中提取信息
	// Prometheus告警通常有alerts数组
	if alertsRaw, ok := rawData["alerts"].([]interface{}); ok && len(alertsRaw) > 0 {
		if alert, ok := alertsRaw[0].(map[string]interface{}); ok {
			// 提取标题
			if labelsMap, ok := alert["labels"].(map[string]interface{}); ok {
				if alertname, ok := labelsMap["alertname"].(string); ok {
					title = alertname
				}
			}

			// 提取描述
			if annotations, ok := alert["annotations"].(map[string]interface{}); ok {
				if desc, ok := annotations["description"].(string); ok {
					description = desc
				} else if summary, ok := annotations["summary"].(string); ok {
					description = summary
				} else if message, ok := annotations["message"].(string); ok {
					description = message
				}
			}

			// 提取严重程度
			if labelsMap, ok := alert["labels"].(map[string]interface{}); ok {
				if severityRaw, ok := labelsMap["severity"].(string); ok {
					switch strings.ToLower(severityRaw) {
					case "critical", "error", "fatal":
						severity = "critical"
					case "warning", "warn":
						severity = "warning"
					default:
						severity = "info"
					}
				}
			}

			// 如果没有从标签中提取到严重程度，尝试从状态中提取
			if severity == "" {
				if status, ok := alert["status"].(string); ok {
					if status == "firing" {
						severity = "critical"
					} else {
						severity = "info"
					}
				}
			}

			// 提取标签
			if labelsMap, ok := alert["labels"].(map[string]interface{}); ok {
				for k, v := range labelsMap {
					if strVal, ok := v.(string); ok {
						labels[k] = strVal
					}
				}
			}

			// 生成告警去重键
			if fingerprint, ok := alert["fingerprint"].(string); ok {
				alertKey = fingerprint
			} else if labelsMap, ok := alert["labels"].(map[string]interface{}); ok {
				// 如果没有fingerprint，使用alertname和instance组合
				alertname, _ := labelsMap["alertname"].(string)
				instance, _ := labelsMap["instance"].(string)
				if alertname != "" && instance != "" {
					alertKey = fmt.Sprintf("prometheus-%s-%s", alertname, instance)
				}
			}
		}
	}

	// 如果无法提取标题，使用默认值
	if title == "" {
		title = "Prometheus Alert"
	}

	// 如果无法提取严重程度，使用默认值
	if severity == "" {
		severity = "info"
	}

	// 如果无法生成告警去重键，使用默认值
	if alertKey == "" {
		alertKey = fmt.Sprintf("prometheus-%s", title)
	}

	return &types.AlertData{
		Title:       title,
		Description: description,
		Severity:    severity,
		AlertKey:    alertKey,
		Labels:      labels,
		RawData:     rawData,
	}, nil
}

// ProcessRawString 处理原始字符串数据
// Prometheus 处理器尝试将字符串解析为 JSON
func (p *PrometheusAlertProcessor) ProcessRawString(rawDataStr string) (*types.AlertData, error) {
	// 尝试解析为 JSON
	var rawData map[string]interface{}
	if err := json.Unmarshal([]byte(rawDataStr), &rawData); err != nil {
		return nil, fmt.Errorf("解析 Prometheus 告警数据失败: %v", err)
	}

	// 调用现有的 Process 方法
	return p.Process(rawData)
}

// New 创建Prometheus告警处理器
func New() types.AlertProcessor {
	return &PrometheusAlertProcessor{}
}
