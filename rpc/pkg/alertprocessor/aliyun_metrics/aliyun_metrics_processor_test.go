package aliyunmetrics

import (
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAliyunMericsAlertProcessor_Process(t *testing.T) {
	processor := &AliyunMericsAlertProcessor{}

	// 测试数据1: ALERT状态
	alertRawData := "lastTime=2%E5%88%86%E9%92%9F&rawMetricName=cpu_total&metricName=%28Agent%29cpu.total&instanceName=mechabreak-sh-db-jump%2F*************&signature=7daOm%2FKXmAXEHIeQfLF5I9mnlyM%3D&transId=d5b78b46-687e-11f0-8fbf-00163e457737&groupId=240824115&regionName=%E5%8D%8E%E4%B8%9C2%28%E4%B8%8A%E6%B5%B7%29&alertName=Host.cpu.total&alertState=ALERT&ruleId=applyTemplate_2239958_gktcdnnjbl65u6io8kc2lgem97&timestamp=1753355440940&instanceInfo=%7B%22instanceId%22%3A%22i-uf64znm1268aktjrewz8%22%2C%22instanceName%22%3A%22mechabreak-sh-db-jump%22%2C%22vpc%22%3A%7B%22vswitchInstanceId%22%3A%22vsw-uf618f8tocxout9600ir9%22%2C%22vpcInstanceId%22%3A%22vpc-uf6vg1zf8bgoavdf6oyqz%22%7D%2C%22aliUid%22%3A1527004597892166%2C%22category%22%3A%22ECS%22%2C%22region%22%3A%7B%22regionId%22%3A%22cn-shanghai%22%2C%22availabilityZone%22%3A%22cn-shanghai-n%22%7D%2C%22networkType%22%3A%22VPC%22%2C%22dimension%22%3A%7B%7D%2C%22tags%22%3A%5B%7B%22value%22%3A%22i-uf64znm1268aktjrewz8%22%2C%22key%22%3A%22instanceId%22%7D%2C%7B%22value%22%3A%224b74276e-9db9-42cf-8fed-3bf4fc4d0ef1%22%2C%22key%22%3A%22serialNumber%22%7D%2C%7B%22value%22%3A%221527004597892166%22%2C%22key%22%3A%22aliUid%22%7D%2C%7B%22value%22%3A%22mechabreak-sh-db-jump%22%2C%22key%22%3A%22hostName%22%7D%2C%7B%22value%22%3A%22CentOS++7.9+64%E4%BD%8D%22%2C%22key%22%3A%22os%22%7D%2C%7B%22value%22%3A%22cn-shanghai%22%2C%22key%22%3A%22regionId%22%7D%2C%7B%22value%22%3A%22*************%2C************%22%2C%22key%22%3A%22ipGroup%22%7D%2C%7B%22value%22%3A%223.5.11%22%2C%22key%22%3A%22tianjimonVersion%22%7D%2C%7B%22value%22%3A%22*************%22%2C%22key%22%3A%22eipAddress%22%7D%2C%7B%22value%22%3A%22eip-uf61a8v6e4nkrpyv1fmwv%22%2C%22key%22%3A%22eipId%22%7D%2C%7B%22value%22%3A%22vpc%22%2C%22key%22%3A%22networkType%22%7D%2C%7B%22value%22%3A%225120000%22%2C%22key%22%3A%22eipBandwidth%22%7D%2C%7B%22value%22%3A%22************%22%2C%22key%22%3A%22intranetIp%22%7D%2C%7B%22value%22%3A%22vpc-uf6vg1zf8bgoavdf6oyqz%22%2C%22key%22%3A%22vpcInstanceId%22%7D%2C%7B%22value%22%3A%22cn-shanghai-n%22%2C%22key%22%3A%22availabilityZone%22%7D%2C%7B%22value%22%3A%22vsw-uf618f8tocxout9600ir9%22%2C%22key%22%3A%22vswitchInstanceId%22%7D%2C%7B%22value%22%3A%22ecs.c6%22%2C%22key%22%3A%22instanceTypeFamily%22%7D%2C%7B%22value%22%3A%221360495478%22%2C%22key%22%3A%22id%22%7D%2C%7B%22value%22%3A%22cn-shanghai%22%2C%22key%22%3A%22region%22%7D%2C%7B%22value%22%3A%22Linux%22%2C%22key%22%3A%22operatingSystem%22%7D%5D%7D&dimensionsOriginal=%7B%22instanceId%22%3A%22i-uf64znm1268aktjrewz8%22%2C%22userId%22%3A%221527004597892166%22%7D&expression=%24Average+%3E+90&productGroupName=%E8%A7%A3%E9%99%90%E6%9C%BA%E4%B8%9A%E5%8A%A1%E8%BF%90%E7%BB%B4%E7%BB%84&metricProject=acs_ecs&userId=1527004597892166&curValue=99.81&unit=%25&regionId=cn-shanghai&namespace=acs_ecs&triggerLevel=CRITICAL&preTriggerLevel=null&dimensions=%7BinstanceId%3Di-uf64znm1268aktjrewz8%2C+userId%3D1527004597892166%7D"

	// 测试新的 ProcessRawString 方法
	result, err := processor.ProcessRawString(alertRawData)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证基本信息
	assert.Equal(t, "Host.cpu.total", result.Title)
	assert.Equal(t, "critical", result.Severity)
	assert.Equal(t, "aliyun-metrics-applyTemplate_2239958_gktcdnnjbl65u6io8kc2lgem97", result.AlertKey)
	assert.Contains(t, result.Description, "告警触发")
	assert.Contains(t, result.Description, "99.81%")

	// 验证标签
	assert.Equal(t, "ALERT", result.Labels["alert_state"])
	assert.Equal(t, "99.81", result.Labels["current_value"])
	assert.Equal(t, "%", result.Labels["unit"])
	assert.Equal(t, "mechabreak-sh-db-jump/*************", result.Labels["instance_name"])
	assert.Equal(t, "CRITICAL", result.Labels["trigger_level"])
	assert.Equal(t, "i-uf64znm1268aktjrewz8", result.Labels["instance_id"])
	assert.Equal(t, "ECS", result.Labels["instance_category"])
	assert.Equal(t, "*************", result.Labels["eip_address"])
	assert.Equal(t, "************", result.Labels["intranet_ip"])

	// 解析URL编码的数据（兼容性测试）
	alertData, err := url.ParseQuery(alertRawData)
	assert.NoError(t, err)

	// 转换为map[string]interface{}
	alertRawMap := make(map[string]interface{})
	for k, v := range alertData {
		if len(v) > 0 {
			alertRawMap[k] = v[0]
		}
	}

	// 处理ALERT状态的告警（兼容性测试）
	result2, err := processor.Process(alertRawMap)
	assert.NoError(t, err)
	assert.NotNil(t, result2)

	// 验证基本信息
	assert.Equal(t, "Host.cpu.total", result.Title)
	assert.Equal(t, "critical", result.Severity)
	assert.Equal(t, "aliyun-metrics-applyTemplate_2239958_gktcdnnjbl65u6io8kc2lgem97", result.AlertKey)
	assert.Contains(t, result.Description, "告警触发")
	assert.Contains(t, result.Description, "99.81%")

	// 验证标签
	assert.Equal(t, "ALERT", result.Labels["alert_state"])
	assert.Equal(t, "99.81", result.Labels["current_value"])
	assert.Equal(t, "%", result.Labels["unit"])
	assert.Equal(t, "mechabreak-sh-db-jump/*************", result.Labels["instance_name"])
	assert.Equal(t, "CRITICAL", result.Labels["trigger_level"])
	assert.Equal(t, "i-uf64znm1268aktjrewz8", result.Labels["instance_id"])
	assert.Equal(t, "ECS", result.Labels["instance_category"])
	assert.Equal(t, "*************", result.Labels["eip_address"])
	assert.Equal(t, "************", result.Labels["intranet_ip"])

	// 测试数据2: OK状态
	okRawData := "lastTime=23%E5%88%86%E9%92%9F&rawMetricName=cpu_total&metricName=%28Agent%29cpu.total&instanceName=mechabreak-sh-db-jump%2F*************&signature=ku88BPTd3QiE0UaWISQMVt1%2BXXM%3D&transId=d5b78b46-687e-11f0-8fbf-00163e457737&groupId=240824115&regionName=%E5%8D%8E%E4%B8%9C2%28%E4%B8%8A%E6%B5%B7%29&alertName=Host.cpu.total&alertState=OK&ruleId=applyTemplate_2239958_gktcdnnjbl65u6io8kc2lgem97&timestamp=1753356700936&instanceInfo=%7B%22instanceId%22%3A%22i-uf64znm1268aktjrewz8%22%2C%22instanceName%22%3A%22mechabreak-sh-db-jump%22%2C%22vpc%22%3A%7B%22vswitchInstanceId%22%3A%22vsw-uf618f8tocxout9600ir9%22%2C%22vpcInstanceId%22%3A%22vpc-uf6vg1zf8bgoavdf6oyqz%22%7D%2C%22aliUid%22%3A1527004597892166%2C%22category%22%3A%22ECS%22%2C%22region%22%3A%7B%22regionId%22%3A%22cn-shanghai%22%2C%22availabilityZone%22%3A%22cn-shanghai-n%22%7D%2C%22networkType%22%3A%22VPC%22%2C%22dimension%22%3A%7B%7D%2C%22tags%22%3A%5B%7B%22value%22%3A%22i-uf64znm1268aktjrewz8%22%2C%22key%22%3A%22instanceId%22%7D%2C%7B%22value%22%3A%224b74276e-9db9-42cf-8fed-3bf4fc4d0ef1%22%2C%22key%22%3A%22serialNumber%22%7D%2C%7B%22value%22%3A%221527004597892166%22%2C%22key%22%3A%22aliUid%22%7D%2C%7B%22value%22%3A%22mechabreak-sh-db-jump%22%2C%22key%22%3A%22hostName%22%7D%2C%7B%22value%22%3A%22CentOS++7.9+64%E4%BD%8D%22%2C%22key%22%3A%22os%22%7D%2C%7B%22value%22%3A%22cn-shanghai%22%2C%22key%22%3A%22regionId%22%7D%2C%7B%22value%22%3A%22*************%2C************%22%2C%22key%22%3A%22ipGroup%22%7D%2C%7B%22value%22%3A%223.5.11%22%2C%22key%22%3A%22tianjimonVersion%22%7D%2C%7B%22value%22%3A%22*************%22%2C%22key%22%3A%22eipAddress%22%7D%2C%7B%22value%22%3A%22eip-uf61a8v6e4nkrpyv1fmwv%22%2C%22key%22%3A%22eipId%22%7D%2C%7B%22value%22%3A%22vpc%22%2C%22key%22%3A%22networkType%22%7D%2C%7B%22value%22%3A%225120000%22%2C%22key%22%3A%22eipBandwidth%22%7D%2C%7B%22value%22%3A%22************%22%2C%22key%22%3A%22intranetIp%22%7D%2C%7B%22value%22%3A%22vpc-uf6vg1zf8bgoavdf6oyqz%22%2C%22key%22%3A%22vpcInstanceId%22%7D%2C%7B%22value%22%3A%22cn-shanghai-n%22%2C%22key%22%3A%22availabilityZone%22%7D%2C%7B%22value%22%3A%22vsw-uf618f8tocxout9600ir9%22%2C%22key%22%3A%22vswitchInstanceId%22%7D%2C%7B%22value%22%3A%22ecs.c6%22%2C%22key%22%3A%22instanceTypeFamily%22%7D%2C%7B%22value%22%3A%221360495478%22%2C%22key%22%3A%22id%22%7D%2C%7B%22value%22%3A%22cn-shanghai%22%2C%22key%22%3A%22region%22%7D%2C%7B%22value%22%3A%22Linux%22%2C%22key%22%3A%22operatingSystem%22%7D%5D%7D&dimensionsOriginal=%7B%22instanceId%22%3A%22i-uf64znm1268aktjrewz8%22%2C%22userId%22%3A%221527004597892166%22%7D&expression=%24Average+%3E+90&productGroupName=%E8%A7%A3%E9%99%90%E6%9C%BA%E4%B8%9A%E5%8A%A1%E8%BF%90%E7%BB%B4%E7%BB%84&metricProject=acs_ecs&userId=1527004597892166&curValue=78.6&unit=%25&regionId=cn-shanghai&namespace=acs_ecs&triggerLevel=OK&preTriggerLevel=CRITICAL&dimensions=%7BinstanceId%3Di-uf64znm1268aktjrewz8%2C+userId%3D1527004597892166%7D"

	// 解析OK状态的数据
	okData, err := url.ParseQuery(okRawData)
	assert.NoError(t, err)

	okRawMap := make(map[string]interface{})
	for k, v := range okData {
		if len(v) > 0 {
			okRawMap[k] = v[0]
		}
	}

	// 处理OK状态的告警
	okResult, err := processor.Process(okRawMap)
	assert.NoError(t, err)
	assert.NotNil(t, okResult)

	// 验证OK状态的基本信息
	assert.Equal(t, "Host.cpu.total", okResult.Title)
	assert.Equal(t, "info", okResult.Severity)
	assert.Equal(t, "aliyun-metrics-applyTemplate_2239958_gktcdnnjbl65u6io8kc2lgem97", okResult.AlertKey)
	assert.Contains(t, okResult.Description, "告警恢复")
	assert.Contains(t, okResult.Description, "78.6%")

	// 验证OK状态的标签
	assert.Equal(t, "OK", okResult.Labels["alert_state"])
	assert.Equal(t, "78.6", okResult.Labels["current_value"])
	assert.Equal(t, "CRITICAL", okResult.Labels["pre_trigger_level"])
}

func TestBuildDescription(t *testing.T) {
	processor := &AliyunMericsAlertProcessor{}

	// 测试ALERT状态
	desc := processor.buildDescription("ALERT", "99.81", "%", "$Average > 90")
	assert.Contains(t, desc, "告警触发")
	assert.Contains(t, desc, "99.81%")
	assert.Contains(t, desc, "$Average > 90")

	// 测试OK状态
	desc = processor.buildDescription("OK", "78.6", "%", "$Average > 90")
	assert.Contains(t, desc, "告警恢复")
	assert.Contains(t, desc, "78.6%")
}

func TestDetermineSeverity(t *testing.T) {
	processor := &AliyunMericsAlertProcessor{}

	// 测试CRITICAL级别
	rawData := map[string]interface{}{
		"triggerLevel": "CRITICAL",
	}
	severity := processor.determineSeverity("ALERT", rawData)
	assert.Equal(t, "critical", severity)

	// 测试OK状态
	severity = processor.determineSeverity("OK", rawData)
	assert.Equal(t, "info", severity)

	// 测试WARNING级别
	rawData["triggerLevel"] = "WARN"
	severity = processor.determineSeverity("ALERT", rawData)
	assert.Equal(t, "warning", severity)
}

func TestProcessRawString(t *testing.T) {
	processor := &AliyunMericsAlertProcessor{}

	// 测试原始字符串处理
	rawDataStr := "alertName=Test+Alert&alertState=ALERT&curValue=95.5&unit=%25&triggerLevel=CRITICAL&ruleId=test-rule-123"

	result, err := processor.ProcessRawString(rawDataStr)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证解析结果
	assert.Equal(t, "Test Alert", result.Title)
	assert.Equal(t, "critical", result.Severity)
	assert.Equal(t, "aliyun-metrics-test-rule-123", result.AlertKey)
	assert.Contains(t, result.Description, "告警触发")
	assert.Contains(t, result.Description, "95.5%")

	// 验证标签
	assert.Equal(t, "ALERT", result.Labels["alert_state"])
	assert.Equal(t, "95.5", result.Labels["current_value"])
	assert.Equal(t, "%", result.Labels["unit"])
	assert.Equal(t, "CRITICAL", result.Labels["trigger_level"])
	assert.Equal(t, "test-rule-123", result.Labels["rule_id"])
}

func TestProcessRawStringError(t *testing.T) {
	processor := &AliyunMericsAlertProcessor{}

	// 测试无效的URL编码字符串
	invalidRawDataStr := "invalid%ZZ%data"

	result, err := processor.ProcessRawString(invalidRawDataStr)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "解析阿里云指标数据失败")
}
