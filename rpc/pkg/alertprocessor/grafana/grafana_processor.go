package grafana

import (
	"encoding/json"
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor/types"
)

// GrafanaAlertProcessor Grafana告警处理器
type GrafanaAlertProcessor struct{}

// Process 处理Grafana告警数据
func (p *GrafanaAlertProcessor) Process(rawData map[string]interface{}) (*types.AlertData, error) {
	// 提取Grafana特定的告警信息
	var title, description, severity, alertKey string
	labels := make(map[string]string)

	// 尝试从Grafana告警格式中提取信息
	if alertsRaw, ok := rawData["alerts"].([]interface{}); ok && len(alertsRaw) > 0 {
		if alert, ok := alertsRaw[0].(map[string]interface{}); ok {
			// 提取标题
			if labels, ok := alert["labels"].(map[string]interface{}); ok {
				if alertname, ok := labels["alertname"].(string); ok {
					title = alertname
				}
			}

			// 提取描述
			if annotations, ok := alert["annotations"].(map[string]interface{}); ok {
				if desc, ok := annotations["description"].(string); ok {
					description = desc
				} else if summary, ok := annotations["summary"].(string); ok {
					description = summary
				}
			}

			// 提取严重程度
			if status, ok := alert["status"].(string); ok {
				if status == "firing" {
					severity = "critical"
				} else {
					severity = "info"
				}
			}

			// 提取标签
			if labelsRaw, ok := alert["labels"].(map[string]interface{}); ok {
				for k, v := range labelsRaw {
					if strVal, ok := v.(string); ok {
						labels[k] = strVal
					}
				}
			}

			// 生成告警去重键
			if fingerprint, ok := alert["fingerprint"].(string); ok {
				alertKey = fingerprint
			} else {
				alertKey = fmt.Sprintf("grafana-%s", title)
			}
		}
	}

	// 如果无法提取标题，使用默认值
	if title == "" {
		title = "Grafana Alert"
	}

	// 如果无法提取严重程度，使用默认值
	if severity == "" {
		severity = "info"
	}

	// 如果无法生成告警去重键，使用默认值
	if alertKey == "" {
		alertKey = fmt.Sprintf("grafana-%s", title)
	}

	return &types.AlertData{
		Title:       title,
		Description: description,
		Severity:    severity,
		AlertKey:    alertKey,
		Labels:      labels,
		RawData:     rawData,
	}, nil
}

// ProcessRawString 处理原始字符串数据
// Grafana 处理器尝试将字符串解析为 JSON
func (p *GrafanaAlertProcessor) ProcessRawString(rawDataStr string) (*types.AlertData, error) {
	// 尝试解析为 JSON
	var rawData map[string]interface{}
	if err := json.Unmarshal([]byte(rawDataStr), &rawData); err != nil {
		return nil, fmt.Errorf("解析 Grafana 告警数据失败: %v", err)
	}

	// 调用现有的 Process 方法
	return p.Process(rawData)
}

// New 创建Grafana告警处理器
func New() types.AlertProcessor {
	return &GrafanaAlertProcessor{}
}
