# WPS协作通知使用指南

本文档介绍如何使用WPS协作通知功能，包括阿里云告警处理和通用通知模板。

## 功能概述

### 1. 阿里云告警处理器
- **阿里云事件处理器** (`aliyun_event`): 处理阿里云事件告警，如DDoS攻击、实例异常等
- **阿里云指标处理器** (`aliyun_metrics`): 处理阿里云指标告警，如CPU使用率、内存使用率等

### 2. WPS协作通知渠道
- 支持富文本消息格式
- 自动根据告警严重程度设置颜色和图标
- 支持@用户功能
- 支持发送到指定聊天组

### 3. 通用通知模板
- 阿里云事件告警模板
- 阿里云指标告警模板  
- 通用告警模板

## 快速开始

### 1. 配置WPS协作参数

```go
// WPS协作配置
config := WPSConfig{
    AppID:   "your_app_id",
    AppKey:  "your_app_key", 
    OpenAPI: "https://openapi.wps.cn",
}
```

### 2. 处理阿里云事件告警

```go
// 创建阿里云事件处理器
processor, err := alertprocessor.NewAlertProcessor("aliyun_event")
if err != nil {
    log.Fatal(err)
}

// 处理原始告警数据
alertData, err := processor.Process(rawEventData)
if err != nil {
    log.Fatal(err)
}

// 输出处理结果
fmt.Printf("标题: %s\n", alertData.Title)
fmt.Printf("描述: %s\n", alertData.Description)
fmt.Printf("严重程度: %s\n", alertData.Severity)
```

### 3. 处理阿里云指标告警

```go
// 创建阿里云指标处理器
processor, err := alertprocessor.NewAlertProcessor("aliyun_merics")
if err != nil {
    log.Fatal(err)
}

// 处理URL编码的原始数据
alertData, err := processor.ProcessRawString(rawMetricsData)
if err != nil {
    log.Fatal(err)
}
```

### 4. 构建WPS通知消息

```go
// 创建WPS模板构建器
templateBuilder := template.NewWPSTemplateBuilder(appID)

// 构建通知数据
notificationData := &notifier.NotificationData{
    Title:      alertData.Title,
    Content:    alertData.Description,
    AlertID:    "alert-12345",
    Severity:   alertData.Severity,
    Recipients: []string{"<EMAIL>"},
    Labels:     alertData.Labels,
    ChannelData: map[string]interface{}{
        "wps": map[string]interface{}{
            "chat_ids": []int{41887203},
        },
    },
}

// 根据告警类型选择模板
var messageBody map[string]interface{}
switch alertType {
case "aliyun_event":
    messageBody = templateBuilder.BuildAliyunEventTemplate(notificationData)
case "aliyun_metrics":
    messageBody = templateBuilder.BuildAliyunMetricsTemplate(notificationData)
default:
    messageBody = templateBuilder.BuildGenericTemplate(notificationData)
}
```

### 5. 发送WPS通知

```go
// 创建WPS通知器
wpsNotifier := channel.NewWPSNotifier(appID, appKey, openAPI)

// 发送通知
result := wpsNotifier.Send(context.Background(), notificationData)
if !result.Success {
    log.Printf("发送失败: %v", result.Error)
} else {
    log.Printf("发送成功: %s", result.Message)
}
```

## 运行演示程序

### 1. 编译演示程序

```bash
cd cmd/wps_notification_demo
go build -o wps_demo main.go
```

### 2. 运行演示

```bash
# 使用默认配置运行
./wps_demo

# 或者设置环境变量
export WPS_APP_ID="your_app_id"
export WPS_APP_KEY="your_app_key"
export WPS_OPEN_API="https://openapi.wps.cn"
./wps_demo
```

### 3. 运行测试

```bash
# 运行WPS通知器测试
cd rpc/pkg/notifier/channel
go test -v -run TestWPSNotifier

# 运行特定测试
go test -v -run TestWPSNotifier_AliyunEventAlert
go test -v -run TestWPSNotifier_AliyunMetricsAlert
go test -v -run TestWPSNotifier_GenericAlert
```

## 消息格式示例

### 阿里云事件告警消息

```json
{
  "app_key": "AK20231216PDPGPI",
  "msg_type": 23,
  "content": {
    "type": 23,
    "content": {
      "header": {
        "template": "red",
        "title": {
          "tag": "text",
          "content": {
            "text": "🚨 [云服务器ECS] 实例遭受DDoS攻击中",
            "type": "plainText"
          }
        }
      },
      "elements": [
        {
          "tag": "hr",
          "style": "solid"
        },
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**📊 事件概览**\n\n- 告警等级：CRITICAL\n- 事件状态：Normal\n- 事件类型：Exception\n- 所属地域：华北2（北京）\n- 业务分组：默认应用分组\n",
                "type": "markdown"
              }
            }
          ]
        }
      ]
    }
  }
}
```

### 阿里云指标告警消息

```json
{
  "app_key": "AK20231216PDPGPI", 
  "msg_type": 23,
  "content": {
    "type": 23,
    "content": {
      "header": {
        "template": "yellow",
        "title": {
          "tag": "text",
          "content": {
            "text": "⚠️ [阿里云] CPU使用率过高",
            "type": "plainText"
          }
        }
      },
      "elements": [
        {
          "tag": "hr",
          "style": "solid"
        },
        {
          "tag": "div", 
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**📊 告警概览**\n\n- 指标名称：CPUUtilization\n- 当前值：95.6%\n- 触发条件：CPUUtilization >= 90\n- 告警等级：WARN\n- 告警状态：ALERT\n",
                "type": "markdown"
              }
            }
          ]
        }
      ]
    }
  }
}
```

## 配置说明

### WPS协作配置参数

| 参数 | 说明 | 示例 |
|------|------|------|
| AppID | WPS应用ID | AK20231216PDPGPI |
| AppKey | WPS应用密钥 | deff6800d41d3b296eb41ce4a01094ef |
| OpenAPI | WPS开放API地址 | https://openapi.wps.cn |

### 通知数据配置

| 参数 | 说明 | 类型 |
|------|------|------|
| Recipients | 接收人邮箱列表 | []string |
| ChatIDs | 聊天组ID列表 | []int |
| Severity | 告警严重程度 | critical/warning/info |
| Labels | 告警标签 | map[string]string |

## 注意事项

1. **Mock测试**: 当前实现主要用于演示和测试，不会实际发送WPS消息
2. **配置安全**: 请妥善保管WPS应用密钥，不要在代码中硬编码
3. **错误处理**: 在生产环境中请添加完善的错误处理和重试机制
4. **性能优化**: 对于大量通知，建议实现批量发送和限流机制

## 扩展开发

### 添加新的告警处理器

```go
// 实现AlertProcessor接口
type CustomAlertProcessor struct{}

func (p *CustomAlertProcessor) Process(rawData map[string]interface{}) (*types.AlertData, error) {
    // 自定义处理逻辑
    return &types.AlertData{
        Title:       "自定义告警",
        Description: "自定义告警描述", 
        Severity:    "warning",
        AlertKey:    "custom-alert",
        Labels:      make(map[string]string),
        RawData:     rawData,
    }, nil
}

func (p *CustomAlertProcessor) ProcessRawString(rawDataStr string) (*types.AlertData, error) {
    // 处理原始字符串数据
    return p.Process(map[string]interface{}{"raw": rawDataStr})
}

// 注册自定义处理器
alertprocessor.RegisterProcessor("custom", func() types.AlertProcessor {
    return &CustomAlertProcessor{}
})
```

### 添加新的通知模板

```go
// 扩展WPSTemplateBuilder
func (b *WPSTemplateBuilder) BuildCustomTemplate(data *notifier.NotificationData) map[string]interface{} {
    // 自定义模板逻辑
    return map[string]interface{}{
        "app_key":  b.appID,
        "msg_type": 23,
        "content": map[string]interface{}{
            // 自定义内容结构
        },
    }
}
```
