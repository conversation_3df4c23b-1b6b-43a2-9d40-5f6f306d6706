# WPS协作通知使用指南

本文档介绍如何使用WPS协作通知功能，包括阿里云告警处理和通用通知模板。

## 功能概述

### 1. 阿里云告警处理器
- **阿里云事件处理器** (`aliyun_event`): 处理阿里云事件告警，如DDoS攻击、实例异常等
- **阿里云指标处理器** (`aliyun_metrics`): 处理阿里云指标告警，如CPU使用率、内存使用率等

### 2. WPS协作通知渠道
- 支持富文本消息格式
- 自动根据告警严重程度设置颜色和图标
- 支持@用户功能
- 支持发送到指定聊天组

### 3. 通用通知模板
- 阿里云事件告警模板
- 阿里云指标告警模板  
- 通用告警模板

## 快速开始

### 1. 通过工厂模式使用通知处理器

```go
// 创建通知处理器
notificationProcessor := notification.NewProcessor(svcCtx)

// 准备告警数据
processedAlert := map[string]interface{}{
    "id":             "alert-001",
    "incident_id":    "incident-001",
    "title":          "系统告警",
    "description":    "告警描述",
    "alert_severity": "critical",
    "source":         "aliyun_event", // 或 "aliyun_metrics", "generic"
    "suppressed":     false,
    "labels":         map[string]string{...},
}

// 配置通知路由
routeSpace := map[string]interface{}{
    "notify_config": map[string]interface{}{
        "channels": []interface{}{"wps"},
        "users":    []interface{}{"<EMAIL>"},
        "wps_config": map[string]interface{}{
            "chat_ids": []interface{}{35589691},
        },
    },
}

// 执行通知处理
err := notificationProcessor.ProcessNotifications(context.Background(), processedAlert, routeSpace)
```

### 2. 配置WPS协作参数

```go
// WPS协作配置（在通知处理器中自动配置）
// 当前使用Mock配置进行测试：
// AppID:   "AK20231216PDPGPI"
// AppKey:  "deff6800d41d3b296eb41ce4a01094ef"
// OpenAPI: "https://openapi.wps.cn"
```

### 3. 完整的告警处理流程

```go
// 步骤1: 处理原始告警数据
alertProcessor, err := alertprocessor.NewAlertProcessor("aliyun_event")
if err != nil {
    log.Fatal(err)
}

alertData, err := alertProcessor.Process(rawEventData)
if err != nil {
    log.Fatal(err)
}

// 步骤2: 构建处理后的告警数据
processedAlert := map[string]interface{}{
    "id":             "alert-001",
    "incident_id":    "incident-001",
    "title":          alertData.Title,
    "description":    alertData.Description,
    "alert_severity": alertData.Severity,
    "source":         "aliyun_event",
    "suppressed":     false,
    "labels":         alertData.Labels,
}

// 步骤3: 执行通知处理
err = notificationProcessor.ProcessNotifications(context.Background(), processedAlert, routeSpace)
if err != nil {
    log.Printf("通知处理失败: %v", err)
}
```

### 4. 支持的告警源类型

- **aliyun_event**: 阿里云事件告警，自动使用阿里云事件模板
- **aliyun_metrics**: 阿里云指标告警，自动使用阿里云指标模板
- **generic**: 通用告警，使用通用模板

### 5. 通知渠道配置

```go
// 在routeSpace中配置通知渠道
routeSpace := map[string]interface{}{
    "notify_config": map[string]interface{}{
        "channels": []interface{}{"wps", "email", "dingtalk"}, // 支持多渠道
        "users":    []interface{}{"<EMAIL>", "<EMAIL>"},
        "wps_config": map[string]interface{}{
            "chat_ids": []interface{}{35589691, 12345678}, // 多个聊天组
        },
    },
}
```

## 运行演示程序

### 1. 编译通知演示程序

```bash
cd cmd/notification_demo
go build -o notification_demo main.go
```

### 2. 运行完整通知流程演示

```bash
# 运行完整的告警通知演示
./notification_demo
```

### 3. 编译WPS模板演示程序

```bash
cd cmd/wps_notification_demo
go build -o wps_demo main.go
```

### 4. 运行WPS模板演示

```bash
# 使用默认配置运行
./wps_demo

# 或者设置环境变量
export WPS_APP_ID="your_app_id"
export WPS_APP_KEY="your_app_key"
export WPS_OPEN_API="https://openapi.wps.cn"
./wps_demo
```

### 5. 运行测试

```bash
# 运行通知处理器测试
cd rpc/pkg/runprocessor/notification
go test -v -run TestProcessor

# 运行WPS通知器测试
cd rpc/pkg/notifier/channel
go test -v -run TestWPSNotifier

# 运行WPS模板测试
cd rpc/pkg/notifier/template
go test -v -run TestWPSTemplateRenderer
```

## 消息格式示例

### 阿里云事件告警消息

```json
{
  "app_key": "AK20231216PDPGPI",
  "msg_type": 23,
  "content": {
    "type": 23,
    "content": {
      "header": {
        "template": "red",
        "title": {
          "tag": "text",
          "content": {
            "text": "🚨 [云服务器ECS] 实例遭受DDoS攻击中",
            "type": "plainText"
          }
        }
      },
      "elements": [
        {
          "tag": "hr",
          "style": "solid"
        },
        {
          "tag": "div",
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**📊 事件概览**\n\n- 告警等级：CRITICAL\n- 事件状态：Normal\n- 事件类型：Exception\n- 所属地域：华北2（北京）\n- 业务分组：默认应用分组\n",
                "type": "markdown"
              }
            }
          ]
        }
      ]
    }
  }
}
```

### 阿里云指标告警消息

```json
{
  "app_key": "AK20231216PDPGPI", 
  "msg_type": 23,
  "content": {
    "type": 23,
    "content": {
      "header": {
        "template": "yellow",
        "title": {
          "tag": "text",
          "content": {
            "text": "⚠️ [阿里云] CPU使用率过高",
            "type": "plainText"
          }
        }
      },
      "elements": [
        {
          "tag": "hr",
          "style": "solid"
        },
        {
          "tag": "div", 
          "fields": [
            {
              "tag": "text",
              "content": {
                "text": "**📊 告警概览**\n\n- 指标名称：CPUUtilization\n- 当前值：95.6%\n- 触发条件：CPUUtilization >= 90\n- 告警等级：WARN\n- 告警状态：ALERT\n",
                "type": "markdown"
              }
            }
          ]
        }
      ]
    }
  }
}
```

## 配置说明

### WPS协作配置参数

| 参数 | 说明 | 示例 |
|------|------|------|
| AppID | WPS应用ID | AK20231216PDPGPI |
| AppKey | WPS应用密钥 | deff6800d41d3b296eb41ce4a01094ef |
| OpenAPI | WPS开放API地址 | https://openapi.wps.cn |

### 通知数据配置

| 参数 | 说明 | 类型 |
|------|------|------|
| Recipients | 接收人邮箱列表 | []string |
| ChatIDs | 聊天组ID列表 | []int |
| Severity | 告警严重程度 | critical/warning/info |
| Labels | 告警标签 | map[string]string |

## 注意事项

1. **Mock测试**: 当前实现主要用于演示和测试，不会实际发送WPS消息
2. **配置安全**: 请妥善保管WPS应用密钥，不要在代码中硬编码
3. **错误处理**: 在生产环境中请添加完善的错误处理和重试机制
4. **性能优化**: 对于大量通知，建议实现批量发送和限流机制

## 扩展开发

### 添加新的告警处理器

```go
// 实现AlertProcessor接口
type CustomAlertProcessor struct{}

func (p *CustomAlertProcessor) Process(rawData map[string]interface{}) (*types.AlertData, error) {
    // 自定义处理逻辑
    return &types.AlertData{
        Title:       "自定义告警",
        Description: "自定义告警描述", 
        Severity:    "warning",
        AlertKey:    "custom-alert",
        Labels:      make(map[string]string),
        RawData:     rawData,
    }, nil
}

func (p *CustomAlertProcessor) ProcessRawString(rawDataStr string) (*types.AlertData, error) {
    // 处理原始字符串数据
    return p.Process(map[string]interface{}{"raw": rawDataStr})
}

// 注册自定义处理器
alertprocessor.RegisterProcessor("custom", func() types.AlertProcessor {
    return &CustomAlertProcessor{}
})
```

### 添加新的通知模板

```go
// 扩展WPSTemplateBuilder
func (b *WPSTemplateBuilder) BuildCustomTemplate(data *notifier.NotificationData) map[string]interface{} {
    // 自定义模板逻辑
    return map[string]interface{}{
        "app_key":  b.appID,
        "msg_type": 23,
        "content": map[string]interface{}{
            // 自定义内容结构
        },
    }
}
```
