package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/logic/alertservice"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor"
)

func main() {
	fmt.Println("=== 告警创建测试 ===")
	
	// 创建服务上下文（简化版，用于测试）
	svcCtx := &svc.ServiceContext{}
	
	// 创建告警逻辑处理器
	alertLogic := alertservicelogic.NewCreateAlertLogic(context.Background(), svcCtx)
	
	// 测试1: 处理Grafana告警数据并创建标准告警
	fmt.Println("\n1. 测试Grafana告警处理和创建...")
	testGrafanaAlert(alertLogic)
	
	// 测试2: 处理阿里云事件告警数据并创建标准告警
	fmt.Println("\n2. 测试阿里云事件告警处理和创建...")
	testAliyunEventAlert(alertLogic)
	
	fmt.Println("\n=== 测试完成 ===")
}

// testGrafanaAlert 测试Grafana告警处理
func testGrafanaAlert(alertLogic *alertservicelogic.CreateAlertLogic) {
	// 模拟从日志中提取的Grafana告警数据
	rawGrafanaData := map[string]interface{}{
		"alert_severity": "Info",
		"annotations": map[string]interface{}{
			"message": "CPU使用率过高，当前值: 80%",
		},
		"endsAt":       "2023-04-10T11:46:38Z",
		"generatorURL": "http://localhost:9090/graph?g0.expr=up%7Bjob%3D%22prometheus%22%7D+%3D%3D+1&g0.tab=1",
		"id":           1,
		"labels": map[string]interface{}{
			"alertState":       "ALERT",
			"alertname":        "webhook测试(各通知字段均为示例)",
			"check":            "webhook测试(各通知字段均为示例)",
			"curValue":         "80",
			"dimensions":       "{userId=11111111111,instanceId=testInstanceId}",
			"expression":       "$Value > 75",
			"instanceId":       "testInstanceId",
			"lastTime":         "5 days",
			"metric":           "CPUUtilization",
			"metricName":       "(ECS)CPU使用率",
			"metricProject":    "acs_ecs_dashboard",
			"namespace":        "acs_ecs_dashboard",
			"preTriggerLevel":  "INFO",
			"rawMetricName":    "CPUUtilization",
			"regionId":         "cn-hangzhou",
			"regionName":       "cn-hangzhou",
			"resource":         "testInstanceId",
			"ruleId":           "testRuleId",
			"triggerLevel":     "INFO",
			"userId":           "testUserId",
		},
		"startsAt": "2023-04-10T11:46:38Z",
		"title":    "Webhook Test(All the fields in the notification are examples)",
	}
	
	// 使用告警处理器处理原始数据
	alertProcessor, err := alertprocessor.NewAlertProcessor("grafana")
	if err != nil {
		log.Printf("创建Grafana告警处理器失败: %v", err)
		return
	}
	
	alertData, err := alertProcessor.Process(rawGrafanaData)
	if err != nil {
		log.Printf("处理Grafana告警数据失败: %v", err)
		return
	}
	
	fmt.Printf("✅ Grafana告警数据处理完成:\n")
	fmt.Printf("   - 标题: %s\n", alertData.Title)
	fmt.Printf("   - 描述: %s\n", alertData.Description)
	fmt.Printf("   - 严重程度: %s\n", alertData.Severity)
	fmt.Printf("   - 告警键: %s\n", alertData.AlertKey)
	
	// 构建处理后的告警数据
	processedAlert := map[string]interface{}{
		"title":           alertData.Title,
		"description":     alertData.Description,
		"alert_severity":  alertData.Severity,
		"alert_key":       alertData.AlertKey,
		"integrations_id": "11221", // 模拟集成ID
		"labels":          alertData.Labels,
		"raw_data":        alertData.RawData,
	}
	
	// 打印处理后的告警数据
	processedJSON, _ := json.MarshalIndent(processedAlert, "", "  ")
	fmt.Printf("处理后的告警数据:\n%s\n", string(processedJSON))
	
	// 模拟创建标准告警（注释掉实际的数据库操作，因为没有数据库连接）
	fmt.Printf("✅ Grafana告警处理完成，准备创建标准告警\n")
	
	// 实际环境中会调用：
	// alertID, err := alertLogic.CreateStandardAlert(context.Background(), processedAlert, "raw-alert-id", "space-001")
	// if err != nil {
	//     log.Printf("创建标准告警失败: %v", err)
	//     return
	// }
	// fmt.Printf("✅ 标准告警创建成功，ID: %s\n", alertID)
}

// testAliyunEventAlert 测试阿里云事件告警处理
func testAliyunEventAlert(alertLogic *alertservicelogic.CreateAlertLogic) {
	// 模拟阿里云事件告警数据
	rawAliyunData := map[string]interface{}{
		"severity":     "CRITICAL",
		"strategyName": "数据库连接异常",
		"alert": map[string]interface{}{
			"product":     "RDS",
			"alertStatus": "ALERT",
			"eventType":   "Exception",
			"groupName":   "数据库监控组",
			"meta": map[string]interface{}{
				"sysEventMeta": map[string]interface{}{
					"serviceTypeZh": "云数据库RDS",
					"eventNameZh":   "数据库连接异常",
					"eventType":     "Exception",
					"regionNameZh":  "华东1（杭州）",
					"instanceName":  "prod-mysql-001",
				},
			},
			"eventContentMap": map[string]interface{}{
				"eventId":           "evt-rds-connection-error",
				"instanceId":        "rm-bp1234567890abcdef",
				"reason":            "数据库连接数超过最大限制",
				"reasonCode":        "ConnectionLimit.Exceeded",
				"publishTime":       "2024-01-15T21:00:00Z",
				"executeStartTime":  "2024-01-15T21:00:00Z",
				"eventStatus":       "Exception",
			},
		},
	}
	
	// 使用告警处理器处理原始数据
	alertProcessor, err := alertprocessor.NewAlertProcessor("aliyun_event")
	if err != nil {
		log.Printf("创建阿里云事件告警处理器失败: %v", err)
		return
	}
	
	alertData, err := alertProcessor.Process(rawAliyunData)
	if err != nil {
		log.Printf("处理阿里云事件告警数据失败: %v", err)
		return
	}
	
	fmt.Printf("✅ 阿里云事件告警数据处理完成:\n")
	fmt.Printf("   - 标题: %s\n", alertData.Title)
	fmt.Printf("   - 描述: %s\n", alertData.Description)
	fmt.Printf("   - 严重程度: %s\n", alertData.Severity)
	fmt.Printf("   - 告警键: %s\n", alertData.AlertKey)
	
	// 构建处理后的告警数据
	processedAlert := map[string]interface{}{
		"title":           alertData.Title,
		"description":     alertData.Description,
		"alert_severity":  alertData.Severity,
		"alert_key":       alertData.AlertKey,
		"integrations_id": "11222", // 模拟集成ID
		"labels":          alertData.Labels,
		"raw_data":        alertData.RawData,
	}
	
	// 打印处理后的告警数据
	processedJSON, _ := json.MarshalIndent(processedAlert, "", "  ")
	fmt.Printf("处理后的告警数据:\n%s\n", string(processedJSON))
	
	// 模拟创建标准告警
	fmt.Printf("✅ 阿里云事件告警处理完成，准备创建标准告警\n")
	
	// 实际环境中会调用：
	// alertID, err := alertLogic.CreateStandardAlert(context.Background(), processedAlert, "raw-alert-id", "space-002")
	// if err != nil {
	//     log.Printf("创建标准告警失败: %v", err)
	//     return
	// }
	// fmt.Printf("✅ 标准告警创建成功，ID: %s\n", alertID)
}
