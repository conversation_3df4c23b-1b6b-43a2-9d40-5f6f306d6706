package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier/channel"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier/template"
)

// DemoConfig 演示配置
type DemoConfig struct {
	WPS struct {
		AppID   string `json:"app_id"`
		AppKey  string `json:"app_key"`
		OpenAPI string `json:"open_api"`
	} `json:"wps"`
	Recipients []string `json:"recipients"`
	ChatIDs    []int    `json:"chat_ids"`
}

func main() {
	fmt.Println("=== WPS协作通知演示程序 ===")
	
	// 加载配置
	config := loadDemoConfig()
	
	// 演示阿里云事件告警处理和通知
	fmt.Println("\n1. 演示阿里云事件告警处理...")
	demoAliyunEventAlert(config)
	
	// 演示阿里云指标告警处理和通知
	fmt.Println("\n2. 演示阿里云指标告警处理...")
	demoAliyunMetricsAlert(config)
	
	// 演示通用告警通知
	fmt.Println("\n3. 演示通用告警通知...")
	demoGenericAlert(config)
	
	fmt.Println("\n=== 演示完成 ===")
}

// loadDemoConfig 加载演示配置
func loadDemoConfig() DemoConfig {
	// 默认配置（Mock数据）
	config := DemoConfig{
		Recipients: []string{
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
		},
		ChatIDs: []int{41887203},
	}
	
	config.WPS.AppID = "AK20231216PDPGPI"
	config.WPS.AppKey = "deff6800d41d3b296eb41ce4a01094ef"
	config.WPS.OpenAPI = "https://openapi.wps.cn"
	
	// 尝试从环境变量读取配置
	if appID := os.Getenv("WPS_APP_ID"); appID != "" {
		config.WPS.AppID = appID
	}
	if appKey := os.Getenv("WPS_APP_KEY"); appKey != "" {
		config.WPS.AppKey = appKey
	}
	if openAPI := os.Getenv("WPS_OPEN_API"); openAPI != "" {
		config.WPS.OpenAPI = openAPI
	}
	
	return config
}

// demoAliyunEventAlert 演示阿里云事件告警
func demoAliyunEventAlert(config DemoConfig) {
	// 模拟阿里云事件告警原始数据
	rawEventData := map[string]interface{}{
		"severity":      "CRITICAL",
		"strategyName":  "实例遭受DDoS攻击中",
		"alert": map[string]interface{}{
			"product":     "ECS",
			"alertStatus": "ALERT",
			"eventType":   "Exception",
			"groupName":   "默认应用分组",
			"meta": map[string]interface{}{
				"sysEventMeta": map[string]interface{}{
					"serviceTypeZh": "云服务器ECS",
					"eventNameZh":   "实例遭受DDoS攻击中",
					"eventType":     "Exception",
					"regionNameZh":  "华北2（北京）",
					"instanceName":  "test-server-001",
				},
			},
			"eventContentMap": map[string]interface{}{
				"eventId":           "evt-bp1234567890abcdef",
				"instanceId":        "i-bp1234567890abcdef",
				"reason":            "实例正在遭受DDoS攻击",
				"reasonCode":        "DDoS.Attack.Detected",
				"eipAddress":        "47.93.123.456",
				"privateIpAddress":  []interface{"************"},
				"publishTime":       "2024-01-15T10:30:00Z",
				"executeStartTime":  "2024-01-15T10:30:00Z",
				"eventStatus":       "Normal",
			},
		},
	}
	
	// 使用阿里云事件处理器处理数据
	processor, err := alertprocessor.NewAlertProcessor("aliyun_event")
	if err != nil {
		log.Printf("创建阿里云事件处理器失败: %v", err)
		return
	}
	
	alertData, err := processor.Process(rawEventData)
	if err != nil {
		log.Printf("处理阿里云事件数据失败: %v", err)
		return
	}
	
	fmt.Printf("处理后的告警数据:\n")
	fmt.Printf("- 标题: %s\n", alertData.Title)
	fmt.Printf("- 描述: %s\n", alertData.Description)
	fmt.Printf("- 严重程度: %s\n", alertData.Severity)
	fmt.Printf("- 告警键: %s\n", alertData.AlertKey)
	fmt.Printf("- 标签数量: %d\n", len(alertData.Labels))
	
	// 构建WPS通知
	buildAndShowWPSNotification(config, alertData, "aliyun_event")
}

// demoAliyunMetricsAlert 演示阿里云指标告警
func demoAliyunMetricsAlert(config DemoConfig) {
	// 模拟阿里云指标告警原始数据（URL编码格式）
	rawMetricsData := "alertName=CPU%E4%BD%BF%E7%94%A8%E7%8E%87%E8%BF%87%E9%AB%98&metricName=CPUUtilization&curValue=95.6&unit=%25&triggerLevel=WARN&alertState=ALERT&expression=CPUUtilization%20%3E%3D%2090&instanceName=web-server-prod-001&regionName=cn-beijing&timestamp=1705315800000&ruleId=rule-12345&dimensions=%7BinstanceId%3Di-bp1234567890abcdef%2CuserId%3D123456789%7D"
	
	// 使用阿里云指标处理器处理数据
	processor, err := alertprocessor.NewAlertProcessor("aliyun_merics")
	if err != nil {
		log.Printf("创建阿里云指标处理器失败: %v", err)
		return
	}
	
	alertData, err := processor.ProcessRawString(rawMetricsData)
	if err != nil {
		log.Printf("处理阿里云指标数据失败: %v", err)
		return
	}
	
	fmt.Printf("处理后的告警数据:\n")
	fmt.Printf("- 标题: %s\n", alertData.Title)
	fmt.Printf("- 描述: %s\n", alertData.Description)
	fmt.Printf("- 严重程度: %s\n", alertData.Severity)
	fmt.Printf("- 告警键: %s\n", alertData.AlertKey)
	fmt.Printf("- 标签数量: %d\n", len(alertData.Labels))
	
	// 构建WPS通知
	buildAndShowWPSNotification(config, alertData, "aliyun_metrics")
}

// demoGenericAlert 演示通用告警
func demoGenericAlert(config DemoConfig) {
	// 创建通用告警数据
	alertData := &alertprocessor.AlertData{
		Title:       "系统告警",
		Description: "系统出现异常，请及时处理",
		Severity:    "critical",
		AlertKey:    "generic-alert-001",
		Labels: map[string]string{
			"service":     "web-server",
			"environment": "production",
			"region":      "beijing",
			"team":        "ops",
			"cpu_usage":   "95%",
			"memory_usage": "88%",
		},
		RawData: map[string]interface{}{
			"source":    "monitoring-system",
			"timestamp": "2024-01-15T18:30:00Z",
			"details":   "CPU和内存使用率过高",
		},
	}
	
	fmt.Printf("通用告警数据:\n")
	fmt.Printf("- 标题: %s\n", alertData.Title)
	fmt.Printf("- 描述: %s\n", alertData.Description)
	fmt.Printf("- 严重程度: %s\n", alertData.Severity)
	fmt.Printf("- 告警键: %s\n", alertData.AlertKey)
	fmt.Printf("- 标签数量: %d\n", len(alertData.Labels))
	
	// 构建WPS通知
	buildAndShowWPSNotification(config, alertData, "generic")
}

// buildAndShowWPSNotification 构建并显示WPS通知
func buildAndShowWPSNotification(config DemoConfig, alertData *alertprocessor.AlertData, alertType string) {
	// 创建WPS模板构建器
	templateBuilder := template.NewWPSTemplateBuilder(config.WPS.AppID)
	
	// 构建通知数据
	notificationData := &notifier.NotificationData{
		Title:      alertData.Title,
		Content:    alertData.Description,
		AlertID:    "alert-" + alertData.AlertKey,
		IncidentID: "incident-" + alertData.AlertKey,
		Severity:   alertData.Severity,
		Recipients: config.Recipients,
		Labels:     alertData.Labels,
		AlertData:  alertData.RawData,
		ChannelData: map[string]interface{}{
			"wps": map[string]interface{}{
				"chat_ids": config.ChatIDs,
			},
		},
	}
	
	// 根据告警类型选择模板
	var messageBody map[string]interface{}
	switch alertType {
	case "aliyun_event":
		messageBody = templateBuilder.BuildAliyunEventTemplate(notificationData)
	case "aliyun_metrics":
		messageBody = templateBuilder.BuildAliyunMetricsTemplate(notificationData)
	default:
		messageBody = templateBuilder.BuildGenericTemplate(notificationData)
	}
	
	// 格式化输出消息体
	messageJSON, err := json.MarshalIndent(messageBody, "", "  ")
	if err != nil {
		log.Printf("序列化消息体失败: %v", err)
		return
	}
	
	fmt.Printf("WPS协作通知消息体:\n%s\n", string(messageJSON))
	
	// 创建WPS通知器（仅用于演示，不实际发送）
	wpsNotifier := channel.NewWPSNotifier(config.WPS.AppID, config.WPS.AppKey, config.WPS.OpenAPI)
	fmt.Printf("通知器类型: %s\n", wpsNotifier.Type())
	
	// 注意：在实际环境中，可以调用以下代码发送通知
	// result := wpsNotifier.Send(context.Background(), notificationData)
	// fmt.Printf("发送结果: %+v\n", result)
	
	fmt.Printf("消息构建完成，包含 %d 个元素\n", 
		len(messageBody["content"].(map[string]interface{})["content"].(map[string]interface{})["elements"].([]map[string]interface{})))
}
