package main

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier/channel"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier/template"
)

func main() {
	fmt.Println("=== WPS协作群消息发送测试 ===")

	// 创建WPS通知器
	wpsNotifier := channel.NewWPSNotifier(
		"AK20231216PDPGPI",                 // Mock AppID
		"deff6800d41d3b296eb41ce4a01094ef", // Mock AppKey
		"https://openapi.wps.cn",           // WPS OpenAPI地址
	)

	// 创建WPS消息构建器
	wpsBuilder := template.NewWPSMessageBuilder("AK20231216PDPGPI")

	// 测试1: 发送阿里云事件告警到群
	fmt.Println("\n1. 测试阿里云事件告警群消息...")
	testAliyunEventChatMessage(wpsNotifier, wpsBuilder)

	// 测试2: 发送阿里云指标告警到群
	fmt.Println("\n2. 测试阿里云指标告警群消息...")
	testAliyunMetricsChatMessage(wpsNotifier, wpsBuilder)

	// 测试3: 发送通用告警到群
	fmt.Println("\n3. 测试通用告警群消息...")
	testGenericChatMessage(wpsNotifier, wpsBuilder)

	fmt.Println("\n=== 测试完成 ===")
}

// testAliyunEventChatMessage 测试阿里云事件告警群消息
func testAliyunEventChatMessage(wpsNotifier notifier.Notifier, wpsBuilder *template.WPSMessageBuilder) {
	// 构建通知数据
	notificationData := &notifier.NotificationData{
		Title:    "实例遭受DDoS攻击中",
		Content:  "阿里云ECS实例正在遭受DDoS攻击，请立即处理",
		Severity: "critical",
		AlertID:  "alert-event-chat-001",
		Recipients: []string{
			"<EMAIL>",
		},
		Labels: map[string]string{
			"severity":           "CRITICAL",
			"service_type_zh":    "云服务器ECS",
			"event_status":       "Normal",
			"event_type":         "Exception",
			"region_name_zh":     "华北2（北京）",
			"group_name":         "默认应用分组",
			"instance_name":      "prod-web-server-001",
			"instance_id":        "i-bp1234567890abcdef",
			"private_ip_address": "************",
			"eip_address":        "47.93.123.456",
			"event_id":           "evt-bp1234567890abcdef",
			"reason":             "实例正在遭受DDoS攻击",
			"reason_code":        "DDoS.Attack.Detected",
			"publish_time":       "2024-01-15T10:30:00Z",
			"execute_start_time": "2024-01-15T10:30:00Z",
		},
		ChannelData: map[string]interface{}{
			"wps": map[string]interface{}{
				"chat_ids": []int{35589691}, // 直接设置聊天组ID
			},
		},
	}

	// 构建消息体
	messageBody := wpsBuilder.BuildAliyunEventMessage(notificationData)

	// 将消息体添加到渠道数据中
	wpsData := notificationData.ChannelData["wps"].(map[string]interface{})
	wpsData["message_body"] = messageBody

	// 打印消息体（用于调试）
	messageJSON, _ := json.MarshalIndent(messageBody, "", "  ")
	fmt.Printf("阿里云事件告警消息体:\n%s\n", string(messageJSON))

	// 发送通知（Mock模式）
	result := wpsNotifier.Send(context.Background(), notificationData)
	if result.Success {
		fmt.Printf("✅ 阿里云事件告警群消息发送成功: %s\n", result.Message)
	} else {
		fmt.Printf("❌ 阿里云事件告警群消息发送失败: %s\n", result.Message)
	}
}

// testAliyunMetricsChatMessage 测试阿里云指标告警群消息
func testAliyunMetricsChatMessage(wpsNotifier notifier.Notifier, wpsBuilder *template.WPSMessageBuilder) {
	// 构建通知数据
	notificationData := &notifier.NotificationData{
		Title:    "CPU使用率过高",
		Content:  "ECS实例CPU使用率持续超过90%",
		Severity: "warning",
		AlertID:  "alert-metrics-chat-001",
		Recipients: []string{
			"<EMAIL>",
		},
		Labels: map[string]string{
			"metric_name":   "CPUUtilization",
			"current_value": "95.6",
			"unit":          "%",
			"trigger_level": "WARN",
			"alert_state":   "ALERT",
			"expression":    "CPUUtilization >= 90",
			"instance_name": "web-server-prod-001",
			"region_name":   "cn-beijing",
			"instance_id":   "i-bp1234567890abcdef",
			"user_id":       "123456789",
			"alert_time":    "2024-01-15 18:30:00",
		},
		ChannelData: map[string]interface{}{
			"wps": map[string]interface{}{
				"chat_ids": []int{35589691}, // 直接设置聊天组ID
			},
		},
	}

	// 构建消息体
	messageBody := wpsBuilder.BuildAliyunMetricsMessage(notificationData)

	// 将消息体添加到渠道数据中
	wpsData := notificationData.ChannelData["wps"].(map[string]interface{})
	wpsData["message_body"] = messageBody

	// 打印消息体（用于调试）
	messageJSON, _ := json.MarshalIndent(messageBody, "", "  ")
	fmt.Printf("阿里云指标告警消息体:\n%s\n", string(messageJSON))

	// 发送通知（Mock模式）
	result := wpsNotifier.Send(context.Background(), notificationData)
	if result.Success {
		fmt.Printf("✅ 阿里云指标告警群消息发送成功: %s\n", result.Message)
	} else {
		fmt.Printf("❌ 阿里云指标告警群消息发送失败: %s\n", result.Message)
	}
}

// testGenericChatMessage 测试通用告警群消息
func testGenericChatMessage(wpsNotifier notifier.Notifier, wpsBuilder *template.WPSMessageBuilder) {
	// 构建通知数据
	notificationData := &notifier.NotificationData{
		Title:    "应用服务异常",
		Content:  "Web应用响应时间过长，部分用户访问受影响",
		Severity: "warning",
		AlertID:  "alert-generic-chat-001",
		Recipients: []string{
			"<EMAIL>",
		},
		Labels: map[string]string{
			"service":        "web-app",
			"environment":    "production",
			"region":         "beijing",
			"team":           "backend",
			"response_time":  "5.2s",
			"error_rate":     "12%",
			"affected_users": "156",
		},
		ChannelData: map[string]interface{}{
			"wps": map[string]interface{}{
				"chat_ids": []int{35589691}, // 直接设置聊天组ID
			},
		},
	}

	// 构建消息体
	messageBody := wpsBuilder.BuildGenericMessage(notificationData)

	// 将消息体添加到渠道数据中
	wpsData := notificationData.ChannelData["wps"].(map[string]interface{})
	wpsData["message_body"] = messageBody

	// 打印消息体（用于调试）
	messageJSON, _ := json.MarshalIndent(messageBody, "", "  ")
	fmt.Printf("通用告警消息体:\n%s\n", string(messageJSON))

	// 发送通知（Mock模式）
	result := wpsNotifier.Send(context.Background(), notificationData)
	if result.Success {
		fmt.Printf("✅ 通用告警群消息发送成功: %s\n", result.Message)
	} else {
		fmt.Printf("❌ 通用告警群消息发送失败: %s\n", result.Message)
	}
}
