package main

import (
	"encoding/json"
	"fmt"
	"log"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor"
)

func main() {
	fmt.Println("=== 阿里云事件告警处理器测试 ===")
	
	// 测试新的阿里云事件告警数据格式
	testNewAliyunEventFormat()
	
	fmt.Println("\n=== 测试完成 ===")
}

// testNewAliyunEventFormat 测试新的阿里云事件告警格式
func testNewAliyunEventFormat() {
	fmt.Println("\n测试新的阿里云事件告警格式...")
	
	// 新的阿里云事件告警数据
	rawAliyunEventData := map[string]interface{}{
		"severity": "CRITICAL",
		"userInfo": map[string]interface{}{
			"aliyunId":  "sdsdsd_sdsadsa_sdsad",
			"userId":    "************",
			"userName":  "susnsjwns.com",
		},
		"strategyName":     "业务运维",
		"relatedAlertIds": []interface{}{"a231c563-b348-447f-88d5-f7ca67f0ad4e"},
		"subscription": map[string]interface{}{
			"subscriptionUuid": "6695cd96b8224c36a3bfad5bbde217cb",
			"conditions": []interface{}{
				map[string]interface{}{
					"op":    "EQ",
					"field": "source",
					"value": "SYS_EVENT",
				},
				map[string]interface{}{
					"op":    "IN",
					"field": "severity",
					"value": "CRITICAL,WARNING",
				},
			},
		},
		"alert": map[string]interface{}{
			"alertStatus": "TRIGGERED",
			"severity":    "CRITICAL",
			"product":     "ECS",
			"groupName":   "解限机业务运维",
			"eventName":   "Instance:Security.DDoSDefense:Executing",
			"eventContentMap": map[string]interface{}{
				"eventId":           "e-t4nswrh4thrmh47",
				"eipAddress":        "*******",
				"eventStatus":       "Executing",
				"eventType":         "Security.DDoSDefense",
				"privateIpAddress":  []interface{}{"***********"},
				"regionName":        "ap-southeast-1",
			},
			"meta": map[string]interface{}{
				"sysEventMeta": map[string]interface{}{
					"regionNameEn": "ap-southeast-1",
					"regionNameZh": "新加坡",
					"eventNameZh":  "实例遭受DDoS攻击中",
				},
			},
		},
	}
	
	// 创建阿里云事件处理器
	alertProcessor, err := alertprocessor.NewAlertProcessor("aliyun_event")
	if err != nil {
		log.Fatalf("创建阿里云事件处理器失败: %v", err)
	}
	
	// 处理告警数据
	alertData, err := alertProcessor.Process(rawAliyunEventData)
	if err != nil {
		log.Fatalf("处理阿里云事件告警数据失败: %v", err)
	}
	
	// 打印处理结果
	fmt.Printf("✅ 阿里云事件告警处理完成:\n")
	fmt.Printf("   - 标题: %s\n", alertData.Title)
	fmt.Printf("   - 描述: %s\n", alertData.Description)
	fmt.Printf("   - 严重程度: %s\n", alertData.Severity)
	fmt.Printf("   - 告警键: %s\n", alertData.AlertKey)
	fmt.Printf("   - 标签数量: %d\n", len(alertData.Labels))
	
	// 打印重要标签
	fmt.Printf("\n📋 重要标签信息:\n")
	importantLabels := []string{
		"event_name_zh", "product", "alert_status", "event_type", "event_status",
		"region_name_zh", "region_name_en", "event_id", "eip_address", "private_ip_address",
		"user_id", "user_name", "subscription_uuid", "group_name",
	}
	
	for _, key := range importantLabels {
		if value, exists := alertData.Labels[key]; exists {
			fmt.Printf("   - %s: %s\n", key, value)
		}
	}
	
	// 打印所有标签（用于调试）
	fmt.Printf("\n🏷️ 所有标签:\n")
	labelsJSON, _ := json.MarshalIndent(alertData.Labels, "", "  ")
	fmt.Printf("%s\n", string(labelsJSON))
	
	// 打印原始数据（用于调试）
	fmt.Printf("\n📄 原始数据:\n")
	rawDataJSON, _ := json.MarshalIndent(alertData.RawData, "", "  ")
	fmt.Printf("%s\n", string(rawDataJSON))
	
	// 验证关键字段
	fmt.Printf("\n✅ 验证结果:\n")
	
	// 验证标题
	if alertData.Title == "实例遭受DDoS攻击中" {
		fmt.Printf("   ✅ 标题正确: %s\n", alertData.Title)
	} else {
		fmt.Printf("   ❌ 标题不正确: 期望 '实例遭受DDoS攻击中', 实际 '%s'\n", alertData.Title)
	}
	
	// 验证严重程度
	if alertData.Severity == "critical" {
		fmt.Printf("   ✅ 严重程度正确: %s\n", alertData.Severity)
	} else {
		fmt.Printf("   ❌ 严重程度不正确: 期望 'critical', 实际 '%s'\n", alertData.Severity)
	}
	
	// 验证告警键
	if alertData.AlertKey == "aliyun-event-e-t4nswrh4thrmh47" {
		fmt.Printf("   ✅ 告警键正确: %s\n", alertData.AlertKey)
	} else {
		fmt.Printf("   ❌ 告警键不正确: 期望 'aliyun-event-e-t4nswrh4thrmh47', 实际 '%s'\n", alertData.AlertKey)
	}
	
	// 验证重要标签
	expectedLabels := map[string]string{
		"product":         "ECS",
		"event_type":      "Security.DDoSDefense",
		"event_status":    "Executing",
		"region_name_zh":  "新加坡",
		"region_name_en":  "ap-southeast-1",
		"eip_address":     "*******",
		"private_ip_address": "***********",
		"user_id":         "************",
	}
	
	for key, expectedValue := range expectedLabels {
		if actualValue, exists := alertData.Labels[key]; exists {
			if actualValue == expectedValue {
				fmt.Printf("   ✅ 标签 %s 正确: %s\n", key, actualValue)
			} else {
				fmt.Printf("   ❌ 标签 %s 不正确: 期望 '%s', 实际 '%s'\n", key, expectedValue, actualValue)
			}
		} else {
			fmt.Printf("   ❌ 缺少标签: %s\n", key)
		}
	}
	
	fmt.Printf("\n🎉 阿里云事件告警处理器测试完成！\n")
}
