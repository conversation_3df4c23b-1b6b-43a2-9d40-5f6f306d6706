package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/runprocessor/notification"
)

func main() {
	fmt.Println("=== 告警通知处理演示程序 ===")
	
	// 创建服务上下文（简化版）
	svcCtx := &svc.ServiceContext{}
	
	// 创建通知处理器
	notificationProcessor := notification.NewProcessor(svcCtx)
	
	// 演示1: 阿里云事件告警处理和通知
	fmt.Println("\n1. 演示阿里云事件告警处理和通知...")
	demoAliyunEventNotification(notificationProcessor)
	
	// 演示2: 阿里云指标告警处理和通知
	fmt.Println("\n2. 演示阿里云指标告警处理和通知...")
	demoAliyunMetricsNotification(notificationProcessor)
	
	// 演示3: 通用告警通知
	fmt.Println("\n3. 演示通用告警通知...")
	demoGenericNotification(notificationProcessor)
	
	// 演示4: 完整的告警处理流程（从原始数据到通知）
	fmt.Println("\n4. 演示完整的告警处理流程...")
	demoCompleteAlertFlow(notificationProcessor)
	
	fmt.Println("\n=== 演示完成 ===")
}

// demoAliyunEventNotification 演示阿里云事件告警通知
func demoAliyunEventNotification(processor *notification.Processor) {
	// 模拟已处理的阿里云事件告警数据
	processedAlert := map[string]interface{}{
		"id":             "alert-aliyun-event-demo",
		"incident_id":    "incident-aliyun-event-demo",
		"title":          "实例遭受DDoS攻击中",
		"description":    "阿里云ECS实例正在遭受DDoS攻击，请立即处理",
		"alert_severity": "critical",
		"source":         "aliyun_event",
		"suppressed":     false,
		"created_at":     "2024-01-15T10:30:00Z",
		"labels": map[string]string{
			"severity":           "CRITICAL",
			"service_type_zh":    "云服务器ECS",
			"event_status":       "Normal",
			"event_type":         "Exception",
			"region_name_zh":     "华北2（北京）",
			"group_name":         "默认应用分组",
			"instance_name":      "prod-web-server-001",
			"instance_id":        "i-bp1234567890abcdef",
			"private_ip_address": "************",
			"eip_address":        "47.93.123.456",
			"event_id":           "evt-bp1234567890abcdef",
			"reason":             "实例正在遭受DDoS攻击",
			"reason_code":        "DDoS.Attack.Detected",
			"publish_time":       "2024-01-15T10:30:00Z",
			"execute_start_time": "2024-01-15T10:30:00Z",
		},
	}
	
	// 模拟路由空间配置
	routeSpace := map[string]interface{}{
		"id":   "route-space-001",
		"name": "生产环境告警路由",
		"notify_config": map[string]interface{}{
			"template_id": "template-aliyun-event-001",
			"channels":    []interface{}{"wps"},
			"users": []interface{}{
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
			},
			"wps_config": map[string]interface{}{
				"chat_ids": []interface{}{35589691}, // 生产环境告警群
			},
		},
	}
	
	// 执行通知处理
	err := processor.ProcessNotifications(context.Background(), processedAlert, routeSpace)
	if err != nil {
		log.Printf("阿里云事件告警通知处理失败: %v", err)
	} else {
		fmt.Println("✅ 阿里云事件告警通知处理成功")
	}
}

// demoAliyunMetricsNotification 演示阿里云指标告警通知
func demoAliyunMetricsNotification(processor *notification.Processor) {
	// 模拟已处理的阿里云指标告警数据
	processedAlert := map[string]interface{}{
		"id":             "alert-aliyun-metrics-demo",
		"incident_id":    "incident-aliyun-metrics-demo",
		"title":          "CPU使用率过高",
		"description":    "ECS实例CPU使用率持续超过90%，可能影响服务性能",
		"alert_severity": "warning",
		"source":         "aliyun_metrics",
		"suppressed":     false,
		"created_at":     "2024-01-15T18:30:00Z",
		"labels": map[string]string{
			"metric_name":    "CPUUtilization",
			"current_value":  "95.6",
			"unit":           "%",
			"trigger_level":  "WARN",
			"alert_state":    "ALERT",
			"expression":     "CPUUtilization >= 90",
			"instance_name":  "web-server-prod-001",
			"region_name":    "cn-beijing",
			"instance_id":    "i-bp1234567890abcdef",
			"user_id":        "123456789",
			"alert_time":     "2024-01-15 18:30:00",
			"rule_id":        "rule-cpu-high-001",
		},
	}
	
	// 模拟路由空间配置
	routeSpace := map[string]interface{}{
		"id":   "route-space-002",
		"name": "性能监控告警路由",
		"notify_config": map[string]interface{}{
			"template_id": "template-aliyun-metrics-001",
			"channels":    []interface{}{"wps"},
			"users": []interface{}{
				"<EMAIL>",
				"<EMAIL>",
			},
			"wps_config": map[string]interface{}{
				"chat_ids": []interface{}{35589691}, // 运维监控群
			},
		},
	}
	
	// 执行通知处理
	err := processor.ProcessNotifications(context.Background(), processedAlert, routeSpace)
	if err != nil {
		log.Printf("阿里云指标告警通知处理失败: %v", err)
	} else {
		fmt.Println("✅ 阿里云指标告警通知处理成功")
	}
}

// demoGenericNotification 演示通用告警通知
func demoGenericNotification(processor *notification.Processor) {
	// 模拟通用告警数据
	processedAlert := map[string]interface{}{
		"id":             "alert-generic-demo",
		"incident_id":    "incident-generic-demo",
		"title":          "应用服务异常",
		"description":    "Web应用响应时间过长，部分用户访问受影响",
		"alert_severity": "warning",
		"source":         "application-monitor",
		"suppressed":     false,
		"created_at":     "2024-01-15T20:15:00Z",
		"labels": map[string]string{
			"service":        "web-app",
			"environment":    "production",
			"region":         "beijing",
			"team":           "backend",
			"response_time":  "5.2s",
			"error_rate":     "12%",
			"affected_users": "156",
		},
	}
	
	// 模拟路由空间配置（使用默认WPS渠道）
	routeSpace := map[string]interface{}{
		"id":   "route-space-003",
		"name": "应用监控告警路由",
		"notify_config": map[string]interface{}{
			"template_id": "template-generic-001",
			"users": []interface{}{
				"<EMAIL>",
				"<EMAIL>",
			},
			// 没有指定channels，将使用默认的WPS渠道
		},
	}
	
	// 执行通知处理
	err := processor.ProcessNotifications(context.Background(), processedAlert, routeSpace)
	if err != nil {
		log.Printf("通用告警通知处理失败: %v", err)
	} else {
		fmt.Println("✅ 通用告警通知处理成功")
	}
}

// demoCompleteAlertFlow 演示完整的告警处理流程
func demoCompleteAlertFlow(notificationProcessor *notification.Processor) {
	fmt.Println("开始完整的告警处理流程演示...")
	
	// 步骤1: 模拟原始阿里云事件告警数据
	rawAlertData := map[string]interface{}{
		"severity":      "CRITICAL",
		"strategyName":  "数据库连接异常",
		"alert": map[string]interface{}{
			"product":     "RDS",
			"alertStatus": "ALERT",
			"eventType":   "Exception",
			"groupName":   "数据库监控组",
			"meta": map[string]interface{}{
				"sysEventMeta": map[string]interface{}{
					"serviceTypeZh": "云数据库RDS",
					"eventNameZh":   "数据库连接异常",
					"eventType":     "Exception",
					"regionNameZh":  "华东1（杭州）",
					"instanceName":  "prod-mysql-001",
				},
			},
			"eventContentMap": map[string]interface{}{
				"eventId":           "evt-rds-connection-error",
				"instanceId":        "rm-bp1234567890abcdef",
				"reason":            "数据库连接数超过最大限制",
				"reasonCode":        "ConnectionLimit.Exceeded",
				"publishTime":       "2024-01-15T21:00:00Z",
				"executeStartTime":  "2024-01-15T21:00:00Z",
				"eventStatus":       "Exception",
			},
		},
	}
	
	// 步骤2: 使用告警处理器处理原始数据
	fmt.Println("步骤1: 处理原始告警数据...")
	alertProcessor, err := alertprocessor.NewAlertProcessor("aliyun_event")
	if err != nil {
		log.Printf("创建告警处理器失败: %v", err)
		return
	}
	
	alertData, err := alertProcessor.Process(rawAlertData)
	if err != nil {
		log.Printf("处理原始告警数据失败: %v", err)
		return
	}
	
	fmt.Printf("✅ 告警数据处理完成:\n")
	fmt.Printf("   - 标题: %s\n", alertData.Title)
	fmt.Printf("   - 描述: %s\n", alertData.Description)
	fmt.Printf("   - 严重程度: %s\n", alertData.Severity)
	fmt.Printf("   - 告警键: %s\n", alertData.AlertKey)
	fmt.Printf("   - 标签数量: %d\n", len(alertData.Labels))
	
	// 步骤3: 构建处理后的告警数据
	fmt.Println("\n步骤2: 构建处理后的告警数据...")
	processedAlert := map[string]interface{}{
		"id":             "alert-complete-flow-demo",
		"incident_id":    "incident-complete-flow-demo",
		"title":          alertData.Title,
		"description":    alertData.Description,
		"alert_severity": alertData.Severity,
		"source":         "aliyun_event",
		"suppressed":     false,
		"created_at":     "2024-01-15T21:00:00Z",
		"labels":         alertData.Labels,
		"raw_data":       alertData.RawData,
	}
	
	// 步骤4: 配置路由空间
	fmt.Println("步骤3: 配置通知路由...")
	routeSpace := map[string]interface{}{
		"id":   "route-space-complete-demo",
		"name": "完整流程演示路由",
		"notify_config": map[string]interface{}{
			"template_id": "template-complete-demo",
			"channels":    []interface{}{"wps"},
			"users": []interface{}{
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
			},
			"wps_config": map[string]interface{}{
				"chat_ids": []interface{}{35589691}, // 紧急告警群
			},
		},
	}
	
	// 步骤5: 执行通知处理
	fmt.Println("步骤4: 执行通知处理...")
	err = notificationProcessor.ProcessNotifications(context.Background(), processedAlert, routeSpace)
	if err != nil {
		log.Printf("通知处理失败: %v", err)
		return
	}
	
	fmt.Println("✅ 完整的告警处理流程演示成功完成！")
	fmt.Println("\n流程总结:")
	fmt.Println("1. ✅ 原始告警数据解析和标准化")
	fmt.Println("2. ✅ 告警数据处理和标签提取")
	fmt.Println("3. ✅ 通知路由配置和渠道选择")
	fmt.Println("4. ✅ WPS协作通知模板渲染")
	fmt.Println("5. ✅ 通知发送（Mock模式）")
	
	// 打印处理后的告警数据（用于调试）
	alertJSON, _ := json.MarshalIndent(processedAlert, "", "  ")
	fmt.Printf("\n处理后的告警数据:\n%s\n", string(alertJSON))
}
