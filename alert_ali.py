import hashlib
import json
import time
import requests
from flask import Flask, request
from datetime import datetime,timedelta
import os
from urllib.parse import parse_qs
import threading
from collections import defaultdict
from dateutil import parser
import pytz
import subprocess
import re

app = Flask(__name__)

# 日志保存目录
LOG_DIR = "/data/tools/webhook/logs"

# 机甲
APPID = "AK20231216PDPGPI"
APPKEY = "deff6800d41d3b296eb41ce4a01094ef"

OPENAPI = "https://openapi.wps.cn"
PYTHON_PATH = os.path.dirname(os.path.abspath(__file__))    

# 在其他全局变量之后添加这些新的全局变量
# @消息收敛相关的全局变量
MESSAGE_THROTTLE_LOCK = threading.Lock()
MESSAGE_THROTTLE_CACHE = defaultdict(list)  # 用于缓存短时间内的@消息
MESSAGE_THROTTLE_LAST_SENT = {}  # 记录每个聊天组最后一次发送@消息的时间
MESSAGE_THROTTLE_WINDOW = 300  # 收敛时间窗口，单位秒（5分钟）

def parse_iso_datetime(iso_string, default='未知'):
    """使用dateutil解析ISO格式的时间字符串"""
    if iso_string == default:
        return default
    
    try:
        # 解析ISO时间字符串
        dt = parser.parse(iso_string)
        # 确保时区是UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=pytz.UTC)
        # 转换为东八区时间
        cst = pytz.timezone('Asia/Shanghai')
        dt_cst = dt.astimezone(cst)
        # 格式化为易读的字符串
        return dt_cst.strftime('%Y-%m-%d %H:%M:%S')
    except Exception:
        return default

def _sig(content_md5, url, date):
    """WPS-3 签名算法"""
    sha1 = hashlib.sha1(APPKEY.lower().encode('utf-8'))
    sha1.update(content_md5.encode('utf-8'))
    sha1.update(url.encode('utf-8'))
    sha1.update("application/json".encode('utf-8'))
    sha1.update(date.encode('utf-8'))
    return "WPS-3:{}:{}".format(APPID, sha1.hexdigest())

def xiezuo_request(method, host, uri, body=None, cookie=None, headers=None):
    """发送请求到协作平台"""
    requests.packages.urllib3.disable_warnings()
    if method in ["PUT", "POST", "DELETE"]:
        body = json.dumps(body)
        content_md5 = hashlib.md5(body.encode('utf-8')).hexdigest()
    else:
        content_md5 = hashlib.md5("".encode('utf-8')).hexdigest()

    date = time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime())
    header = {
        "Content-type": "application/json",
        'X-Auth': _sig(content_md5, uri, date),
        'Date': date,
        'Content-Md5': content_md5
    }
    
    if headers:
        header.update(headers)
        
    url = f"{host}{uri}"
    r = requests.request(method, url, data=body, headers=header, cookies=cookie, verify=False)
    return r.status_code, r.text

def get_company_token():
    """获取企业token"""
    url = f"/oauthapi/v3/inner/company/token?app_id={APPID}"
    status, rsp = xiezuo_request("GET", OPENAPI, url)
    rsp = json.loads(rsp)
    if 'company_token' in rsp:
        return rsp["company_token"]
    print("no company-token found in response, authorized failed")
    return None

def get_user(company_token, email_list, open_api_url):
    """获取用户ID列表"""
    company_uids = []
    for email in email_list:
        url = f"/oauthapi/v3/company/company_user/byemail?company_token={company_token}&email={email}"
        status, rsp = xiezuo_request("GET", open_api_url, url)
        rsp = json.loads(rsp)
        company_uid = rsp.get("company_uid")
        if company_uid:
            company_uids.append(company_uid)
    return company_uids

def get_company(company_token, open_api_url):
    """获取企业ID"""
    url = f"/plus/v1/company?company_token={company_token}"
    _, rsp = xiezuo_request("GET", open_api_url, url)
    rsp = json.loads(rsp)
    return rsp["company"]["company_id"]

def format_ali_alert_message(data):
    """格式化阿里云告警消息"""
    severity = data.get('severity', 'UNKNOWN')
    strategy_name = data.get('strategyName', '未知策略')
    alert = data.get('alert', {})
    meta = alert.get('meta', {}).get('sysEventMeta', {})
    event_content = alert.get('eventContentMap', {})
    
    # 告警颜色和图标映射
    severity_map = {
        'CRITICAL': ('red', '🚨'),
        'WARN': ('yellow', '⚠️'),
        'INFO': ('green', 'ℹ️'),
        'UNKNOWN': ('grey', '❓'),
        'OK': ('green', '✅')
    }
    color, icon = severity_map.get(severity, ('grey', '❓'))
    
    # 构建消息标题
    product = meta.get('serviceTypeZh', alert.get('product', '未知'))
    title = f"{icon} [阿里云{product}] {meta.get('eventNameZh', strategy_name)}"
    
    # 构建消息体
    message_body = {
        "app_key": APPID,
        "msg_type": 23,
        "content": {
            "type": 23,
            "content": {
                "header": {
                    "template": color,
                    "title": {
                        "tag": "text",
                        "content": {
                            "text": title,
                            "type": "plainText"
                        }
                    }
                },
                "elements": []
            }
        }
    }
    
    elements = message_body["content"]["content"]["elements"]
    
    # 添加基本信息
    elements.extend([
        {"tag": "hr", "style": "solid"},
        {
            "tag": "div",
            "fields": [
                {
                    "tag": "text",
                    "content": {
                        "text": (
                            f"**📊 事件概览**\n\n"
                            f"- 告警等级：{severity}\n"
                            f"- 事件状态：{event_content.get('eventStatus', alert.get('alertStatus', '未知'))}\n"
                            f"- 事件类型：{meta.get('eventType', alert.get('eventType', '未知'))}\n"
                            f"- 所属地域：{meta.get('regionNameZh', '未知')}\n"
                            f"- 业务分组：{alert.get('groupName', '未知')}\n"
                        ),
                        "type": "markdown"
                    }
                }
            ]
        }
    ])

    # 添加资源信息
    elements.extend([
        {"tag": "hr", "style": "dashed"},
        {
            "tag": "div",
            "fields": [
                {
                    "tag": "text",
                    "content": {
                        "text": (
                            f"**🖥️ 资源信息**\n\n"
                            f"- 实例名称：{meta.get('instanceName', '未知')}\n"
                            f"- 实例ID：{event_content.get('instanceId', '未知')}\n"
                            f"- 私有IP：{', '.join(event_content.get('privateIpAddress', ['无']))}\n"
                            f"- 公网IP：{', '.join(event_content.get('publicIpAddress', ['无']))}\n"
                            f"- 弹性IP：{event_content.get('eipAddress', '未知')}\n"
                        ),
                        "type": "markdown"
                    }
                }
            ]
        }
    ])

    # 添加事件详情
    elements.extend([
        {"tag": "hr", "style": "dashed"},
        {
            "tag": "div",
            "fields": [
                {
                    "tag": "text",
                    "content": {
                        "text": (
                            f"**🔍 事件详情**\n\n"
                            f"- 事件ID：{event_content.get('eventId', '未知')}\n"
                            f"- 原因：{event_content.get('reason', '未知')}\n"
                            f"- 事件代码：{event_content.get('reasonCode', '未知')}\n"
                        ),
                        "type": "markdown"
                    }
                }
            ]
        }
    ])

    # 添加时间信息
    elements.extend([
        {"tag": "hr", "style": "dashed"},
        {
            "tag": "div",
            "fields": [
                {
                    "tag": "text",
                    "content": {
                        "text": (
                            f"**⏰ 时间信息**\n\n"
                            f"- 发布时间：{parse_iso_datetime(event_content.get('publishTime', '未知'))}\n"
                            f"- 开始时间：{parse_iso_datetime(event_content.get('executeStartTime', '未知'))}\n"
                            f"- 结束时间：{parse_iso_datetime(event_content.get('executeFinishTime', '未知'))}\n"

                        ),
                        "type": "markdown"
                    }
                }
            ]
        }
    ])

    # 添加订阅信息
    subscription = data.get('subscription', {})
    if subscription:
        conditions = subscription.get('conditions', [])
        condition_text = "**🔔 订阅条件**\n\n"
        for cond in conditions:
            condition_text += f"- {cond.get('field', '')}: {cond.get('op', '')} {cond.get('value', '')}\n"
        
        elements.extend([
            {"tag": "hr", "style": "dashed"},
            {
                "tag": "text",
                "content": {
                    "text": condition_text,
                    "type": "markdown"
                }
            }
        ])

    return message_body

def send_at_message(OPENAPI, url, chat_list, email_list):
    """构建@消息体"""
    at_message = {
        "app_key": APPID,
        "to_chats": chat_list,
        "msg_type": 1,
        "content": {
            "type": 1,
            "body": " ".join([f"<at email=\"{email}\"></at>" for email in email_list])
        }
    }
    status, rsp = xiezuo_request("POST", OPENAPI, url, at_message)
    if status != 200:
        print(f"Failed to send @@@ message: {rsp}", 500)
    return "OK", 200

def send_at_message_with_throttling(OPENAPI, url, chat_list, email_list):
    """带有收敛功能的@消息发送函数"""
    current_time = time.time()
    
    with MESSAGE_THROTTLE_LOCK:
        # 为每个聊天组处理消息收敛
        for chat_id in chat_list:
            chat_key = str(chat_id)
            
            # 检查是否在冷却期内
            if chat_key in MESSAGE_THROTTLE_LAST_SENT:
                time_since_last = current_time - MESSAGE_THROTTLE_LAST_SENT[chat_key]
                if time_since_last < MESSAGE_THROTTLE_WINDOW:
                    # 在冷却期内，将消息添加到缓存
                    MESSAGE_THROTTLE_CACHE[chat_key].extend(email_list)
                    # 去重
                    MESSAGE_THROTTLE_CACHE[chat_key] = list(set(MESSAGE_THROTTLE_CACHE[chat_key]))
                    print(f"消息收敛: 聊天组 {chat_id} 的@消息已缓存，将在 {MESSAGE_THROTTLE_WINDOW - time_since_last:.1f} 秒后发送")
                    return "OK", 200
            
            # 不在冷却期内，发送当前消息和缓存的消息
            all_emails = list(set(email_list + MESSAGE_THROTTLE_CACHE.get(chat_key, [])))
            
            # 构建@消息体
            at_message = {
                "app_key": APPID,
                "to_chats": [chat_id],
                "msg_type": 1,
                "content": {
                    "type": 1,
                    "body": " ".join([f"<at email=\"{email}\"></at>" for email in all_emails])
                }
            }
            
            # 如果缓存中有消息，添加提示
            if len(MESSAGE_THROTTLE_CACHE.get(chat_key, [])) > 0:
                at_message["content"]["body"] = "【多条告警已合并】 " + at_message["content"]["body"]
            
            # 发送消息
            status, rsp = xiezuo_request("POST", OPENAPI, url, at_message)
            if status != 200:
                print(f"Failed to send @@@ message: {rsp}", 500)
                return f"Failed to send message: {rsp}", 500
            
            # 更新最后发送时间并清空缓存
            MESSAGE_THROTTLE_LAST_SENT[chat_key] = current_time
            MESSAGE_THROTTLE_CACHE[chat_key] = []
    
    return "OK", 200


def format_metric_message(alert_info):
    """格式化时序指标告警消息"""
    # 告警颜色和图标映射
    level_map = {
        'CRITICAL': ('red', '🚨'),
        'WARN': ('yellow', '⚠️'),
        'INFO': ('green', 'ℹ️'),
        'UNKNOWN': ('grey', '❓'),
        'OK': ('green', '✅')
    }
    color, icon = level_map.get(alert_info['triggerLevel'], ('grey', '❓'))
    
    # 格式化时间
    alert_time = datetime.fromtimestamp(alert_info['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
    
    # 解析维度信息
    dimensions = alert_info['dimensions'].strip('{}').split(',')
    dimension_dict = {}
    for dim in dimensions:
        if '=' in dim:
            key, value = dim.split('=')
            dimension_dict[key] = value

    # 构建消息体
    message_body = {
        "app_key": APPID,
        "msg_type": 23,
        "content": {
            "type": 23,
            "content": {
                "header": {
                    "template": color,
                    "title": {
                        "tag": "text",
                        "content": {
                            "text": f"{icon}[阿里云] {alert_info['alertName']}",
                            "type": "plainText"
                        }
                    }
                },
                "elements": [
                    {"tag": "hr", "style": "solid"},
                    {
                        "tag": "div",
                        "fields": [
                            {
                                "tag": "text",
                                "content": {
                                    "text": (
                                        f"**📊 告警概览**\n\n"
                                        f"- 指标名称：{alert_info['metricName']}\n"
                                        f"- 当前值：{alert_info['curValue']}\n"
                                        f"- 触发条件：{alert_info['expression']}\n"
                                        f"- 告警等级：{alert_info['triggerLevel']}\n"
                                        f"- 告警状态：{alert_info['alertState']}\n"
                                    ),
                                    "type": "markdown"
                                }
                            }
                        ]
                    },
                    {"tag": "hr", "style": "dashed"},
                    {
                        "tag": "div",
                        "fields": [
                            {
                                "tag": "text",
                                "content": {
                                    "text": (
                                        f"**🖥️ 资源信息**\n\n"
                                        f"- 实例名称：{alert_info['instanceName']}\n"
                                        f"- 地域：{alert_info['regionName']}\n"
                                        f"- 实例ID：{dimension_dict.get('instanceId', '未知')}\n"
                                        f"- 用户ID：{dimension_dict.get('userId', '未知')}\n"
                                    ),
                                    "type": "markdown"
                                }
                            }
                        ]
                    },
                    {"tag": "hr", "style": "dashed"},
                    {
                        "tag": "div",
                        "fields": [
                            {
                                "tag": "text",
                                "content": {
                                    "text": f"**⏰ 告警时间**：{alert_time}",
                                    "type": "markdown"
                                }
                            }
                        ]
                    }
                ]
            }
        }
    }
    
    return message_body

def handle_ddos_alerts(title, event_content):
    """
    处理DDoS相关告警，执行相应的命令
    
    @param title: 告警标题
    @param event_content: 事件内容
    @return: 处理结果信息
    """
    try:
        result_message = ""
        
        # 获取地域信息
        region_info = event_content.get('regionName', '')
        is_china_region = any(city in region_info for city in ['深圳', '上海', '北京', '成都'])
        
        # 根据地域设置命令参数
        if is_china_region:
            region = "cn-hangzhou"
            instance_id = "ddosorigin_cn-2ml4b8dam01"
            profile_arg = []  # 国内区域不需要profile参数
        else:
            region = "ap-southeast-1"
            instance_id = "ddosorigin_ntl-sg-e1n4b9swv01"
            profile_arg = ["--profile", "gp"]
        
        # 判断告警类型并执行相应操作
        if "实例遭受DDoS攻击中" in title:
            # 提取IP地址
            ip_addresses = []
            eip = event_content.get('eipAddress')
            
            # 合并所有IP
            if eip and eip != '未知':
                ip_addresses.append(eip)
            
            # 如果没有找到IP，记录错误
            if not ip_addresses:
                print(f"未能从事件内容中提取IP地址: {event_content}")
                return "未能提取IP地址，无法执行防护命令"
            
            # 执行AddIp命令
            for ip in ip_addresses:
                # 构建JSON格式的IP列表
                ip_list = f'[{{"ip":"{ip}"}}]'
                cmd = [
                    "/usr/local/bin/aliyun", "ddosbgp", "AddIp"
                ] + profile_arg + [
                    "--region", region,
                    "--RegionId", region,
                    "--InstanceId", instance_id,
                    "--IpList", ip_list
                ]
                
                print(f"执行命令: {' '.join(cmd)}")
                # 使用兼容Python 3.6的方式运行命令
                process = subprocess.Popen(
                    cmd, 
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )
                stdout, stderr = process.communicate()
                
                if process.returncode != 0:
                    error_msg = f"AddIp命令执行失败: {stderr}"
                    print(error_msg)
                    return error_msg
                
                success_msg = f"成功添加IP {ip} 到DDoS防护"
                print(success_msg)
                result_message += success_msg + "\n"
            
            return f"成功添加IP {', '.join(ip_addresses)} 到DDoS防护"
            
        elif "实例IP处于黑洞中" in title:
            # 提取IP地址
            eip = event_content.get('eipAddress')
            
            # IP
            if eip and eip != '未知':
                ip_addresses = eip
            
            # 如果仍然没有找到IP，记录错误
            if not ip_addresses:
                print(f"未能从事件内容或标题中提取IP地址: {title}, {event_content}")
                return "未能提取IP地址，无法执行解除黑洞命令"
            
            # 执行DeleteBlackhole命令
            cmd = [
                "/usr/local/bin/aliyun", "ddosbgp", "DeleteBlackhole"
            ] + profile_arg + [
                "--region", region,
                "--RegionId", region,
                "--Ip", ip_addresses,
                "--InstanceId", instance_id
            ]
            
            print(f"执行命令: {' '.join(cmd)}")
            # result = subprocess.run(cmd, capture_output=True, text=True)
            # 使用兼容Python 3.6的方式运行命令
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            stdout, stderr = process.communicate()
            
            if process.returncode != 0:
                error_msg = f"DeleteBlackhole命令执行失败: {stderr}"
                print(error_msg)
                return error_msg
            
            success_msg = f"成功删除IP {ip} 的黑洞状态"
            print(success_msg)
            result_message += success_msg + "\n"
            
            return f"成功删除IP {', '.join(ip_addresses)} 的黑洞状态"
        
        return "非DDoS相关告警，无需处理"
    
    except Exception as e:
        error_msg = f"处理DDoS告警时发生错误: {str(e)}"
        print(error_msg)
        return error_msg

def send_ddos_operation_notification(operation_result, company_token):
    """
    发送DDoS操作结果通知
    
    @param operation_result: 操作结果
    @param company_token: 企业token
    @return: 发送结果
    """
    try:
        # 构建消息体
        message_body = {
            "app_key": APPID,
            "msg_type": 23,
            "content": {
                "type": 23,
                "content": {
                    "header": {
                        "template": "blue",
                        "title": {
                            "tag": "text",
                            "content": {
                                "text": "🛡️ DDoS防护操作通知",
                                "type": "plainText"
                            }
                        }
                    },
                    "elements": [
                        {"tag": "hr", "style": "solid"},
                        {
                            "tag": "div",
                            "fields": [
                                {
                                    "tag": "text",
                                    "content": {
                                        "text": (
                                            f"**📊 操作结果**\n\n"
                                            f"{operation_result}\n\n"
                                            f"**⏰ 操作时间**\n\n"
                                            f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                                        ),
                                        "type": "markdown"
                                    }
                                }
                            ]
                        }
                    ]
                }
            }
        }
        
        # 发送消息
        email_list = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]
        if email_list:
            url = f'/kopen/woa/v2/dev/app/messages?company_token={company_token}'
            company_uids = get_user(company_token, email_list, OPENAPI)
            if not company_uids:
                return f"No valid users found for emails: {email_list}", 500
            
            company_id = get_company(company_token, OPENAPI)
            message_body["to_users"] = {"company_id": company_id, "company_uids": company_uids}
            message_body["to_chats"] = [41887203] 
            status, rsp = xiezuo_request("POST", OPENAPI, url, message_body)
            print(f"Send operation notification response: status={status}, response={rsp}")
            if status != 200:
                return f"Failed to send operation notification: {rsp}", 500
        
        return "OK", 200
    
    except Exception as e:
        error_msg = f"发送DDoS操作通知时发生错误: {str(e)}"
        print(error_msg)
        return error_msg, 500


@app.route('/ali_event', methods=['POST'])
def ali_event():
    """处理阿里云告警消息"""
    try:
        # 1. 获取和验证请求数据
        try:
            data = request.get_json(force=True)
            # 添加请求数据的日志记录
            print(f"Received data: {json.dumps(data, ensure_ascii=False)}")
            
            if not data:
                return "Empty request body", 400
        except Exception as e:
            print(f"JSON parse error: {str(e)}")
            return f"Failed to parse request JSON: {str(e)}", 400

        # 2. 记录日志
        try:
            log_dir = LOG_DIR
            os.makedirs(log_dir, exist_ok=True)
            log_file = os.path.join(log_dir, f"ali_event_{datetime.now().strftime('%Y%m%d')}.log")
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} {json.dumps(data, ensure_ascii=False)}\n")
        except Exception as e:
            print(f"Log write error: {str(e)}")

        # 3. 获取金山协作 token
        try:
            company_token = get_company_token()
            if not company_token:
                print("Failed to get company token")
                return "Failed to get company token", 500
        except Exception as e:
            print(f"Token get error: {str(e)}")
            return f"Error getting company token: {str(e)}", 500

        # 4. 构建消息体
        try:
            message_body = format_ali_alert_message(data)
            print(f"Formatted message: {json.dumps(message_body, ensure_ascii=False)}")
        except Exception as e:
            print(f"Message format error: {str(e)}")
            return f"Error formatting message: {str(e)}", 500

        # 发送消息到指定用户
        email_list = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]
        if email_list:
            try:
                url = f'/kopen/woa/v2/dev/app/messages?company_token={company_token}'
                company_uids = get_user(company_token, email_list, OPENAPI)
                if not company_uids:
                    print(f"No valid users found for emails: {email_list}")
                    return f"No valid users found for emails: {email_list}", 500
                
                company_id = get_company(company_token, OPENAPI)
                message_body["to_users"] = {"company_id": company_id, "company_uids": company_uids}
                message_body["to_chats"] = [41887203] 
                status, rsp = xiezuo_request("POST", OPENAPI, url, message_body)
                print(f"Send message response: status={status}, response={rsp}")
                if status != 200:
                    return f"Failed to send message: {rsp}", 500
            except Exception as e:
                print(f"Message send error: {str(e)}")
                return f"Error sending message: {str(e)}", 500

        # return "OK", 200

        # 5. 处理DDoS相关告警
        try:
            # 获取标题和事件内容
            title = message_body["content"]["content"]["header"]["title"]["content"]["text"]
            event_content = data.get('alert', {}).get('eventContentMap', {})
            
            # 判断是否为DDoS相关告警
            if "实例遭受DDoS攻击中" in title or "实例IP处于黑洞中" in title:
                # 处理DDoS告警
                operation_result = handle_ddos_alerts(title, event_content)
                
                # 发送操作结果通知
                send_ddos_operation_notification(operation_result, company_token)
        except Exception as e:
            print(f"DDoS handling error: {str(e)}")
            # 继续处理，不影响主流程

        return "OK", 200


    except Exception as e:
        error_msg = f"Error processing ali alert: {str(e)}"
        print(error_msg)  # 打印错误信息到控制台
        return error_msg, 500

@app.route('/ali_metric', methods=['POST'])
def ali_metric():
    """处理阿里云时序指标告警"""
    try:
        # 1. 解析表单数据
        raw_data = request.get_data().decode('utf-8')
        params = parse_qs(raw_data)
        
        # 2. 提取关键参数
        alert_info = {
            'alertName': params.get('alertName', ['未知告警'])[0],
            'metricName': params.get('metricName', ['未知指标'])[0],
            'curValue': params.get('curValue', ['0'])[0],
            'triggerLevel': params.get('triggerLevel', ['UNKNOWN'])[0],
            'alertState': params.get('alertState', ['UNKNOWN'])[0],
            'instanceName': params.get('instanceName', ['未知实例'])[0],
            'regionName': params.get('regionName', ['未知地域'])[0],
            'timestamp': int(params.get('timestamp', ['0'])[0]) / 1000,
            'dimensions': params.get('dimensions', ['{}'])[0],
            'expression': params.get('expression', ['未知条件'])[0]
        }
        
        # 排除未知告警（事件）
        if alert_info['alertName'] == '未知告警':
            return "OK", 200
        
        # 2. 记录日志
        try:
            log_dir = LOG_DIR
            os.makedirs(log_dir, exist_ok=True)
            log_file = os.path.join(log_dir, f"ali_metric_{datetime.now().strftime('%Y%m%d')}.log")
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} raw_data: {raw_data}\n")
        except Exception as e:
            print(f"Log write error: {str(e)}")

        # 3. 获取金山协作 token
        company_token = get_company_token()
        if not company_token:
            return "Failed to get company token", 500

        # 4. 构建消息体
        message_body = format_metric_message(alert_info)

        # 5. 发送消息
        email_list = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]
        if email_list:
            url = f'/kopen/woa/v2/dev/app/messages?company_token={company_token}'
            company_uids = get_user(company_token, email_list, OPENAPI)
            if not company_uids:
                return f"No valid users found for emails: {email_list}", 500
            
            company_id = get_company(company_token, OPENAPI)
            message_body["to_users"] = {"company_id": company_id, "company_uids": company_uids}
            
            status, rsp = xiezuo_request("POST", OPENAPI, url, message_body)
            if status != 200:
                return f"Failed to send message: {rsp}", 500

        return "OK", 200

    except Exception as e:
        error_msg = f"Error processing metric alert: {str(e)}"
        print(error_msg)
        return error_msg, 500

@app.route('/ali_metric_dba', methods=['POST'])
def ali_metric_dba():
    try:
        # 1. 解析表单数据
        raw_data = request.get_data().decode('utf-8')
        params = parse_qs(raw_data)
        
        # 2. 提取关键参数
        alert_info = {
            'alertName': params.get('alertName', ['未知告警'])[0],
            'metricName': params.get('metricName', ['未知指标'])[0],
            'curValue': params.get('curValue', ['0'])[0],
            'triggerLevel': params.get('triggerLevel', ['UNKNOWN'])[0],
            'alertState': params.get('alertState', ['UNKNOWN'])[0],
            'instanceName': params.get('instanceName', ['未知实例'])[0],
            'regionName': params.get('regionName', ['未知地域'])[0],
            'timestamp': int(params.get('timestamp', ['0'])[0]) / 1000,
            'dimensions': params.get('dimensions', ['{}'])[0],
            'expression': params.get('expression', ['未知条件'])[0]
        }
        
        # 排除未知告警（事件）
        if alert_info['alertName'] == '未知告警':
            return "OK", 200
        
        # 2. 记录日志
        try:
            log_dir = LOG_DIR
            os.makedirs(log_dir, exist_ok=True)
            log_file = os.path.join(log_dir, f"ali_metric_dba_{datetime.now().strftime('%Y%m%d')}.log")
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} raw_data: {raw_data}\n")
        except Exception as e:
            print(f"Log write error: {str(e)}")

        # 3. 获取金山协作 token
        company_token = get_company_token()
        if not company_token:
            return "Failed to get company token", 500

        # 4. 构建消息体
        message_body = format_metric_message(alert_info)

        # 5. 发送消息
        chat_list=[41887203]
        email_list=["<EMAIL>","<EMAIL>","<EMAIL>"]
        if chat_list:
            url = f'/kopen/woa/v2/dev/app/messages?company_token={company_token}'
            company_uids = get_user(company_token, email_list, OPENAPI)
            if not company_uids:
                return f"No valid users found for emails: {email_list}", 500
            
            # company_id = get_company(company_token, OPENAPI)
            message_body["to_chats"] = chat_list
            message_body['content']['content']['elements'].extend(
                [
                    {"tag": "hr", "style": "dashed"},
                    {
                        "content": {
                            "text": "🙎关联人员：<at email=\"<EMAIL>\"></at> <at email=\"<EMAIL>\"></at> <at email=\"<EMAIL>\"></at>",
                            "type": "markdown"
                        },
                        "tag": "text"
                    }
                ]
            )
            
            status, rsp = xiezuo_request("POST", OPENAPI, url, message_body)
            if status != 200:
                return f"Failed to send message: {rsp}", 500
            # 发送@消息
            #send_at_message(OPENAPI, url, chat_list, email_list)
            send_at_message_with_throttling(OPENAPI, url, chat_list, email_list)
        return "OK", 200

    except Exception as e:
        error_msg = f"Error processing metric alert: {str(e)}"
        print(error_msg)
        return error_msg, 500
    
# @app.route('/ali_event_dba', methods=['POST'])
# def ali_metric_dba():

@app.route('/ali_metric_network', methods=['POST'])
def ali_metric_network():
    try:
        # 1. 解析表单数据
        raw_data = request.get_data().decode('utf-8')
        params = parse_qs(raw_data)
        
        # 2. 提取关键参数
        alert_info = {
            'alertName': params.get('alertName', ['未知告警'])[0],
            'metricName': params.get('metricName', ['未知指标'])[0],
            'curValue': params.get('curValue', ['0'])[0],
            'triggerLevel': params.get('triggerLevel', ['UNKNOWN'])[0],
            'alertState': params.get('alertState', ['UNKNOWN'])[0],
            'instanceName': params.get('instanceName', ['未知实例'])[0],
            'regionName': params.get('regionName', ['未知地域'])[0],
            'timestamp': int(params.get('timestamp', ['0'])[0]) / 1000,
            'dimensions': params.get('dimensions', ['{}'])[0],
            'expression': params.get('expression', ['未知条件'])[0]
        }
        
        # 排除未知告警（事件）
        if alert_info['alertName'] == '未知告警':
            return "OK", 200
        
        # 2. 记录日志
        try:
            log_dir = LOG_DIR
            os.makedirs(log_dir, exist_ok=True)
            log_file = os.path.join(log_dir, f"ali_metric_network_{datetime.now().strftime('%Y%m%d')}.log")
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} raw_data: {raw_data}\n")
        except Exception as e:
            print(f"Log write error: {str(e)}")

        # 3. 获取金山协作 token
        company_token = get_company_token()
        if not company_token:
            return "Failed to get company token", 500

        # 4. 构建消息体
        message_body = format_metric_message(alert_info)

        # 5. 发送消息
        chat_list=[41887203]
        email_list=["<EMAIL>"]
        if chat_list:
            url = f'/kopen/woa/v2/dev/app/messages?company_token={company_token}'
            company_uids = get_user(company_token, email_list, OPENAPI)
            if not company_uids:
                return f"No valid users found for emails: {email_list}", 500
            
            # company_id = get_company(company_token, OPENAPI)
            message_body["to_chats"] = chat_list
            message_body['content']['content']['elements'].extend(
                [
                    {"tag": "hr", "style": "dashed"},
                    {
                        "content": {
                            "text": "🙎关联人员：<at email=\"<EMAIL>\"></at>",
                            "type": "markdown"
                        },
                        "tag": "text"
                    }
                ]
            )          

            status, rsp = xiezuo_request("POST", OPENAPI, url, message_body)
            if status != 200:
                return f"Failed to send message: {rsp}", 500
            # 发送@消息
            send_at_message(OPENAPI, url,chat_list, email_list)
        return "OK", 200

    except Exception as e:
        error_msg = f"Error processing metric alert: {str(e)}"
        print(error_msg)
        return error_msg, 500
# @app.route('/ali_event_network', methods=['POST'])
# def ali_metric_network():


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=2224, debug=False)
